import {FC} from "react";
import MatchFinishedPage from "@/src/client/Gaming/infrastructure/pages/MatchFinishedPage/MatchFinishedPage";

type Props = {
  params: Promise<{ locale: string; matchId: string }>;
};

const MatchFinishedPageContainer: FC<Props> = async ({params}) => {
  const {matchId} = await params;

  return (
    <MatchFinishedPage matchId={matchId}/>
  );
};

export default MatchFinishedPageContainer;