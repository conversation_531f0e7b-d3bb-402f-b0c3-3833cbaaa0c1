import {FC} from "react";
import MatchMulliganPage from "@/src/client/Gaming/infrastructure/pages/MatchMulliganPage/MatchMulliganPage";

type Props = {
  params: Promise<{ locale: string; matchId: string }>;
};

const MatchMulliganPageContainer: FC<Props> = async ({params}) => {
  const {matchId} = await params;

  return (
    <MatchMulliganPage matchId={matchId}/>
  );
};

export default MatchMulliganPageContainer;