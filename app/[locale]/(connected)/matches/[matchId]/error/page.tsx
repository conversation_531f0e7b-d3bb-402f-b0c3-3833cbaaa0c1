import {FC} from "react";
import MatchErrorPage from "@/src/client/Gaming/infrastructure/pages/MatchErrorPage/MatchErrorPage";

type Props = {
  params: Promise<{ locale: string; matchId: string }>;
  searchParams: Promise<{ error?: string }>;
};

const MatchErrorPageContainer: FC<Props> = async ({params, searchParams}) => {
  const {matchId} = await params;
  const {error} = await searchParams;

  return (
    <MatchErrorPage matchId={matchId} errorMessage={error || "Unknown error occurred"}/>
  );
};

export default MatchErrorPageContainer;