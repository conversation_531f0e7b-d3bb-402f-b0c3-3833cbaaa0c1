import {FC} from "react";
import {fetchQuery} from "convex/nextjs";
import {api} from "@/convex/_generated/api";
import {Id} from "@/convex/_generated/dataModel";
import {convexAuthNextjsToken} from "@convex-dev/auth/nextjs/server";
import DeckList from "@/src/client/DeckBuilding/infrastructure/components/DeckList/DeckList";

type Props = { params: Promise<{ locale: string; gameId: string }> };

const DecksPage: FC<Props> = async ({params}) => {
  const {locale, gameId} = await params;

  const {data} = await fetchQuery(
    api.queries.deck.loadDecksByUserIdAndGameId,
    {gameId: gameId as Id<'games'>, locale},
    {token: await convexAuthNextjsToken()}
  );
  const decks = data?.decks ?? [];

  return (
    <DeckList decks={decks} locale={locale} gameId={gameId}/>
  );
};

export default DecksPage;