import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";

export const endpoint = protectedMutation({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchRepository(ctx);
    const match = await repository.findById(matchId as string);
    
    if (!match) {
      throw new Error('Match not found');
    }
    
    if (match.getStatus() === 'active' && !match.getCurrentTurn()) {
      const players = match.getPlayers();
      if (players.length >= 2) {
        match.setCurrentTurn(players[0]);
        await repository.save(match);
      }
    }
  },
});