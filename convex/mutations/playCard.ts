import {v} from "convex/values";
import {protectedMutation} from "@/convex/helpers";
import {PlayCardCommandHandler} from "@/src/server/Gaming/application/commands/Match/PlayCard/PlayCardCommandHandler";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";

export const endpoint = protectedMutation({
  args: {
    matchId: v.id("matches"),
    cardId: v.string(),
    rowType: v.union(v.literal("first"), v.literal("second")),
    slotIndex: v.number(),
  },
  handler: async (ctx, {matchId, cardId, rowType, slotIndex}) => {
    const repository = new ConvexMatchRepository(ctx);
    const handler = new PlayCardCommandHandler(repository);

    await handler.handle({
      matchId: matchId as string,
      cardId,
      rowType,
      slotIndex,
      userId: ctx.userId
    });
  },
});
