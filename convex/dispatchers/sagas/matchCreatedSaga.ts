import {MutationCtx} from "../../_generated/server";
import {Id} from "../../_generated/dataModel";
import {
  CleanUpMatchMakingQueueCommandHandler
} from "@/src/server/MatchMaking/application/commands/MatchMaking/CleanUpMatchMakingQueue/CleanUpMatchMakingQueueCommandHandler";
import {
  UpdatePlayersStatusCommandHandler
} from "@/src/server/MatchMaking/application/commands/MatchMaking/UpdatePlayersStatus/UpdatePlayersStatusCommandHandler";
import {ConvexMatchRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchRepository";
import {
  ConvexMatchmakingQueueRepository
} from "@/src/server/MatchMaking/infrastructure/repositories/MatchmakingQueue/ConvexMatchmakingQueueRepository";
import {
  ConvexAppUserRepository
} from "@/src/server/Authentication/infrastructure/repositories/AppUser/ConvexAppUserRepository";
import {ConvexEventBus} from "@/src/server/Shared/infrastructure/gateways/Context/ConvexEventBus";
import {CreateGameDeckCommandHandler} from "@/src/server/Gaming/application/commands/GameDeck/CreateGameDeck/CreateGameDeckCommandHandler";
import {ConvexDeckRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/Deck/ConvexDeckRepository";
import {ConvexCatalogCardListRepository} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {ConvexGameDeckRepository} from "@/src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckRepository";

export async function matchCreatedSaga(
  ctx: MutationCtx,
  payload: {
    matchId: string;
    players: string[];
  }
) {
  const eventBus = new ConvexEventBus(ctx);
  const cleanUpMatchMakingQueueCommandHandler = new CleanUpMatchMakingQueueCommandHandler(
    eventBus,
    new ConvexMatchRepository(ctx),
    new ConvexMatchmakingQueueRepository(ctx)
  );
  const updatePlayersStatusCommandHandler = new UpdatePlayersStatusCommandHandler(
    new ConvexAppUserRepository(ctx)
  );

  const matchRepo = new ConvexMatchRepository(ctx);
  const queueRepo = new ConvexMatchmakingQueueRepository(ctx);
  const match = await matchRepo.findById(payload.matchId as string);
  if (match) {
    const queue = await queueRepo.findByGameId(match.getGameId());
    const items = queue.findItemsForPlayers(payload.players);
    const handler = new CreateGameDeckCommandHandler(
      new ConvexDeckRepository(ctx),
      new ConvexCatalogCardListRepository(ctx),
      new ConvexGameDeckRepository(ctx),
    );
    const created = await Promise.all(items.map(async item => {
      const snapshot = item.toSnapshot();
      const gameDeckId = await handler.handle({ deckId: snapshot.deckId });
      return { playerId: snapshot.playerId, gameDeckId: gameDeckId as Id<'gameDecks'> };
    }));

    await ctx.db.patch(payload.matchId as Id<'matches'>, { playerGameDecks: created });
  }

  await Promise.all([
    cleanUpMatchMakingQueueCommandHandler.handle({matchId: payload.matchId as string, players: payload.players}),
    updatePlayersStatusCommandHandler.handle({players: payload.players, status: "playing"}),
  ]);
}
