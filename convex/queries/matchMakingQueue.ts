import {v} from "convex/values";
import {protectedQuery} from "@/convex/helpers";
import {Id} from "@/convex/_generated/dataModel";

export const getPlayerQueueStatus = protectedQuery({
  args: {
    gameId: v.string(),
  },
  handler: async (ctx, {gameId}) => {
    const queueItem = await ctx.db
      .query('matchmakingQueue')
      .withIndex('by_gameId', q => q.eq('gameId', gameId as Id<'games'>))
      .filter(q => q.eq(q.field('playerId'), ctx.userId))
      .first();

    const matches = await ctx.db
      .query('matches')
      .filter(q => 
        q.and(
          q.eq(q.field('gameId'), gameId as Id<'games'>),
          q.or(
            q.eq(q.field('status'), 'setup'),
            q.eq(q.field('status'), 'waiting_for_mulligan'),
            q.eq(q.field('status'), 'active'),
            q.eq(q.field('status'), 'playing')
          )
        )
      )
      .collect();
    
    const match = matches.find(m => m.players.includes(ctx.userId));

    return {
      isInQueue: !!queueItem,
      queueItem: queueItem ? {
        id: queueItem._id,
        gameId: queueItem.gameId,
        deckId: queueItem.deckId,
        queuedAt: queueItem.queuedAt
      } : null,
      match: match?._id || null
    };
  },
});