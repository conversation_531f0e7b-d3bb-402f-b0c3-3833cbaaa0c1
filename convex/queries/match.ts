import {LoadMatchByIdWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchByIdWebPresenter";
import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {
  ConvexMatchReadRepository
} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchReadRepository";
import {
  ConvexGameSettingsRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameSettings/ConvexGameSettingsRepository";
import {
  ConvexMulliganSelectionReadRepository
} from "@/src/server/Gaming/infrastructure/repositories/MulliganSelection/ConvexMulliganSelectionReadRepository";
import {
  LoadMatchByIdQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQueryHandler";
import {
  ConvexGameDeckReadRepository
} from "@/src/server/Gaming/infrastructure/repositories/GameDeck/ConvexGameDeckReadRepository";
import {LoadMatchDataWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchDataWebPresenter";
import {
  ConvexCatalogCardListRepository
} from "@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/ConvexCatalogCardListRepository";
import {
  LoadMatchDataQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchData/LoadMatchDataQueryHandler";
import {
  ConvexMatchEventListRepository
} from "@/src/server/Gaming/infrastructure/repositories/MatchEventList/ConvexMatchEventListRepository";
import {
  LoadMatchEventsQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchEvents/LoadMatchEventsQueryHandler";
import {LoadMatchEventsWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadMatchEventsWebPresenter";
import {
  ConvexMatchEndedEventRepository
} from "@/src/server/Gaming/infrastructure/repositories/MatchEndedEvent/ConvexMatchEndedEventRepository";
import {
  LoadMatchEndedEventQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchEndedEvent/LoadMatchEndedEventQueryHandler";
import {
  LoadMatchEndedEventWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadMatchEndedEventWebPresenter";
import {
  LoadMatchCreatedEventQueryHandler
} from "@/src/server/Gaming/application/queries/LoadMatchCreatedEvent/LoadMatchCreatedEventQueryHandler";
import {
  ConvexMatchMakingEventRepository
} from "@/src/server/Gaming/infrastructure/repositories/MatchMakingEvent/ConvexMatchMakingEventRepository";
import {
  LoadMatchCreatedEventWebPresenter
} from "@/src/server/Gaming/presentation/presenters/LoadMatchCreatedEventWebPresenter";

export const getMatchMeta = protectedQuery({
  args: {
    matchId: v.string(),
  },
  handler: async (ctx, { matchId }) => {
    if (!ctx.userId) {
      return { error: 'User not authenticated', data: null };
    }

    const appUser = await ctx.db
      .query('appUsers')
      .withIndex('by_appUserId', q => q.eq('appUserId', ctx.userId))
      .first();

    if (!appUser) {
      return { error: 'User not found', data: null };
    }

    const repository = new ConvexMatchReadRepository(ctx);
    const match = await repository.findById(matchId);

    if (!match) {
      return { error: 'Match not found', data: null };
    }

    const players = match.getPlayers();
    const currentPlayerPosition = players[0] === appUser.appUserId ? 'player1' : 'player2' as 'player1' | 'player2';

    return {
      error: null,
      data: {
        id: match.getId(),
        status: match.getStatus(),
        players,
        currentPlayerPosition,
        currentTurn: match.getCurrentTurn(),
        isWinner: match.getWinner() === appUser.appUserId,
      }
    };
  }
});

export const loadMatchById = protectedQuery({
  args: {
    matchId: v.string(),
  },
  handler: async (ctx, {matchId}) => {
    console.log('ctx.userId', ctx.userId)
    if (!ctx.userId) {
      return {error: 'User not authenticated', data: null};
    }
    
    const appUser = await ctx.db
      .query('appUsers')
      .withIndex('by_appUserId', q => q.eq('appUserId', ctx.userId))
      .first();
    
    if (!appUser) {
      return {error: 'User not found', data: null};
    }
    
    const repository = new ConvexMatchReadRepository(ctx);
    const gameSettingsRepo = new ConvexGameSettingsRepository(ctx);
    const mulliganRepo = new ConvexMulliganSelectionReadRepository(ctx);
    const handler = new LoadMatchByIdQueryHandler(repository, gameSettingsRepo, mulliganRepo);
    const presenter = new LoadMatchByIdWebPresenter();

    await handler.handle({matchId, userId: appUser.appUserId}, presenter);

    return presenter.getViewModel();
  },
});

export const loadMatchData = protectedQuery({
  args: { matchId: v.string() },
  handler: async (ctx, { matchId }) => {
    const matchRepo = new ConvexMatchReadRepository(ctx);
    const deckRepo = new ConvexGameDeckReadRepository(ctx);
    const catalogRepo = new ConvexCatalogCardListRepository(ctx);
    const handler = new LoadMatchDataQueryHandler(matchRepo, deckRepo, catalogRepo);
    const presenter = new LoadMatchDataWebPresenter();

    await handler.handle({ matchId, userId: ctx.userId }, presenter);
    return presenter.getViewModel();
  }
});

export const matchEvents = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchEventListRepository(ctx);
    const handler = new LoadMatchEventsQueryHandler(repository);
    const presenter = new LoadMatchEventsWebPresenter();

    await handler.handle({matchId}, presenter);

    return presenter.getViewModel();
  },
});

export const onMatchCreated = protectedQuery({
  args: {
    gameId: v.id("games"),
  },
  handler: async (ctx, { gameId }) => {
    const queryHandler = new LoadMatchCreatedEventQueryHandler(
      new ConvexMatchMakingEventRepository(ctx),
      new ConvexMatchReadRepository(ctx)
    );
    const presenter = new LoadMatchCreatedEventWebPresenter();

    await queryHandler.handle({gameId, userId: ctx.userId}, presenter);

    return presenter.getViewModel();
  },
});

export const onMatchEnded = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const repository = new ConvexMatchEndedEventRepository(ctx);
    const queryHandler = new LoadMatchEndedEventQueryHandler(repository);
    const presenter = new LoadMatchEndedEventWebPresenter();

    await queryHandler.handle({matchId}, presenter);

    return presenter.getViewModel();
  },
});

export const getPlayerActiveMatch = protectedQuery({
  args: {},
  handler: async (ctx) => {
    if (!ctx.userId) {
      return {hasActiveMatch: false, matchId: null, gameId: null};
    }
    
    const appUser = await ctx.db
      .query('appUsers')
      .withIndex('by_convexUserId', q => q.eq('convexUserId', ctx.userId))
      .first();
    
    if (!appUser) {
      return {hasActiveMatch: false, matchId: null, gameId: null};
    }
    
    const matches = await ctx.db
      .query('matches')
      .filter(q =>
        q.or(
          q.eq(q.field('status'), 'setup'),
          q.eq(q.field('status'), 'waiting_for_mulligan'),
          q.eq(q.field('status'), 'active'),
          q.eq(q.field('status'), 'playing')
        )
      )
      .collect();

    const activeMatch = matches.find(match =>
      match.players.includes(appUser.appUserId)
    );

    if (!activeMatch) {
      return {hasActiveMatch: false, matchId: null, gameId: null};
    }

    return {
      hasActiveMatch: true,
      matchId: activeMatch._id,
      gameId: activeMatch.gameId,
      status: activeMatch.status
    };
  },
});
