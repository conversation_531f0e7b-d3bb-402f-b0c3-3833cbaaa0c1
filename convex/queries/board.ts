import {protectedQuery} from "@/convex/helpers";
import {v} from "convex/values";
import {LoadBoardStateQueryHandler} from "@/src/server/Gaming/application/queries/LoadBoardState/LoadBoardStateQueryHandler";
import {ConvexBoardReadRepository} from "@/src/server/Gaming/infrastructure/repositories/Board/ConvexBoardReadRepository";
import {ConvexMatchReadRepository} from "@/src/server/Gaming/infrastructure/repositories/Match/ConvexMatchReadRepository";
import {ConvexBoardEnhancementService} from "@/src/server/Gaming/infrastructure/services/ConvexBoardEnhancementService";
import {LoadBoardStateWebPresenter} from "@/src/server/Gaming/presentation/presenters/LoadBoardStateWebPresenter";

export const getBoardState = protectedQuery({
  args: {
    matchId: v.id("matches"),
  },
  handler: async (ctx, {matchId}) => {
    const boardRepository = new ConvexBoardReadRepository(ctx);
    const matchRepository = new ConvexMatchReadRepository(ctx);
    const enhancementService = new ConvexBoardEnhancementService(ctx);
    const presenter = new LoadBoardStateWebPresenter();
    
    const queryHandler = new LoadBoardStateQueryHandler(
      boardRepository, 
      matchRepository, 
      enhancementService
    );

    await queryHandler.handle({matchId}, presenter);
    return presenter.getViewModel();
  },
});
