module.exports = {
    extends: [
        'next/core-web-vitals',
        'next',
        'plugin:@typescript-eslint/recommended',
        'plugin:@vitest/legacy-recommended'
    ],
    plugins: [
        'boundaries',
        'clean-architecture',
        '@typescript-eslint',
        'custom-rules',
    ],
    rules: {
        'custom-rules/describe-naming-convention': 'error',
        'custom-rules/it-should-start-with-should': 'error',
        'custom-rules/require-arrange-act-assert-comments': 'error',
        'custom-rules/no-duplicate-arrange-act-assert-comments': 'error',
        'custom-rules/no-text-after-arrange-act-assert': 'error',
        'custom-rules/no-arrange-act-assert-in-setup': 'error',
        'custom-rules/no-vi-mock': 'error',
        '@typescript-eslint/parameter-properties': 'error',
        '@typescript-eslint/ban-ts-comment': ['error', {
            'ts-expect-error': 'allow-with-description',
            'ts-ignore': 'allow-with-description'
        }],
        '@typescript-eslint/explicit-member-accessibility': [
            'error',
            {
                accessibility: 'no-public',
                overrides: {
                    constructors: 'no-public',
                },
            },
        ],
        '@typescript-eslint/no-unused-vars': [
            'error',
            {
                args: 'after-used',
                argsIgnorePattern: '^_',
                vars: 'all',
                varsIgnorePattern: '^_',
                ignoreRestSiblings: false,
            },
        ],
        'boundaries/element-types': [
            'error',
            {
                default: 'allow',
                rules: [
                    {
                        from: 'server-domain',
                        disallow: [
                            'server-application',
                            'server-infrastructure',
                            'server-presentation',
                        ],
                    },
                    {
                        from: 'server-application',
                        disallow: ['server-infrastructure', 'server-presentation'],
                    },
                    {
                        from: 'client-domain',
                        disallow: [
                            'client-application',
                            'client-infrastructure',
                            'client-presentation',
                        ],
                    },
                    {
                        from: 'client-application',
                        disallow: ['client-infrastructure', 'client-presentation'],
                    },
                ],
            },
        ],
        'boundaries/external': [
            'error',
            {
                default: 'allow',
                rules: [
                    {
                        from: 'server-domain',
                        disallow: ['*'],
                    },
                ],
            },
        ],
        "@vitest/max-nested-describe": [
            "error",
            {
                "max": 5
            }
        ],
        "@vitest/valid-title": "off",
        "@vitest/no-conditional-expect": "error",
        "@vitest/no-conditional-in-test": "error",
        "@vitest/no-conditional-tests": "error",
        "@vitest/no-disabled-tests": "error",
        "@vitest/no-focused-tests": "error",
        "@vitest/no-importing-vitest-globals": "error",
    },
    settings: {
        'boundaries/elements': [
            {type: 'server-domain', pattern: 'src/server/domain/**'},
            {type: 'server-application', pattern: 'src/server/application/**'},
            {type: 'server-infrastructure', pattern: 'src/server/infrastructure/**'},
            {type: 'server-presentation', pattern: 'src/server/presentation/**'},
            {type: 'client-domain', pattern: 'src/client/domain/**'},
            {type: 'client-application', pattern: 'src/client/application/**'},
            {type: 'client-infrastructure', pattern: 'src/client/infrastructure/**'},
            {type: 'client-presentation', pattern: 'src/client/presentation/**'},
        ],
    },
};
