Read the coding conventions and architectural guidelines from the CLAUDE.md file in the repository root. 
Then refactor the code that was just created in the previous task to ensure it follows all the established conventions, including:

1. **Architecture patterns**: Clean Architecture + DDD + CQRS + TDD structure
2. **File naming conventions**: Proper naming for commands, queries, handlers, repositories, presenters, and tests
3. **Testing patterns**: Vitest with Arrange/Act/Assert structure, sociable tests, and proper test organization
4. **Layer separation**: Respect bounded context boundaries and ESLint boundary rules
5. **Code organization**: Proper placement of files within the layered architecture
6. **Code quality**: Apply SOLID principles, extract until drop, use full descriptive names, focus on behavior, justify every line, encapsulate dependencies, and avoid comments in production code and tests (except for Arrange/Act/Assert)

After refactoring, run `npm run lint` and `npm test` to ensure all conventions are properly applied and no violations remain. 
Fix any linting errors or test failures that arise from the refactoring.