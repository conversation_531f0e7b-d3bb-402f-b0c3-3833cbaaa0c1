The current implementation is functionally working, but there are failing tests and/or linting errors that need to be resolved.
Please:

1. **Run diagnostics first**: Execute `npm test` and `npm run lint` to identify all current failures
2. **Fix failing tests**: Update test files to match the current implementation, ensuring they follow the project's testing conventions:
    - Use Vitest with jest-extended matchers
    - Structure tests with Arrange/Act/Assert comments
    - Use "it should..." format for test descriptions
    - Use "describe('When...')" for context blocks
3. **Fix linting errors**: Resolve all ESLint violations, paying special attention to:
    - Boundary rule violations
    - TypeScript errors
    - Code style issues
4. **Apply boyscout rules**: Clean up the code you touch by:
    - Removing unnecessary comments
    - Improving code readability
    - Following project naming conventions
5. **Verify completion**: Ensure both `npm test` and `npm run lint` pass completely before finishing

Focus only on making the existing functionality work correctly with passing tests and clean linting - do not add new features or change the core implementation logic.