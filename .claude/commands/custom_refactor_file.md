Read the coding conventions and architectural guidelines from the CLAUDE.md file in the repository root.

Warning: The goal is not to change the current behavior of the code, but to refactor it to respect the conventions.
For example, if the code contains business logic in the client, you should move it to the server.
Or if the code contains a test that does not follow the Arrange/Act/Assert structure, you should refactor it to respect the conventions.
If you need to change the behavior, please ask the user for confirmation before proceeding.

Run "git ls-files" to list all files in the project, it will help you to understand the scope of the problem.

Then refactor the code of the file `$ARGUMENTS` to ensure it follows all the established conventions, including:
1. **Architecture patterns**: Clean Architecture + DDD + CQRS + TDD structure
2. **File naming conventions**: Proper naming for commands, queries, handlers, repositories, presenters, and tests
3. **Testing patterns**: Vitest with Arrange/Act/Assert structure, sociable tests, and proper test organization
4. **Layer separation**: Respect bounded context boundaries and ESLint boundary rules
5. **Code organization**: Proper placement of files within the layered architecture
6. **Code quality**: Apply SOLID principles, extract until drop, use full descriptive names, focus on behavior, justify every line, encapsulate dependencies, and avoid comments in production code and tests (except for Arrange/Act/Assert)
7. **Logic in server**: Ensure all business logic is in the server, not in the client.
8. **Domain tested through queries and commands**: Ensure the domain is not tested directly, but through the queries and commands.
9. **Avoid as or satisfies**: Ensure the code does not use "as" or "satisfies" to cast types. Use proper types instead.

After refactoring, run `npm run lint` and `npm test` to ensure all conventions are properly applied and no violations remain.
Fix any linting errors or test failures that arise from the refactoring.