After read all conventions in the CLAUDE.md file, follow these specific steps:

1. **Reproduce the Issue**: First, write a failing test that demonstrates the exact problem described in "my issue". The test should clearly show the expected vs actual behavior.

2. **Follow TDD Red-Green-Refactor Cycle**:
    - RED: Write a failing test that reproduces the issue
    - GREEN: Write the minimal code needed to make the test pass
    - REFACTOR: Clean up the code while keeping tests passing - apply code conventions (remove comments, apply SOLID principles, extract until drop, use full descriptive names, focus on behavior, justify every line, encapsulate dependencies etc.)

3. **Respect Project Conventions**:
    - Use Vitest testing framework with jest-extended matchers
    - Structure tests with Arrange/Act/Assert comments
    - Use `describe("When ...")` blocks for context
    - Start test descriptions with "it should..."
    - Follow Clean Architecture + DDD + CQRS patterns
    - Use absolute imports with `@/` alias
    - Apply humble object pattern for React components
    - Move business logic to services/hooks, not components
    - No comments in production code except for the three-section test structure

4. **Testing Requirements**:
    - Write sociable tests that exercise use cases (command/query handlers)
    - Avoid overly technical or solitary unit tests
    - Avoid too many mocks
    - Perform only one action in the Act section
    - Respect // Arrange, // Act, // Assert sections
    - Use InMemory repositories for testing query handlers

5. **Validation Steps**:
    - Run `npm run lint` and fix any linting errors
    - Run `npm test` and ensure all tests pass
    - Both commands must succeed before considering the task complete

Note: all conventions are mandatory and must be strictly followed.
More details can be found in the CLAUDE.md file.

Use the convex mcp server to deeply analyze the data and the behavior of the system.
You can reuse the non-sensitive data from the convex database to write your tests and validate your hypothesis.