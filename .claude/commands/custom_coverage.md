Analyze the test coverage for: 
```
$ARGUMENTS
```

Then identify any uncovered code branches, statements, and functions. Then create comprehensive functional tests to achieve better coverage. Follow these specific requirements:


1. **Coverage Analysis**: Run test coverage analysis to identify exactly which lines, branches, and functions are not covered by existing tests.

2. **Test Creation Guidelines**:
    - Write functional tests that exercise real use cases (command or query handlers) rather than isolated unit tests
    - Follow the project's TDD approach using Vitest with jest-extended matchers
    - Structure each test with clear Arrange/Act/Assert sections
    - Perform only ONE action in the Act section
    - Use `describe("When ...")` blocks for context and `it("should ...")` statements for assertions
    - Create meaningful test scenarios that represent actual business logic flows

3. **Test Quality Requirements**:
    - Each test should cover a specific uncovered code path with a realistic scenario
    - Avoid trivial tests that only increase coverage numbers without testing meaningful behavior
    - Use InMemory repositories for testing query handlers
    - Follow the project's sociable testing approach (testing through public interfaces)
    - Ensure tests are maintainable and clearly document the expected behavior

4. **Verification**:
    - Run `npm test` to ensure all new tests pass
    - Run `npm run test:coverage` to verify improved coverage
    - Run `npm run lint` to ensure code quality standards are met
    - All commands must succeed before considering the task complete

Focus on creating tests that would catch real bugs and regressions, not just achieve 100% coverage metrics.