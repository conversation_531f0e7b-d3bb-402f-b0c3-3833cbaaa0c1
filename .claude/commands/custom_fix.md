First analyze the issue and understand the root cause. 
Run "git ls-files" to list all files in the project, it will help you to understand the scope of the problem.
**Think deeply** about the problem using the Mikado method.
Use the convex mcp server to deeply analyze the data and the behavior of the system.

My issue: 
```
$ARGUMENTS
```

Please explain the user what the issue may be, the Mikado analysis and ask for confirmation before proceeding.