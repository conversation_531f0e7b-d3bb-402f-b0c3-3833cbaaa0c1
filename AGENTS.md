# Repository Instructions

## Important
- Don't run `npm install` before starting to work. It is already done.

### Volta Usage (Mandatory)
- Always run Node and npm through Volta to use the pinned toolchain.
- Prefix all npm/npx commands with `volta run`.
  - Examples:
    - `volta run npm test`
    - `volta run npm run lint`
    - `volta run npx <command>`
    - `volta run node <script>`
// This avoids issues with global shims and ensures Node `22.13.1` and npm `10.9.0` are used.

## General Workflow
- Follow a **Test-Driven Development** approach.
- After each iteration or change, run `npm run lint` and `npm test`.
- Both commands must succeed before committing.
- Keep commits focused and describe the functional change.

## Test-Driven Development (TDD) Requirements

### TDD Workflow
- **Red-Green-Refactor cycle**: Write a failing test → Make it pass → Refactor
- **Tests first, always**: A failing test must precede every line of production code
- **Triangulation**: Write the minimum code necessary to make tests pass, then add more tests to drive out the real implementation

### Pair Programming
- Either you or the user can start the pair programming session
- Implement features in ping pong style: one writes the test, the other implements minimum code to make it pass
- Wait for the next cycle after refactoring discussions

### Test Quality Standards
- **Comprehensive coverage**: Test all branches, not just happy paths
- **Parameter combinations**: Test all meaningful combinations of input parameters
- **Exception handling**: Test all error conditions and edge cases
- **Specification-style tests**: Tests should read like executable specifications of behavior
- **Isolated and independent**: Tests must run in isolation, in any order and be repeatable
- **Fast execution**: Tests should run in milliseconds, not seconds
- **No logic in tests**: Tests should only assert behavior, always prefer splitting into multiple tests rather than if/else statements
- **it** should never contain "when": Always use `describe` for context

### Testing Guidelines
- Tests use **Vitest** with jest-extended matchers.
- Use `npm test` to run them.
- Structure tests with the sections:
  ```ts
  // Arrange
  // Act
  // Assert
  ```
- Only perform **one** action in the `// Act` section (most of the time it is only one line of code).
- Use sociable tests that exercise use cases (command or query handlers).
- These act as acceptance tests; the domain is never tested directly.
- Avoid overly technical tests and solitary unit tests.
- Do **not** dispatch multiple Redux actions in one test; setup must be manual.
- `describe` blocks should use `describe("When ...")` to give context.
- Each `it` statement must start with `should` and stay close to the functional behaviour.
- `it` cannot contain behavioral context, only simple functional intention.
- Avoid `and`/`or` in single `it`, split in different `it` instead or occasionally use parameterized tests.
- Never leave `.only` or `.skip` in committed tests. Ask the user if you need them.

### Mocking Strategy
- **Don't mock what you don't own**: Use dependency injection whenever possible rather than extensive mocking
- **Use cases never use jest or vitest mocks**: Mocks are only used in rare cases and never in use cases which use in memory or fake implementations
- **Automatic execution**: All tests must be executable via `npm test`

### Code Justification
- **Every line must be justified**: If you cannot explain why a line of code exists in the new code by a relevant test, remove it
- **No speculative generality**: Don't add functionality that isn't immediately needed
- **Behavior-driven**: Code should express business requirements and domain concepts clearly

### Legacy Code Testing
- When dealing with legacy code, you always have to put a test harness
- Tests should not focus on technical details but on behaviors
- Don't rely on unstable things like css classes or ids
- Use data-testid to select DOM elements
- Don't verify volatile data (css, classes, styles for example)

## Code Quality Standards

### Documentation and Naming
- **No documentation blocks** of any kind are allowed in the codebase (tests included)
- Write **self-documenting code** through expressive naming and clear structure
- **No comments in production code** except for the three-section test structure (`// Arrange`, `// Act`, `// Assert`)
- Use **full, descriptive names** without abbreviations (e.g., `Authentication` not `Auth`, `UserRepository` not `UserRepo`)

### Design Principles (Strict Adherence Required)
- **Single Responsibility Principle**: Each class/function should have exactly one reason to change
- **Open/Closed Principle**: Open for extension, closed for modification
- **Liskov Substitution Principle**: Derived classes must be substitutable for their base classes
- **Interface Segregation Principle**: Clients should not depend on interfaces they don't use
- **Dependency Inversion Principle**: Depend on abstractions, not concretions
- **Tell Don't Ask**: Objects should tell other objects what to do, not ask for data to act upon
- **Law of Demeter**: Only talk to immediate friends, avoid chaining method calls
- **Extract until drop**: Continuously extract functions, variables, and classes until no further meaningful extraction is possible

### Code Philosophy
- **Focus on behavior over implementation**: Express what the code does, not how it does it
- **Prioritize readability over DRY**: Don't repeat yourself only when it improves readability and maintainability
- **Keep It Simple (KISS)**: Simple means readable, maintainable, and testable - not naive or oversimplified
- **Encapsulate external dependencies**: Always wrap third-party libraries in your own abstractions
- **Use DTOs (Data Transfer Objects)** for transferring data between layers
- **Avoid getter and setter methods**: Always prefer behavior methods over exposing internal state

## Project Architecture
- The project follows **Clean Architecture + DDD + CQRS + TDD** organized by **Bounded Contexts**:
  - **Structure**: `src/client/[BoundedContext]` and `src/server/[BoundedContext]`
  - **Bounded Contexts**: `Authentication`, `DeckBuilding`, `Gaming`, `MatchMaking`, `Shared`
  - **Layers**: `domain`, `application`, `infrastructure`, `presentation`, `specs`
  - **ESLint boundary rules** enforce these constraints

## Key Patterns
- **CQRS**: Commands and Queries with dedicated handlers
- **Repository Pattern**: Interfaces in application, implementations in infrastructure
- **Presenter Pattern**: For view models and display logic
- **Domain Entities**: With factory methods, snapshots, and business logic
- **Value Objects**: Immutable objects representing domain concepts

## File Naming Conventions
- **Commands**: `[Action]Command.ts`, `[Action]CommandHandler.ts`
- **Queries**: `[Action]Query.ts`, `[Action]QueryHandler.ts`
- **Repositories**: `[Entity]Repository.ts` (interface), `Convex[Entity]Repository.ts`, `InMemory[Entity]Repository.ts`
- **Presenters**: `[Action]Presenter.ts` (interface), `[Action]WebPresenter.ts`
- **Tests**: `[action].spec.ts`
- **Domain**: `[Entity].ts`, `[ValueObject].ts`

## Import Conventions
- Path alias `@/*` points to the repository root
- Always use absolute imports with the `@/` alias
- Import from interfaces, not implementations
- Follow dependency direction: domain ← application ← infrastructure/presentation

## Tools
- **Linting**: `npm run lint` (TypeScript + ESLint with boundary rules)
- **Testing**: `npm test` (Vitest)
- **Coverage**: `npm run test:coverage`
- **Development**: `npm run dev:all` (Next.js + Convex)

## Frontend Specific
- **Framework**: Next.js 15 with React 19
- **UI**: Radix UI components and themes
- **State**: Redux with clean architecture patterns
- **Internationalization**: Supports `en` and `fr` locales
- **Styling**: Tailwind CSS
- **Components**: Apply the humble object pattern. Move all logic outside of
  React components by relying on async thunks for use cases and selectors for
  querying data. Use hooks with no logic as the glue between components and
  use cases.

## Commit Requirements
- Keep the working directory clean before committing
- Include relevant tests for new features or fixes
- Ensure `npm run lint` and `npm test` pass before every commit
- Follow conventional commit messages describing functional changes
- Branch names should be descriptive and related to the task or feature in english


## AI Assistant Guidance

### Bounded Context Navigation

When working on features, first identify the appropriate bounded context:

- **Authentication**: User sign-in, permissions, user management
- **CatalogManagement**: Card catalog creation, updates, and management (admin features)
- **DeckBuilding**: Deck creation, editing, card filtering, and deck management
- **GameManagement**: Game creation, configuration, and admin features
- **Gaming**: Match gameplay, 3D rendering, player interactions
- **MatchMaking**: Queue management, player matching, game lobbies
- **Shared**: Common utilities, components, and cross-cutting concerns

### File Location Strategies

1. **Start with bounded context**: `src/{client|server}/[BoundedContext]`
2. **Layer identification**:
   - Domain logic → `domain/`
   - Use cases → `application/commands|queries/`
   - External concerns → `infrastructure/`
   - UI components → `infrastructure/components/`
   - Tests → `specs/`

3. **Common patterns**:
   - Commands: `application/commands/[action]/[action]Command.ts`
   - Queries: `application/queries/[action]/[action]Query.ts`
   - Components: `infrastructure/components/app/[Context]/[Component]/`
   - Services: `infrastructure/services/[Service]/`

### Working with CQRS Patterns

**Adding a new command:**
1. Create command: `application/commands/[action]/[Action]Command.ts`
2. Create handler: `application/commands/[action]/[Action]CommandHandler.ts`
3. Add to thunk: `application/commands/[action]/[action].ts`
4. Create request type: `application/commands/[action]/[action]Request.ts`
5. Write tests: `specs/unit/commands/[action].spec.ts`

**Adding a new query:**
1. Create selector: `application/queries/[action]/[action].ts`
2. Write tests: `specs/unit/queries/[action].spec.ts`

## Directory Overview
- **Root folders**: `app`, `assets`, `convex`, `eslint-plugin-custom-rules`, `i18n`, `public`, `src`, `tools`
- **src** contains all bounded contexts organized under `client` and `server`:
  - `src/client/[BoundedContext]`
    - `application`
    - `domain`
    - `infrastructure`
    - `specs`
  - `src/server/[BoundedContext]`
    - `application`
    - `domain`
    - `infrastructure`
    - `presentation`
    - `specs`

Each bounded context repeats this layered structure. The complete directory tree
for the `src` folder is provided below as a reference so you can see the
contents of every `application`, `domain`, `infrastructure` and `presentation`
folder.

```
src
├── client
│   ├── Authentication
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   └── signIn
│   │   │   │       ├── signIn.ts
│   │   │   │       └── signInRequest.ts
│   │   │   └── queries
│   │   │       ├── getAuthError
│   │   │       │   └── getAuthError.ts
│   │   │       └── isSigningIn
│   │   │           └── isSigningIn.ts
│   │   ├── domain
│   │   │   └── Auth
│   │   │       ├── authEvents.ts
│   │   │       └── authReducer.ts
│   │   ├── infrastructure
│   │   │   ├── components
│   │   │   │   └── app
│   │   │   │       └── Auth
│   │   │   │           └── SignIn
│   │   │   │               ├── SignInButton
│   │   │   │               │   └── SignInButton.tsx
│   │   │   │               └── SignInForm
│   │   │   │                   ├── SignInForm.tsx
│   │   │   │                   └── useSignInForm.tsx
│   │   │   └── pages
│   │   │       └── Auth
│   │   │           ├── AccessDeniedPage
│   │   │           │   └── AccessDeniedPage.tsx
│   │   │           └── SignInPage
│   │   │               └── SignInPage.tsx
│   │   └── specs
│   │       └── unit
│   │           └── commands
│   │               └── signIn.spec.ts
│   ├── CatalogManagement
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   ├── createCatalogCard
│   │   │   │   │   ├── createCatalogCard.ts
│   │   │   │   │   └── createCatalogCardRequest.ts
│   │   │   │   ├── deleteCatalogCard
│   │   │   │   │   ├── deleteCatalogCard.ts
│   │   │   │   │   └── deleteCatalogCardRequest.ts
│   │   │   │   ├── loadCatalogCards
│   │   │   │   │   ├── loadCatalogCards.ts
│   │   │   │   │   └── loadCatalogCardsRequest.ts
│   │   │   │   └── updateCatalogCard
│   │   │   │       ├── updateCatalogCard.ts
│   │   │   │       └── updateCatalogCardRequest.ts
│   │   │   └── queries
│   │   │       ├── getCatalogCards
│   │   │       │   └── getCatalogCards.ts
│   │   │       ├── getCatalogManagementError
│   │   │       │   └── getCatalogManagementError.ts
│   │   │       └── isCatalogManagementLoading
│   │   │           └── isCatalogManagementLoading.ts
│   │   ├── domain
│   │   │   └── Catalog
│   │   │       ├── CatalogCard.ts
│   │   │       ├── catalogEvents.ts
│   │   │       └── catalogReducer.ts
│   │   ├── infrastructure
│   │   │   └── pages
│   │   │       └── CatalogManagementPage
│   │   │           └── CatalogManagementPage.tsx
│   │   └── specs
│   │       └── unit
│   │           └── commands
│   │               └── loadCatalogCards.spec.ts
│   ├── DeckBuilding
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   ├── addCardToDeck
│   │   │   │   │   ├── addCardToDeck.ts
│   │   │   │   │   └── addCardToDeckRequest.ts
│   │   │   │   ├── autoSaveDeck
│   │   │   │   │   ├── autoSaveDeck.ts
│   │   │   │   │   ├── autoSaveDeckRequest.ts
│   │   │   │   │   └── autoSaveDeckResult.ts
│   │   │   │   ├── createDeckAndNavigate
│   │   │   │   │   ├── createDeckAndNavigate.ts
│   │   │   │   │   ├── createDeckAndNavigateRequest.ts
│   │   │   │   │   └── createDeckAndNavigateResult.ts
│   │   │   │   ├── filterCatalog
│   │   │   │   │   ├── filterCatalog.ts
│   │   │   │   │   └── filterCatalogRequest.ts
│   │   │   │   ├── hideCardDetails
│   │   │   │   │   └── hideCardDetails.ts
│   │   │   │   ├── initializeDeckBuilderFromLocation
│   │   │   │   │   └── initializeDeckBuilderFromLocation.ts
│   │   │   │   ├── initializeDeckEditor
│   │   │   │   │   ├── initializeDeckEditor.ts
│   │   │   │   │   └── initializeDeckEditorRequest.ts
│   │   │   │   ├── loadCatalogCards
│   │   │   │   │   ├── loadCatalogCards.ts
│   │   │   │   │   └── loadCatalogCardsRequest.ts
│   │   │   │   ├── loadCatalogCardsByGameId
│   │   │   │   │   ├── loadCatalogCardsByGameId.ts
│   │   │   │   │   └── loadCatalogCardsByGameIdRequest.ts
│   │   │   │   ├── loadDeckById
│   │   │   │   │   ├── loadDeckById.ts
│   │   │   │   │   └── loadDeckByIdRequest.ts
│   │   │   │   ├── loadDeckIntoBuilder
│   │   │   │   │   ├── loadDeckIntoBuilder.ts
│   │   │   │   │   └── loadDeckIntoBuilderRequest.ts
│   │   │   │   ├── loadGameSettings
│   │   │   │   │   ├── loadGameSettings.ts
│   │   │   │   │   └── loadGameSettingsRequest.ts
│   │   │   │   ├── loadGameSettingsByGameId
│   │   │   │   │   ├── loadGameSettingsByGameId.ts
│   │   │   │   │   └── loadGameSettingsByGameIdRequest.ts
│   │   │   │   ├── removeCardFromDeck
│   │   │   │   │   ├── removeCardFromDeck.ts
│   │   │   │   │   └── removeCardFromDeckRequest.ts
│   │   │   │   ├── search
│   │   │   │   │   ├── search.ts
│   │   │   │   │   └── searchRequest.ts
│   │   │   │   ├── showCardDetails
│   │   │   │   │   ├── showCardDetails.ts
│   │   │   │   │   └── showCardDetailsRequest.ts
│   │   │   │   ├── switchDeckBuilderView
│   │   │   │   │   ├── switchDeckBuilderView.ts
│   │   │   │   │   └── switchDeckBuilderViewRequest.ts
│   │   │   │   ├── updateAvailableFilters
│   │   │   │   │   ├── updateAvailableFilters.ts
│   │   │   │   │   └── updateAvailableFiltersRequest.ts
│   │   │   │   └── updateDeckName
│   │   │   │       ├── updateDeckName.ts
│   │   │   │       ├── updateDeckNameRequest.ts
│   │   │   │       └── updateDeckNameResult.ts
│   │   │   ├── queries
│   │   │   │   ├── applyFilterToCard
│   │   │   │   │   └── applyFilterToCard.ts
│   │   │   │   ├── findDeckCardById
│   │   │   │   │   └── findDeckCardById.ts
│   │   │   │   ├── getActiveFilters
│   │   │   │   │   └── getActiveFilters.ts
│   │   │   │   ├── getAvailableFilters
│   │   │   │   │   └── getAvailableFilters.ts
│   │   │   │   ├── getCardDetails
│   │   │   │   │   └── getCardDetails.ts
│   │   │   │   ├── getCardsInDeck
│   │   │   │   │   └── getCardsInDeck.ts
│   │   │   │   ├── getCardsInDeckMap
│   │   │   │   │   └── getCardsInDeckMap.ts
│   │   │   │   ├── getCatalogCardById
│   │   │   │   │   └── getCatalogCardById.ts
│   │   │   │   ├── getCatalogCards
│   │   │   │   │   └── getCatalogCards.ts
│   │   │   │   ├── getCatalogError
│   │   │   │   │   └── getCatalogError.ts
│   │   │   │   ├── getDeck
│   │   │   │   │   └── getDeck.ts
│   │   │   │   ├── getDeckBuilderMessageStatus
│   │   │   │   │   └── getDeckBuilderMessageStatus.ts
│   │   │   │   ├── getDeckBuilderUiStatus
│   │   │   │   │   └── getDeckBuilderUiStatus.ts
│   │   │   │   ├── getDeckBuilderView
│   │   │   │   │   └── getDeckBuilderView.ts
│   │   │   │   ├── getDeckCardQuantities
│   │   │   │   │   └── getDeckCardQuantities.ts
│   │   │   │   ├── getDeckError
│   │   │   │   │   └── getDeckError.ts
│   │   │   │   ├── getDeckName
│   │   │   │   │   └── getDeckName.ts
│   │   │   │   ├── getDeckNameError
│   │   │   │   │   └── getDeckNameError.ts
│   │   │   │   ├── getDeckTags
│   │   │   │   │   └── getDeckTags.ts
│   │   │   │   ├── getFilteredCards
│   │   │   │   │   └── getFilteredCards.ts
│   │   │   │   ├── getGameSettings
│   │   │   │   │   └── getGameSettings.ts
│   │   │   │   ├── getGameSettingsError
│   │   │   │   │   └── getGameSettingsError.ts
│   │   │   │   ├── getMaxCardsInDeck
│   │   │   │   │   └── getMaxCardsInDeck.ts
│   │   │   │   ├── getSearchTerm
│   │   │   │   │   └── getSearchTerm.ts
│   │   │   │   ├── getTotalCardsInDeck
│   │   │   │   │   └── getTotalCardsInDeck.ts
│   │   │   │   ├── isCatalogLoading
│   │   │   │   │   └── isCatalogLoading.ts
│   │   │   │   ├── isDeckLoading
│   │   │   │   │   └── isDeckLoading.ts
│   │   │   │   ├── isDeckNameSaving
│   │   │   │   │   └── isDeckNameSaving.ts
│   │   │   │   ├── isDeckNotFound
│   │   │   │   │   └── isDeckNotFound.ts
│   │   │   │   ├── isGameSettingsLoading
│   │   │   │   │   └── isGameSettingsLoading.ts
│   │   │   │   └── shouldAutoSave
│   │   │   │       └── shouldAutoSave.ts
│   │   │   └── services
│   │   │       ├── Catalog
│   │   │       │   └── CatalogService.ts
│   │   │       ├── Deck
│   │   │       │   └── DeckService.ts
│   │   │       └── GameSettings
│   │   │           └── GameSettingsService.ts
│   │   ├── domain
│   │   │   ├── CardData
│   │   │   │   └── CardData..ts
│   │   │   ├── Catalog
│   │   │   │   ├── CatalogCard.ts
│   │   │   │   ├── CatalogFilters.ts
│   │   │   │   ├── Filter.ts
│   │   │   │   ├── catalogEvents.ts
│   │   │   │   ├── catalogFilterEvents.ts
│   │   │   │   ├── catalogFiltersReducer.ts
│   │   │   │   ├── catalogReducer.ts
│   │   │   │   ├── catalogSearchEvents.ts
│   │   │   │   └── catalogSearchReducer.ts
│   │   │   ├── Deck
│   │   │   │   ├── Deck.ts
│   │   │   │   ├── deckEvents.ts
│   │   │   │   └── deckReducer.ts
│   │   │   ├── DeckBuilder
│   │   │   │   ├── DeckBuilder.ts
│   │   │   │   ├── DeckBuilderCard.ts
│   │   │   │   ├── deckBuilderEvents.ts
│   │   │   │   ├── deckBuilderReducer.ts
│   │   │   │   └── ui
│   │   │   │       ├── deckBuilderUiEvents.ts
│   │   │   │       └── deckBuilderUiReducer.ts
│   │   │   └── GameSettings
│   │   │       ├── GameSettings.ts
│   │   │       ├── gameSettingsEvents.ts
│   │   │       └── gameSettingsReducer.ts
│   │   ├── infrastructure
│   │   │   ├── components
│   │   │   │   └── app
│   │   │   │       └── DeckBuilding
│   │   │   │           ├── DeckBuilderCardsGrid
│   │   │   │           │   ├── DeckBuilderCardsGrid.tsx
│   │   │   │           │   ├── DeckBuilderCardsGridComponent.tsx
│   │   │   │           │   ├── DeckBuilderHeader.tsx
│   │   │   │           │   ├── DeckRenameDialog.tsx
│   │   │   │           │   └── VirtualizedCardsGrid.tsx
│   │   │   │           ├── DeckBuilderInitializer
│   │   │   │           │   └── DeckBuilderInitializer.ts
│   │   │   │           ├── DeckBuilderMessageSection
│   │   │   │           │   ├── DeckBuilderCreationOnboardingMessage.tsx
│   │   │   │           │   ├── DeckBuilderDeckLoadingMessage.tsx
│   │   │   │           │   └── DeckBuilderMessageSection.tsx
│   │   │   │           ├── DeckBuilderPanel
│   │   │   │           │   ├── DeckBuilderPanel.tsx
│   │   │   │           │   └── DeckCardRow.tsx
│   │   │   │           ├── DeckBuildingCard
│   │   │   │           │   └── DeckBuildingCard.tsx
│   │   │   │           ├── DeckBuildingFilters
│   │   │   │           │   ├── DeckBuildingFilters.tsx
│   │   │   │           │   └── FilterItem.tsx
│   │   │   │           ├── DeckBuildingSkeletonCard
│   │   │   │           │   └── DeckBuildingSkeletonCard.tsx
│   │   │   │           ├── DeckCardDetailsDialog
│   │   │   │           │   └── DeckCardDetailsDialog.tsx
│   │   │   │           ├── DeckList
│   │   │   │           │   └── DeckList.tsx
│   │   │   │           ├── EditDeckInitializer
│   │   │   │           │   └── EditDeckInitializer.tsx
│   │   │   │           ├── RenameDeckDialog
│   │   │   │           │   └── CreateOrRenameDeckDialog.tsx
│   │   │   │           └── TotalCardsInDeckCounter
│   │   │   │               └── TotalCardsInDeckCounter.tsx
│   │   │   ├── dtos
│   │   │   │   ├── LoadCatalogCardsByGameIdDto.ts
│   │   │   │   ├── LoadDeckByIdDto.ts
│   │   │   │   └── LoadGameSettingsByGameIdDto.ts
│   │   │   ├── hooks
│   │   │   │   ├── useAutoSaveDeck
│   │   │   │   │   └── useAutoSaveDeck.ts
│   │   │   │   ├── useCatalogCardsByGameId
│   │   │   │   │   └── useCardsByGameId.tsx
│   │   │   │   ├── useDeckBuilder
│   │   │   │   │   └── useDeckBuilder.ts
│   │   │   │   ├── useDeckBuilderCardsGrid
│   │   │   │   │   ├── useDeckBuilderCardsGrid.tsx
│   │   │   │   │   ├── useDeckBuilderSearch.ts
│   │   │   │   │   └── useVirtualGrid.ts
│   │   │   │   ├── useDeckById
│   │   │   │   │   └── useDeckById.ts
│   │   │   │   ├── useDeckCreation
│   │   │   │   │   └── useDeckCreation.ts
│   │   │   │   ├── useDeckId
│   │   │   │   │   └── useDeckId.ts
│   │   │   │   ├── useDeckName
│   │   │   │   │   └── useDeckName.ts
│   │   │   │   ├── useGameSettingsByGameId
│   │   │   │   │   └── useGameSettingsByGameId.tsx
│   │   │   │   ├── useInitializeDeckBuilderFromLocation
│   │   │   │   │   └── useInitializeDeckBuilderFromLocation.ts
│   │   │   │   └── useInitializeDeckEditor
│   │   │   │       └── useInitializeDeckEditor.ts
│   │   │   └── services
│   │   │       ├── CatalogService
│   │   │       │   ├── ConvexCatalogService.ts
│   │   │       │   └── FakeCatalogService.ts
│   │   │       ├── DeckService
│   │   │       │   ├── ConvexDeckService.ts
│   │   │       │   └── FakeDeckService.ts
│   │   │       └── GameSettingsService
│   │   │           ├── ConvexGameSettingsService.ts
│   │   │           └── FakeGameSettingsService.ts
│   │   └── specs
│   │       ├── components
│   │       │   ├── DeckBuilderCardsGrid.spec.tsx
│   │       │   ├── DeckBuilderInitializer.spec.tsx
│   │       │   └── DeckBuilderMessageSection.spec.tsx
│   │       ├── helpers
│   │       │   └── factories
│   │       │       └── fakeCatalogCards.ts
│   │       └── unit
│   │           ├── commands
│   │           │   ├── addCardToDeck.spec.ts
│   │           │   ├── autoSaveDeck.spec.ts
│   │           │   ├── filterCatalog.spec.ts
│   │           │   ├── hideCardDetails.spec.ts
│   │           │   ├── initFromLocation.spec.ts
│   │           │   ├── loadCatalogCards.spec.ts
│   │           │   ├── loadCatalogCardsByGameId.spec.ts
│   │           │   ├── loadDeckById.spec.ts
│   │           │   ├── loadDeckIntoBuilder.spec.ts
│   │           │   ├── loadGameSettings.spec.ts
│   │           │   ├── loadGameSettingsByGameId.spec.ts
│   │           │   ├── removeCardFromDeck.spec.ts
│   │           │   ├── search.spec.ts
│   │           │   ├── showCardDetails.spec.ts
│   │           │   ├── switchDeckBuilderView.spec.ts
│   │           │   ├── updateAvailableFilters.spec.ts
│   │           │   └── updateDeckName.spec.ts
│   │           ├── hooks
│   │           │   └── useDeckBuilderSearch.spec.ts
│   │           └── queries
│   │               ├── applyFilterToCard.spec.ts
│   │               ├── findDeckCardById.spec.ts
│   │               ├── getActiveFilters.spec.ts
│   │               ├── getDeckBuilderMessageStatus.spec.ts
│   │               ├── getDeckBuilderView.spec.ts
│   │               ├── getDeckCardQuantities
│   │               │   └── getDeckCardQuantities.spec.ts
│   │               ├── getDeckName.spec.ts
│   │               ├── getFilteredCatalogCards.spec.ts
│   │               ├── getTotalCardsInDeck.spec.ts
│   │               └── shouldAutoSave.spec.ts
│   ├── GameManagement
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   ├── createGame
│   │   │   │   │   └── createGame.ts
│   │   │   │   ├── deleteGame
│   │   │   │   │   └── deleteGame.ts
│   │   │   │   ├── loadGames
│   │   │   │   │   ├── loadGames.ts
│   │   │   │   │   └── loadGamesRequest.ts
│   │   │   │   └── updateGame
│   │   │   │       └── updateGame.ts
│   │   │   └── queries
│   │   │       ├── getGameManagementError
│   │   │       │   └── getGameManagementError.ts
│   │   │       ├── getGames
│   │   │       │   └── getGames.ts
│   │   │       └── isGameManagementLoading
│   │   │           └── isGameManagementLoading.ts
│   │   ├── domain
│   │   │   └── Game
│   │   │       ├── Game.ts
│   │   │       ├── gameEvents.ts
│   │   │       └── gameReducer.ts
│   │   ├── infrastructure
│   │   │   └── pages
│   │   │       ├── GamesManagementPage
│   │   │       │   └── GamesManagementPage.tsx
│   │   │       └── StudioHomePage
│   │   │           └── StudioHomePage.tsx
│   │   └── specs
│   │       └── unit
│   │           └── commands
│   │               └── loadGames.spec.ts
│   ├── Gaming
│   │   ├── application
│   │   │   ├── commands
│   │   │   │   ├── cancelMatchRegistration
│   │   │   │   │   ├── cancelMatchRegistration.ts
│   │   │   │   │   ├── cancelMatchRegistrationRequest.ts
│   │   │   │   │   └── cancelMatchRegistrationResult.ts
│   │   │   │   ├── initializeGameScene
│   │   │   │   │   ├── initializeGameScene.ts
│   │   │   │   │   └── initializeGameSceneRequest.ts
│   │   │   │   ├── joinMatchMakingQueue
│   │   │   │   │   ├── joinMatchMakingQueue.ts
│   │   │   │   │   ├── joinMatchMakingQueueRequest.ts
│   │   │   │   │   └── joinMatchMakingQueueResult.ts
│   │   │   │   ├── leaveMatch
│   │   │   │   │   ├── leaveMatch.ts
│   │   │   │   │   ├── leaveMatchRequest.ts
│   │   │   │   │   └── leaveMatchResult.ts
│   │   │   │   ├── loadDecksForGame
│   │   │   │   │   ├── loadDecksForGame.ts
│   │   │   │   │   └── loadDecksForGameRequest.ts
│   │   │   │   ├── monitorMatchCreation
│   │   │   │   │   ├── monitorMatchCreation.ts
│   │   │   │   │   ├── monitorMatchCreationRequest.ts
│   │   │   │   │   └── monitorMatchCreationResult.ts
│   │   │   │   ├── selectDeck
│   │   │   │   │   └── selectDeck.ts
│   │   │   │   ├── startSearchTimer
│   │   │   │   │   └── startSearchTimer.ts
│   │   │   │   ├── stopSearchTimer
│   │   │   │   │   └── stopSearchTimer.ts
│   │   │   │   ├── tickSearchTimer
│   │   │   │   │   └── tickSearchTimer.ts
│   │   │   │   └── updatePlayerHand
│   │   │   │       └── updatePlayerHand.ts
│   │   │   ├── queries
│   │   │   │   ├── formatSearchTime
│   │   │   │   │   └── formatSearchTime.ts
│   │   │   │   ├── getAvailableDecks
│   │   │   │   │   └── getAvailableDecks.ts
│   │   │   │   ├── getFoundMatchId
│   │   │   │   │   └── getFoundMatchId.ts
│   │   │   │   ├── getMatchError
│   │   │   │   │   └── getMatchError.ts
│   │   │   │   ├── getPlayerHands
│   │   │   │   │   └── getPlayerHands.ts
│   │   │   │   ├── getSearchTime
│   │   │   │   │   └── getSearchTime.ts
│   │   │   │   ├── getSelectedDeck
│   │   │   │   │   └── getSelectedDeck.ts
│   │   │   │   ├── getSelectedDeckId
│   │   │   │   │   └── getSelectedDeckId.ts
│   │   │   │   ├── isDecksLoading
│   │   │   │   │   └── isDecksLoading.ts
│   │   │   │   └── isSearching
│   │   │   │       └── isSearching.ts
│   │   │   └── services
│   │   │       ├── DeckList
│   │   │       │   └── DeckListService.ts
│   │   │       ├── Game
│   │   │       │   └── GameService.ts
│   │   │       ├── Match
│   │   │       │   └── MatchService.ts
│   │   │       └── MatchMaking
│   │   │           └── MatchMakingService.ts
│   │   ├── domain
│   │   │   ├── DeckSelection
│   │   │   │   ├── deckSelectionEvents.ts
│   │   │   │   └── deckSelectionReducer.ts
│   │   │   ├── Match
│   │   │   │   ├── matchEvents.ts
│   │   │   │   └── matchReducer.ts
│   │   │   ├── MatchMaking
│   │   │   │   ├── matchMakingEvents.ts
│   │   │   │   └── matchMakingReducer.ts
│   │   │   └── PlayerHands
│   │   │       ├── PlayerHands.ts
│   │   │       ├── playerHandEvents.ts
│   │   │       └── playerHandsReducer.ts
│   │   ├── infrastructure
│   │   │   ├── components
│   │   │   │   ├── app
│   │   │   │   │   ├── 3d
│   │   │   │   │   │   ├── Camera
│   │   │   │   │   │   │   ├── Camera.tsx
│   │   │   │   │   │   │   └── CameraDebugger.tsx
│   │   │   │   │   │   ├── Card
│   │   │   │   │   │   │   └── Card.tsx
│   │   │   │   │   │   ├── CardEffects
│   │   │   │   │   │   │   └── CardEffects.tsx
│   │   │   │   │   │   ├── FixedInView
│   │   │   │   │   │   │   └── FixedInView.tsx
│   │   │   │   │   │   ├── FixedToCamera
│   │   │   │   │   │   │   └── FixedToCamera.tsx
│   │   │   │   │   │   ├── Game2x7Layout
│   │   │   │   │   │   │   └── Game2x7Layout.tsx
│   │   │   │   │   │   ├── GameScene
│   │   │   │   │   │   │   └── GameScene.tsx
│   │   │   │   │   │   ├── Lights
│   │   │   │   │   │   │   └── Lights.tsx
│   │   │   │   │   │   ├── Player1Hand
│   │   │   │   │   │   │   └── FixedPlayer1Hand.tsx
│   │   │   │   │   │   ├── Player1Rows
│   │   │   │   │   │   │   ├── Player1FirstRow.tsx
│   │   │   │   │   │   │   └── Player1SecondRow.tsx
│   │   │   │   │   │   ├── Player2Hand
│   │   │   │   │   │   │   └── FixedPlayer2Hand.tsx
│   │   │   │   │   │   ├── Player2Rows
│   │   │   │   │   │   │   ├── Player2FirstRow.tsx
│   │   │   │   │   │   │   └── Player2SecondRow.tsx
│   │   │   │   │   │   ├── Shadow
│   │   │   │   │   │   │   └── Shadow.tsx
│   │   │   │   │   │   └── Table
│   │   │   │   │   │       └── Table.tsx
│   │   │   │   │   └── Gaming
│   │   │   │   │       ├── ConfirmGameAlert
│   │   │   │   │       │   └── ConfirmGameAlert.tsx
│   │   │   │   │       ├── GameDetailsButton
│   │   │   │   │       │   └── GameDetailsButton.tsx
│   │   │   │   │       ├── HUD
│   │   │   │   │       │   └── HUD.tsx
│   │   │   │   │       ├── LeaveMatchButton
│   │   │   │   │       │   └── LeaveMatchButton.tsx
│   │   │   │   │       ├── Match
│   │   │   │   │       │   └── Match.tsx
│   │   │   │   │       ├── PlayGameButton
│   │   │   │   │       │   └── PlayGameButton.tsx
│   │   │   │   │       └── RedirectToGameList
│   │   │   │   │           └── RedirectToGameList.tsx
│   │   │   │   └── debug
│   │   │   │       └── MatchConsoleEvents
│   │   │   │           └── MatchConsoleEvents.tsx
│   │   │   ├── hooks
│   │   │   │   └── useMatchEndNavigation
│   │   │   │       └── useMatchEndNavigation.ts
│   │   │   ├── pages
│   │   │   │   └── Gaming
│   │   │   │       ├── ErrorLoadingMatchPage
│   │   │   │       │   └── ErrorLoadingMatchPage.tsx
│   │   │   │       ├── FinishedMatchPage
│   │   │   │       │   └── FinishedMatchPage.tsx
│   │   │   │       ├── GameDetailsPage
│   │   │   │       │   └── GameDetailsPage.tsx
│   │   │   │       ├── GameListPage
│   │   │   │       │   └── GameListPage.tsx
│   │   │   │       ├── MatchPage
│   │   │   │       │   └── MatchPage.tsx
│   │   │   │       ├── PlayGamePage
│   │   │   │       │   ├── PlayGamePage.tsx
│   │   │   │       │   └── usePlayGamePage.tsx
│   │   │   │       └── WaitingForOpponentPage
│   │   │   │           ├── WaitingForOpponentPage.tsx
│   │   │   │           └── useWaitingForOpponentPage.tsx
│   │   │   └── services
│   │   │       ├── DeckList
│   │   │       │   ├── ConvexDeckListService.ts
│   │   │       │   └── FakeDeckListService.ts
│   │   │       ├── Game
│   │   │       │   └── FakeGameService.ts
│   │   │       ├── Match
│   │   │       │   ├── ConvexMatchService.ts
│   │   │       │   └── FakeMatchService.ts
│   │   │       └── MatchMaking
│   │   │           ├── ConvexMatchMakingService.ts
│   │   │           └── FakeMatchMakingService.ts
│   │   └── specs
│   │       ├── components
│   │       │   ├── Card.spec.tsx
│   │       │   ├── Match.spec.tsx
│   │       │   ├── MatchPage.spec.tsx
│   │       │   └── PlayerAreas.spec.tsx
│   │       └── unit
│   │           ├── commands
│   │           │   ├── cancelMatchRegistration.spec.ts
│   │           │   ├── joinMatchMakingQueue.spec.ts
│   │           │   ├── leaveMatch.spec.ts
│   │           │   ├── loadDecksForGame.spec.ts
│   │           │   └── updatePlayerHand.spec.ts
│   │           ├── domain
│   │           │   ├── deckSelectionReducer.spec.ts
│   │           │   └── matchMakingReducer.spec.ts
│   │           ├── hooks
│   │           │   ├── usePlayGamePage.spec.tsx
│   │           │   └── useWaitingForOpponentPage.spec.tsx
│   │           └── queries
│   │               └── formatSearchTime.spec.ts
│   ├── MatchMaking
│   │   └── infrastructure
│   │       └── components
│   │           └── debug
│   │               └── MatchMakingConsoleEvents
│   │                   └── MatchMakingConsoleEvents.tsx
│   └── Shared
│       ├── components
│       │   ├── Background
│       │   │   └── Background.tsx
│       │   ├── CardDeck
│       │   │   └── CardDeck.tsx
│       │   ├── Headers
│       │   │   └── MainHeader.tsx
│       │   ├── JsonObjectViewer
│       │   │   └── JsonObjectViewer.tsx
│       │   ├── ShiningButton
│       │   │   ├── ShiningButton.css
│       │   │   └── ShiningButton.tsx
│       │   ├── ShiningCard
│       │   │   ├── ShiningCard.css
│       │   │   └── ShiningCard.tsx
│       │   ├── SkeletonHelper
│       │   │   └── SkeletonHelper.tsx
│       │   └── Sparkles
│       │       └── Sparkles.tsx
│       ├── helpers
│       │   ├── Chunk
│       │   │   └── chunk.ts
│       │   ├── DeepMerge
│       │   │   └── deepMerge.ts
│       │   ├── IsPlainObject
│       │   │   └── isPlainObject.ts
│       │   ├── Tailwind
│       │   │   └── utils.ts
│       │   └── UrlBuilder
│       │       ├── studioUrlBuilder.spec.ts
│       │       ├── urlBuilder.spec.ts
│       │       └── urlBuilder.ts
│       ├── hooks
│       │   ├── useDebounce
│       │   │   ├── index.ts
│       │   │   └── useDebounce.ts
│       │   ├── useGameId
│       │   │   └── useGameId.ts
│       │   ├── useLocale
│       │   │   └── useLocale.ts
│       │   └── useResponsiveColumnCount
│       │       └── useResponsiveColumnCount.tsx
│       ├── layouts
│       │   ├── FullPageLayout
│       │   │   └── FullPageLayout.tsx
│       │   └── RootLayout
│       │       └── RootLayout.tsx
│       ├── providers
│       │   ├── ConvexClientProvider
│       │   │   ├── ConvexClient.ts
│       │   │   └── ConvexClientProvider.tsx
│       │   ├── ReduxProvider
│       │   │   └── ReduxProvider.tsx
│       │   └── ToastProvider
│       │       ├── ToastContext.tsx
│       │       ├── ToastProvider.tsx
│       │       └── useToast.tsx
│       ├── services
│       │   ├── Location
│       │   │   ├── FakeLocationService.ts
│       │   │   ├── LocationService.ts
│       │   │   └── RealLocationService.ts
│       │   └── Timer
│       │       ├── FakeTimerService.ts
│       │       ├── RealTimerService.ts
│       │       └── TimerService.ts
│       ├── store
│       │   ├── appStore
│       │   │   ├── appDispatch.ts
│       │   │   ├── rootInitialState.ts
│       │   │   ├── rootReducers.ts
│       │   │   ├── rootState.ts
│       │   │   └── thunkExtra.ts
│       │   └── reduxStore
│       │       └── reduxStore.ts
│       └── testing
│           └── store
│               └── createTestingStore.ts
└── server
    ├── Authentication
    │   ├── application
    │   │   ├── ports
    │   │   │   ├── AppUserRepository.ts
    │   │   │   ├── AuthenticationGateway.ts
    │   │   │   ├── CurrentUserRepository.ts
    │   │   │   └── GetCurrentUserPresenter.ts
    │   │   └── queries
    │   │       └── GetCurrentUser
    │   │           └── GetCurrentUserQueryHandler.ts
    │   ├── domain
    │   │   ├── AppUser
    │   │   │   ├── AppUser.ts
    │   │   │   └── valueObjects
    │   │   │       └── AppUserId.ts
    │   │   └── User
    │   │       ├── CurrentUser.ts
    │   │       └── errors
    │   │           ├── UserNotAuthenticatedError.ts
    │   │           └── UserNotRegisteredError.ts
    │   ├── infrastructure
    │   │   ├── gateways
    │   │   │   └── AuthenticationGateway
    │   │   │       └── ConvexAuthenticationGateway.ts
    │   │   └── repositories
    │   │       ├── AppUser
    │   │       │   ├── ConvexAppUserRepository.ts
    │   │       │   └── InMemoryAppUserRepository.ts
    │   │       └── CurrentUser
    │   │           ├── ConvexCurrentUserRepository.ts
    │   │           ├── InMemoryCurrentUserRepository.ts
    │   │           └── InMemoryFailingCurrentUserRepository.ts
    │   ├── presentation
    │   │   ├── presenters
    │   │   │   └── GetCurrentUserWebPresenter.ts
    │   │   └── viewModels
    │   │       └── GetCurrentUserViewModel.ts
    │   └── specs
    │       ├── helpers
    │       │   ├── createAppUsers.ts
    │       │   ├── createFakeUsers.ts
    │       │   ├── dtos
    │       │   │   └── UserDTO.ts
    │       │   ├── fakes
    │       │   │   └── fakeUsers.ts
    │       │   └── getAppUser.ts
    │       ├── integration
    │       │   └── gateways
    │       │       └── ConvexAuthenticationGateway.spec.ts
    │       └── unit
    │           └── queries
    │               └── GetCurrentUserQueryHandler.spec.ts
    ├── CatalogManagement
    │   ├── application
    │   │   ├── commands
    │   │   │   ├── CreateCatalogCard
    │   │   │   │   ├── CreateCatalogCardCommand.ts
    │   │   │   │   └── CreateCatalogCardCommandHandler.ts
    │   │   │   ├── DeleteCatalogCard
    │   │   │   │   ├── DeleteCatalogCardCommand.ts
    │   │   │   │   └── DeleteCatalogCardCommandHandler.ts
    │   │   │   └── UpdateCatalogCard
    │   │   │       ├── UpdateCatalogCardCommand.ts
    │   │   │       └── UpdateCatalogCardCommandHandler.ts
    │   │   └── ports
    │   │       └── CatalogCardRepository.ts
    │   ├── domain
    │   │   └── CatalogCard
    │   │       └── CatalogCard.ts
    │   ├── infrastructure
    │   │   └── repositories
    │   │       └── CatalogCard
    │   │           └── ConvexCatalogCardRepository.ts
    │   └── specs
    │       ├── helpers
    │       │   └── createFakeCatalogCard.ts
    │       └── integration
    │           └── api
    │               └── mutations
    │                   ├── createCatalogCard.spec.ts
    │                   ├── deleteCatalogCard.spec.ts
    │                   └── updateCatalogCard.spec.ts
    ├── DeckBuilding
    │   ├── application
    │   │   ├── commands
    │   │   │   ├── SaveDeck
    │   │   │   │   ├── SaveDeckCommand.ts
    │   │   │   │   └── SaveDeckCommandHandler.ts
    │   │   │   └── UpdateDeck
    │   │   │       ├── UpdateDeckCommand.ts
    │   │   │       └── UpdateDeckCommandHandler.ts
    │   │   ├── ports
    │   │   │   ├── CatalogCardListRepository.ts
    │   │   │   ├── DeckBuilderSettingsRepository.ts
    │   │   │   ├── DeckListRepository.ts
    │   │   │   ├── DeckRepository.ts
    │   │   │   ├── LoadCatalogCardsByGameIdPresenter.ts
    │   │   │   ├── LoadDeckBuilderSettingsByIdPresenter.ts
    │   │   │   ├── LoadDeckByIdPresenter.ts
    │   │   │   └── LoadDecksByUserIdAndGameIdPresenter.ts
    │   │   └── queries
    │   │       ├── LoadCatalogCardsByGameId
    │   │       │   ├── LoadCatalogCardsByGameIdQuery.ts
    │   │       │   └── LoadCatalogCardsByGameIdQueryHandler.ts
    │   │       ├── LoadDeckBuilderSettingsById
    │   │       │   ├── LoadDeckBuilderSettingsByIdQuery.ts
    │   │       │   └── LoadDeckBuilderSettingsByIdQueryHandler.ts
    │   │       ├── LoadDeckById
    │   │       │   ├── LoadDeckByIdQuery.ts
    │   │       │   └── LoadDeckByIdQueryHandler.ts
    │   │       └── LoadDecksByUserIdAndGameId
    │   │           ├── LoadDecksByUserIdAndGameIdQuery.ts
    │   │           ├── LoadDecksByUserIdAndGameIdQueryHandler.ts
    │   │           └── LoadDecksByUserIdAndGameIdResponse.ts
    │   ├── domain
    │   │   ├── CardData
    │   │   │   └── CardData.ts
    │   │   ├── Catalog
    │   │   │   ├── CatalogCard.ts
    │   │   │   └── CatalogCardList.ts
    │   │   ├── Deck
    │   │   │   ├── Deck.ts
    │   │   │   ├── DeckList.ts
    │   │   │   └── errors
    │   │   │       └── DeckNotOwnedError.ts
    │   │   └── DeckBuilderSettings
    │   │       └── DeckBuilderSettings.ts
    │   ├── infrastructure
    │   │   └── repositories
    │   │       ├── CatalogCardList
    │   │       │   ├── ConvexCatalogCardListRepository.ts
    │   │       │   ├── InMemoryCatalogCardListRepository.ts
    │   │       │   └── InMemoryFailingCatalogCardListRepository.ts
    │   │       ├── Deck
    │   │       │   ├── ConvexDeckReadRepository.ts
    │   │       │   ├── ConvexDeckRepository.ts
    │   │       │   ├── InMemoryDeckRepository.ts
    │   │       │   └── InMemoryFailingDeckRepository.ts
    │   │       ├── DeckBuilderSettings
    │   │       │   ├── ConvexDeckBuilderSettingsRepository.ts
    │   │       │   ├── InMemoryDeckBuilderSettingsRepository.ts
    │   │       │   └── InMemoryFailingDeckBuilderSettingsRepository.ts
    │   │       └── DeckList
    │   │           ├── ConvexDeckListRepository.ts
    │   │           ├── InMemoryDeckListRepository.ts
    │   │           └── InMemoryFailingDeckListRepository.ts
    │   ├── presentation
    │   │   ├── presenters
    │   │   │   ├── LoadCatalogCardsByGameIdWebPresenter.ts
    │   │   │   ├── LoadDeckBuilderSettingsByIdWebPresenter.ts
    │   │   │   ├── LoadDeckByIdWebPresenter.ts
    │   │   │   └── LoadDecksByUserIdAndGameIdWebPresenter.ts
    │   │   └── viewModels
    │   │       ├── LoadCatalogCardsByGameIdViewModel.ts
    │   │       ├── LoadDeckBuilderSettingsByIdViewModel.ts
    │   │       ├── LoadDeckByIdViewModel.ts
    │   │       └── LoadDecksByUserIdAndGameIdViewModel.ts
    │   └── specs
    │       ├── helpers
    │       │   ├── createDeckBuilderSettings.ts
    │       │   ├── createFakeDeck.ts
    │       │   ├── createFakeGameSettings.ts
    │       │   ├── dtos
    │       │   │   ├── GameSettingsDTO.ts
    │       │   │   └── deckDTO.ts
    │       │   └── fakes
    │       │       └── fakeDecks.ts
    │       ├── integration
    │       │   └── api
    │       │       ├── mutations
    │       │       │   ├── saveDeck.spec.ts
    │       │       │   └── updateDeck.spec.ts
    │       │       └── queries
    │       │           ├── loadAvailableFilters.spec.ts
    │       │           ├── loadCatalogCardsByGameId.spec.ts
    │       │           ├── loadDeckBuilderSettingsByGameId.spec.ts
    │       │           ├── loadDeckById.spec.ts
    │       │           ├── loadDecksByUserIdAndGameId.spec.ts
    │       │           └── loadGameSettingsByGameId.spec.ts
    │       └── unit
    │           ├── presenters
    │           │   ├── LoadCatalogCardsByGameIdWebPresenter.spec.ts
    │           │   ├── LoadDeckBuilderSettingsByIdWebPresenter.spec.ts
    │           │   ├── LoadDeckByIdWebPresenter.spec.ts
    │           │   └── LoadDecksByUserIdAndGameIdWebPresenter.spec.ts
    │           ├── queries
    │           │   ├── LoadCatalogCardsByGameIdQueryHandler.spec.ts
    │           │   ├── LoadDeckBuilderSettingsByIdQueryHandler.spec.ts
    │           │   ├── LoadDeckByIdQueryHandler.spec.ts
    │           │   └── LoadDecksByUserIdAndGameIdQueryHandler.spec.ts
    │           ├── saveDeckCommandHandler.spec.ts
    │           └── updateDeckCommandHandler.spec.ts
    ├── GameManagement
    │   ├── application
    │   │   ├── commands
    │   │   │   ├── CreateGame
    │   │   │   │   ├── CreateGameCommand.ts
    │   │   │   │   └── CreateGameCommandHandler.ts
    │   │   │   ├── DeleteGame
    │   │   │   │   ├── DeleteGameCommand.ts
    │   │   │   │   └── DeleteGameCommandHandler.ts
    │   │   │   └── UpdateGame
    │   │   │       ├── UpdateGameCommand.ts
    │   │   │       └── UpdateGameCommandHandler.ts
    │   │   └── ports
    │   │       └── GameRepository.ts
    │   ├── domain
    │   │   └── Game
    │   │       ├── Game.ts
    │   │       └── errors
    │   │           └── GameNotOwnedError.ts
    │   ├── infrastructure
    │   │   └── repositories
    │   │       └── Game
    │   │           └── ConvexGameRepository.ts
    │   └── specs
    │       └── integration
    │           └── api
    │               └── mutations
    │                   ├── createGame.spec.ts
    │                   ├── deleteGame.spec.ts
    │                   └── updateGame.spec.ts
    ├── Gaming
    │   ├── application
    │   │   ├── commands
    │   │   │   ├── GameDeck
    │   │   │   │   └── CreateGameDeck
    │   │   │   │       ├── CreateGameDeckCommand.ts
    │   │   │   │       └── CreateGameDeckCommandHandler.ts
    │   │   │   └── Match
    │   │   │       └── LeaveMatch
    │   │   │           ├── LeaveMatchCommand.ts
    │   │   │           └── LeaveMatchCommandHandler.ts
    │   │   ├── ports
    │   │   │   ├── GameDeckRepository.ts
    │   │   │   ├── GameFilterListRepository.ts
    │   │   │   ├── GameListRepository.ts
    │   │   │   ├── GameRepository.ts
    │   │   │   ├── GameSettingsRepository.ts
    │   │   │   ├── LoadGameByIdPresenter.ts
    │   │   │   ├── LoadGameDeckByIdPresenter.ts
    │   │   │   ├── LoadGameFilterListPresenter.ts
    │   │   │   ├── LoadGameListPresenter.ts
    │   │   │   ├── LoadGameSettingsByGameIdPresenter.ts
    │   │   │   ├── LoadMatchByIdPresenter.ts
    │   │   │   ├── LoadMatchCreatedEventPresenter.ts
    │   │   │   ├── LoadMatchEndedEventPresenter.ts
    │   │   │   ├── LoadMatchEventsPresenter.ts
    │   │   │   ├── MatchEndedEventRepository.ts
    │   │   │   ├── MatchEventListRepository.ts
    │   │   │   ├── MatchMakingEventRepository.ts
    │   │   │   ├── MatchReadRepository.ts
    │   │   │   └── MatchRepository.ts
    │   │   └── queries
    │   │       ├── LoadGameById
    │   │       │   ├── LoadGameByIdQuery.ts
    │   │       │   └── LoadGameByIdQueryHandler.ts
    │   │       ├── LoadGameDeckById
    │   │       │   ├── LoadGameDeckByIdQuery.ts
    │   │       │   └── LoadGameDeckByIdQueryHandler.ts
    │   │       ├── LoadGameFilterList
    │   │       │   ├── LoadGameFilterListQuery.ts
    │   │       │   └── LoadGameFilterListQueryHandler.ts
    │   │       ├── LoadGameList
    │   │       │   ├── LoadGameListQuery.ts
    │   │       │   └── LoadGameListQueryHandler.ts
    │   │       ├── LoadGameSettingsByGameId
    │   │       │   ├── LoadGameSettingsByGameIdQuery.ts
    │   │       │   └── LoadGameSettingsByGameIdQueryHandler.ts
    │   │       ├── LoadMatchById
    │   │       │   ├── LoadMatchByIdQuery.ts
    │   │       │   └── LoadMatchByIdQueryHandler.ts
    │   │       ├── LoadMatchCreatedEvent
    │   │       │   ├── LoadMatchCreatedEventQuery.ts
    │   │       │   └── LoadMatchCreatedEventQueryHandler.ts
    │   │       ├── LoadMatchEndedEvent
    │   │       │   ├── LoadMatchEndedEventQuery.ts
    │   │       │   └── LoadMatchEndedEventQueryHandler.ts
    │   │       └── LoadMatchEvents
    │   │           ├── LoadMatchEventsQuery.ts
    │   │           └── LoadMatchEventsQueryHandler.ts
    │   ├── domain
    │   │   ├── Game
    │   │   │   ├── Game.ts
    │   │   │   └── GameList.ts
    │   │   ├── GameCard
    │   │   │   └── GameCard.ts
    │   │   ├── GameDeck
    │   │   │   └── GameDeck.ts
    │   │   ├── GameFilter
    │   │   │   ├── GameFilter.ts
    │   │   │   └── GameFilterList.ts
    │   │   ├── GameSettings
    │   │   │   └── GameSettings.ts
    │   │   ├── Match
    │   │   │   ├── Match.ts
    │   │   │   ├── MatchCreatedEvent.ts
    │   │   │   └── errors
    │   │   │       ├── MatchAlreadyFinishedError.ts
    │   │   │       └── MatchNotFoundError.ts
    │   │   ├── MatchEvent
    │   │   │   ├── MatchEndedEvent.ts
    │   │   │   ├── MatchEvent.ts
    │   │   │   └── MatchEventList.ts
    │   │   └── MatchMakingEvent
    │   │       └── MatchMakingEventList.ts
    │   ├── infrastructure
    │   │   └── repositories
    │   │       ├── Game
    │   │       │   ├── ConvexGameRepository.ts
    │   │       │   ├── InMemoryFailingGameRepository.ts
    │   │       │   └── InMemoryGameRepository.ts
    │   │       ├── GameDeck
    │   │       │   ├── InMemoryFailingGameDeckRepository.ts
    │   │       │   └── InMemoryGameDeckRepository.ts
    │   │       ├── GameFilterList
    │   │       │   ├── ConvexGameFilterListRepository.ts
    │   │       │   ├── InMemoryFailingGameFilterListRepository.ts
    │   │       │   └── InMemoryGameFilterListRepository.ts
    │   │       ├── GameList
    │   │       │   ├── ConvexGameListRepository.ts
    │   │       │   ├── InMemoryFailingGameListRepository.ts
    │   │       │   └── InMemoryGameListRepository.ts
    │   │       ├── GameSettings
    │   │       │   ├── ConvexGameSettingsRepository.ts
    │   │       │   ├── InMemoryFailingGameSettingsRepository.ts
    │   │       │   └── InMemoryGameSettingsRepository.ts
    │   │       ├── Match
    │   │       │   ├── ConvexMatchReadRepository.ts
    │   │       │   ├── ConvexMatchRepository.ts
    │   │       │   ├── InMemoryFailingMatchRepository.ts
    │   │       │   └── InMemoryMatchRepository.ts
    │   │       ├── MatchEndedEvent
    │   │       │   ├── ConvexMatchEndedEventRepository.ts
    │   │       │   ├── InMemoryFailingMatchEndedEventRepository.ts
    │   │       │   └── InMemoryMatchEndedEventRepository.ts
    │   │       ├── MatchEventList
    │   │       │   ├── ConvexMatchEventListRepository.ts
    │   │       │   ├── InMemoryFailingMatchEventListRepository.ts
    │   │       │   └── InMemoryMatchEventListRepository.ts
    │   │       └── MatchMakingEvent
    │   │           ├── ConvexMatchMakingEventRepository.ts
    │   │           ├── InMemoryFailingMatchMakingEventRepository.ts
    │   │           └── InMemoryMatchMakingEventRepository.ts
    │   ├── presentation
    │   │   ├── presenters
    │   │   │   ├── LoadGameByIdWebPresenter.ts
    │   │   │   ├── LoadGameFilterListWebPresenter.ts
    │   │   │   ├── LoadGameListWebPresenter.ts
    │   │   │   ├── LoadGameSettingsByGameIdWebPresenter.ts
    │   │   │   ├── LoadMatchByIdWebPresenter.ts
    │   │   │   ├── LoadMatchCreatedEventWebPresenter.ts
    │   │   │   ├── LoadMatchEndedEventWebPresenter.ts
    │   │   │   └── LoadMatchEventsWebPresenter.ts
    │   │   └── viewModels
    │   │       ├── LoadGameByIdViewModel.ts
    │   │       ├── LoadGameFilterListViewModel.ts
    │   │       ├── LoadGameListViewModel.ts
    │   │       ├── LoadGameSettingsByGameIdViewModel.ts
    │   │       ├── LoadMatchByIdViewModel.ts
    │   │       ├── LoadMatchCreatedEventViewModel.ts
    │   │       ├── LoadMatchEndedEventViewModel.ts
    │   │       └── LoadMatchEventsViewModel.ts
    │   └── specs
    │       ├── integration
    │       │   └── api
    │       │       ├── mutations
    │       │       │   └── leaveMatch.spec.ts
    │       │       └── queries
    │       │           └── loadMatchById.spec.ts
    │       └── unit
    │           ├── createGameDeckCommandHandler.spec.ts
    │           ├── leaveMatchCommandHandler.spec.ts
    │           ├── presenters
    │           │   ├── LoadAvailableFiltersWebPresenter.spec.ts
    │           │   ├── LoadGameByIdWebPresenter.spec.ts
    │           │   ├── LoadGameListWebPresenter.spec.ts
    │           │   ├── LoadGameSettingsByGameIdWebPresenter.spec.ts
    │           │   ├── LoadMatchByIdWebPresenter.spec.ts
    │           │   ├── LoadMatchCreatedEventWebPresenter.spec.ts
    │           │   ├── LoadMatchEndedEventWebPresenter.spec.ts
    │           │   └── LoadMatchEventsWebPresenter.spec.ts
    │           └── queries
    │               ├── LoadGameByIdQueryHandler.spec.ts
    │               ├── LoadGameDeckByIdQueryHandler.spec.ts
    │               ├── LoadGameFilterListQueryHandler.spec.ts
    │               ├── LoadGameListQueryHandler.spec.ts
    │               ├── LoadGameSettingsByGameIdQueryHandler.spec.ts
    │               ├── LoadMatchByIdQueryHandler.spec.ts
    │               ├── LoadMatchCreatedEventQueryHandler.spec.ts
    │               ├── LoadMatchEndedEventQueryHandler.spec.ts
    │               └── LoadMatchEventsQueryHandler.spec.ts
    ├── MatchMaking
    │   ├── application
    │   │   ├── commands
    │   │   │   └── MatchMaking
    │   │   │       ├── AddPlayerToMatchMakingQueue
    │   │   │       │   ├── AddPlayerToMatchMakingQueueCommand.ts
    │   │   │       │   └── AddPlayerToMatchMakingQueueCommandHandler.ts
    │   │   │       ├── CancelMatchRegistration
    │   │   │       │   ├── CancelMatchRegistrationCommand.ts
    │   │   │       │   └── CancelMatchRegistrationCommandHandler.ts
    │   │   │       ├── CleanUpMatchMakingQueue
    │   │   │       │   ├── CleanUpMatchMakingQueueCommand.ts
    │   │   │       │   └── CleanUpMatchMakingQueueCommandHandler.ts
    │   │   │       ├── MakeMatch
    │   │   │       │   ├── MakeMatchCommand.ts
    │   │   │       │   └── MakeMatchCommandHandler.ts
    │   │   │       ├── UpdatePlayersStatus
    │   │   │       │   ├── UpdatePlayersStatusCommand.ts
    │   │   │       │   └── UpdatePlayersStatusCommandHandler.ts
    │   │   │       └── UpdateSinglePlayerStatus
    │   │   │           ├── UpdateSinglePlayerStatusCommand.ts
    │   │   │           └── UpdateSinglePlayerStatusCommandHandler.ts
    │   │   └── ports
    │   │       └── MatchmakingQueueRepository.ts
    │   ├── domain
    │   │   ├── MatchmakingQueue
    │   │   │   ├── MatchmakingQueue.ts
    │   │   │   └── MatchmakingQueueItem.ts
    │   │   └── errors
    │   │       └── PlayerAlreadyInQueueError.ts
    │   ├── infrastructure
    │   │   └── repositories
    │   │       └── MatchmakingQueue
    │   │           ├── ConvexMatchmakingQueueRepository.ts
    │   │           └── InMemoryMatchmakingQueueRepository.ts
    │   └── specs
    │       ├── integration
    │       │   └── api
    │       │       └── mutations
    │       │           ├── addPlayerToMatchMakingQueue.spec.ts
    │       │           └── cancelMatchRegistration.spec.ts
    │       └── unit
    │           ├── AddPlayerToMatchMakingQueueCommandHandler.spec.ts
    │           ├── CancelMatchRegistrationCommandHandler.spec.ts
    │           ├── CleanUpMatchMakingQueueCommandHandler.spec.ts
    │           ├── MakeMatchCommandHandler.spec.ts
    │           ├── UpdatePlayersStatusCommandHandler.spec.ts
    │           └── UpdateSinglePlayerStatusCommandHandler.spec.ts
    └── Shared
        ├── DependencyInjection.ts
        ├── application
        │   ├── ports
        │   │   ├── CryptoPort.ts
        │   │   ├── EventBus.ts
        │   │   ├── IdentityProvider.ts
        │   │   └── TimeService.ts
        │   └── queries
        │       └── Debug
        │           ├── LoadMatchEventsForMatchQuery.ts
        │           ├── LoadMatchEventsForMatchQueryHandler.ts
        │           ├── LoadMatchmakingEventsForGameQuery.ts
        │           └── LoadMatchmakingEventsForGameQueryHandler.ts
        ├── infrastructure
        │   ├── gateways
        │   │   └── Context
        │   │       ├── ConvexEventBus.ts
        │   │       └── FakeEventBus.ts
        │   ├── providers
        │   │   ├── IdentityProvider
        │   │   │   ├── FakeIdentityProvider.ts
        │   │   │   └── UuidIdentityProvider.ts
        │   │   └── Time
        │   │       ├── FakeTimeService.ts
        │   │       └── RealTimeService.ts
        │   └── seeds
        │       └── cards.jsonl
        └── specs
            └── helpers
                ├── fakes
                │   ├── GameDTO.ts
                │   ├── fakeCatalogCards.ts
                │   ├── fakeGameFilters.ts
                │   └── fakeGames.ts
                ├── requests
                │   ├── createFakeGame.ts
                │   └── getAllFrom.ts
                └── utils
                    └── TestingContainer.ts

486 directories, 648 files
```

## Common AI Assistant Tasks

### Task: Adding a New Feature to DeckBuilding

**Example**: Adding ability to favorite cards

1. **Domain First**: Create value object in `src/client/DeckBuilding/domain/`
2. **Command**: Create `addCardToFavorites` in `application/commands/`
3. **Query**: Create `getFavoriteCards` selector in `application/queries/`
4. **UI**: Add component in `infrastructure/components/app/DeckBuilding/`
5. **Tests**: Write acceptance tests in `specs/unit/commands/`

### Task: Adding a New Component

**Example**: Card hover preview

1. **Location**: `src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/CardPreview/`
2. **Structure**: 
   - `CardPreview.tsx` - Main component
   - `useCardPreview.ts` - Hook with no logic (just glue)
3. **Integration**: Use existing selectors, dispatch thunks for actions
4. **Testing**: Component test in `specs/components/`

### Task: Debugging Test Failures

1. **Run specific test**: `npm test -- path/to/test.spec.ts`
2. **Check imports**: Ensure `@/` alias usage
3. **Verify mocks**: Check service implementations (Fake vs Convex)
4. **Boundary violations**: ESLint rules enforce architecture constraints

### Task: Adding Server-Side Query

**Example**: Loading deck statistics

1. **Domain**: Create entity in `src/server/DeckBuilding/domain/`
2. **Query**: Create query handler in `application/queries/`
3. **Repository**: Create port in `application/ports/`
4. **Implementation**: Create Convex repository in `infrastructure/repositories/`
5. **Presenter**: Create web presenter in `presentation/presenters/`
6. **API**: Add Convex query function
7. **Tests**: Integration test in `specs/integration/`

## Best Practices for AI Assistants

### Code Generation Guidelines

- **Always** read existing similar files before creating new ones
- **Follow** existing patterns exactly (imports, structure, naming)
- **Use** TypeScript strictly - leverage existing types
- **Respect** layer boundaries - domain ← application ← infrastructure
- **Write** tests first when doing TDD - Red-Green-Refactor cycle
- **NEVER** add comments except `// Arrange`, `// Act`, `// Assert` in tests
- **Apply SOLID principles** strictly in all code generation
- **Extract until drop** - continuously refactor for maximum clarity
- **Use full descriptive names** - no abbreviations allowed
- **Focus on behavior** - express what, not how
- **Justify every line** - if not tested, remove it
- **Encapsulate dependencies** - wrap third-party libraries

### Navigation Efficiency

- **Start broad**: Use `LS` on bounded context directories
- **Narrow down**: Use `Grep` with specific patterns
- **Read examples**: Look at similar existing implementations
- **Understand context**: Read related domain files first

### Testing Strategy

- **Sociable tests**: Test through command/query handlers
- **Avoid** testing domain directly
- **One action** per test in `// Act` section
- **Use** `describe("When ...")` for context
- **Start** `it` statements with "should"

### Architecture Compliance

- **Check boundaries**: ESLint will catch violations
- **Use interfaces**: Import from ports, not implementations
- **Follow dependency direction**: Always depend inward
- **Separate concerns**: Keep React components humble (no logic)

### Debugging Workflow

1. **Read error messages** carefully
2. **Check file paths** and imports
3. **Verify service registration** in DI container
4. **Run linting**: `npm run lint`
5. **Run specific tests**: Focus on failing area

