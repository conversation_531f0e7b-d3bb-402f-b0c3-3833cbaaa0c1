type ErrorLike = { message?: string; stack?: string }

type TaskResult = {
  state?: 'pass' | 'fail' | 'skip' | 'only' | 'todo'
  errors?: ErrorLike[]
}

type BaseTask = {
  type: 'suite' | 'test' | 'custom'
  name?: string
  result?: TaskResult
}

type Task = BaseTask & {
  tasks?: Task[]
  file?: { name?: string; filepath?: string }
  filepath?: string
}

type FileTask = {
  type: 'suite'
  name?: string
  tasks?: Task[]
  filepath?: string
}

type Writer = (s: string) => void

type Options = {
  failFast: boolean
  compact: boolean
  write: Writer
}

type Ctx = {
  state: {
    getFiles: () => FileTask[]
  }
  config: {
    bail?: number
  }
}

type Reporter = {
  onInit?: (ctx: unknown) => void
  onTestFail?: (task: Task, error: unknown) => void
  onFinished?: () => void
}

const isTest = (t: Task) => t.type === 'test'
const isFailed = (t: Task) => t.result?.state === 'fail'
const firstError = (t: Task) => t.result?.errors?.[0]
const testName = (t: Task) => t.name ?? 'Unnamed test'

function* walk(node: Task | FileTask): Generator<Task> {
  if ((node as Task).type) yield node as Task
  for (const child of (node as Task).tasks ?? []) yield* walk(child)
}

const fileOf = (t: Task): string =>
  t.file?.name ?? t.file?.filepath ?? t.filepath ?? 'unknown'

const groupByFile = (tasks: Task[]) => {
  const map = new Map<string, Task[]>()
  for (const t of tasks) {
    const key = fileOf(t)
    const arr = map.get(key)
    if (arr) arr.push(t)
    else map.set(key, [t])
  }
  return map
}

const collectAllTests = (files: FileTask[]) =>
  files.flatMap(f => Array.from(walk(f))).filter(isTest)

const collectFailed = (files: FileTask[]) =>
  files.flatMap(f => Array.from(walk(f))).filter(t => isTest(t) && isFailed(t))

const readOptions = (ctx: Ctx, write: Writer): Options => ({
  failFast: process.env.FAIL_FAST === '1' || ctx.config.bail === 1,
  compact: process.env.COMPACT === '1',
  write,
})

const formatFailureHeader = (file: string) => `\nFAIL ${file}\n`
const formatFailureLine = (t: Task, compact: boolean) => {
  const err = firstError(t)
  const msg = err?.message ?? 'Test failed'
  const stack = compact ? '' : (err?.stack ? `\n${err.stack}\n` : '\n')
  return `  ✖ ${testName(t)}\n    ${msg}${stack}`
}
const formatSummary = (passed: number, failed: number, total: number) =>
  `\nSummary: ${passed} passed, ${failed} failed, ${total} total\n`

const printFailures = (failed: Task[], compact: boolean, write: Writer) => {
  const grouped = groupByFile(failed)
  for (const [file, tests] of grouped) {
    write(formatFailureHeader(file))
    for (const t of tests) write(formatFailureLine(t, compact))
  }
}

export default class MinimalFailReporter implements Reporter {
  private ctx!: Ctx
  private opts!: Options
  private buffer: Task[] = []

  onInit(ctx: unknown) {
    this.ctx = ctx as Ctx
    this.opts = readOptions(this.ctx, (s) => process.stdout.write(s))
    this.opts.write('🕥 Running...\n')
  }

  onTestFail(task: Task) {
    this.buffer.push(task)
    printFailures([task], this.opts.compact, this.opts.write)
  }

  onFinished() {
    const files = this.ctx.state.getFiles()
    const failedFromState = collectFailed(files)
    const allFailed = this.buffer.length ? this.buffer : failedFromState
    const total = collectAllTests(files).length
    const passed = total - allFailed.length

    if (allFailed.length > 0) {
      if (this.buffer.length > 1) {
        const extras = this.buffer.slice(1)
        printFailures(extras, this.opts.compact, this.opts.write)
      } else if (this.buffer.length === 0) {
        printFailures(allFailed, this.opts.compact, this.opts.write)
      }
    }

    this.opts.write(formatSummary(passed, allFailed.length, total))
  }
}
