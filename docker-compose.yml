services:
  backend:
    image: ghcr.io/get-convex/convex-backend:00bd92723422f3bff968230c94ccdeb8c1719832
    stop_grace_period: 10s
    stop_signal: SIGINT
    ports:
      - "${PORT:-3210}:3210"
      - "${SITE_PROXY_PORT:-3211}:3211"
    volumes:
      - data:/convex/data
    environment:
      - INSTANCE_NAME
      - INSTANCE_SECRET
      - CONVEX_RELEASE_VERSION_DEV
      - ACTIONS_USER_TIMEOUT_SECS
      - CONVEX_CLOUD_ORIGIN=${CONVEX_CLOUD_ORIGIN:-http://127.0.0.1:${PORT:-3210}}
      - CONVEX_SITE_ORIGIN=${CONVEX_SITE_ORIGIN:-http://127.0.0.1:${SITE_PROXY_PORT:-3211}}
      - DATABASE_URL
      - DISABLE_BEACON
      - REDACT_LOGS_TO_CLIENT
      - DO_NOT_REQUIRE_SSL
      - POSTGRES_URL
      - MYSQL_URL
      - RUST_LOG=${RUST_LOG:-info}
      - RUST_BACKTRACE
      - AWS_REGION
      - AWS_ACCESS_KEY_ID
      - AWS_SECRET_ACCESS_KEY
      - AWS_SESSION_TOKEN
      - AWS_S3_FORCE_PATH_STYLE
      - AWS_S3_DISABLE_SSE
      - AWS_S3_DISABLE_CHECKSUMS
      - S3_STORAGE_EXPORTS_BUCKET
      - S3_STORAGE_SNAPSHOT_IMPORTS_BUCKET
      - S3_STORAGE_MODULES_BUCKET
      - S3_STORAGE_FILES_BUCKET
      - S3_STORAGE_SEARCH_BUCKET
      - S3_ENDPOINT_URL

    healthcheck:
      test: curl -f http://localhost:3210/version
      interval: 5s
      start_period: 10s

  dashboard:
    image: ghcr.io/get-convex/convex-dashboard:33cef775a8a6228cbacee4a09ac2c4073d62ed13
    stop_grace_period: 10s
    stop_signal: SIGINT
    ports:
      - "${DASHBOARD_PORT:-6791}:6791"
    environment:
      - NEXT_PUBLIC_DEPLOYMENT_URL=${NEXT_PUBLIC_DEPLOYMENT_URL:-http://127.0.0.1:${PORT:-3210}}
    depends_on:
      backend:
        condition: service_healthy

volumes:
  data: