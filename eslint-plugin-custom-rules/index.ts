import describeNamingConvention from './rules/describe-naming-convention';
import itShouldStartWithShould from './rules/it-should-start-with-should';
import requireArrangeActAssertComments from './rules/require-arrange-act-assert-comments';
import noDuplicateArrangeActAssertComments from './rules/no-duplicate-arrange-act-assert-comments';
import noTextAfterArrangeActAssert from './rules/no-text-after-arrange-act-assert';
import noArrangeActAssertInSetup from './rules/no-arrange-act-assert-in-setup';
import noViMock from './rules/no-vi-mock';

export = {
  rules: {
    'describe-naming-convention': describeNamingConvention,
    'it-should-start-with-should': itShouldStartWithShould,
    'require-arrange-act-assert-comments': requireArrangeActAssertComments,
    'no-duplicate-arrange-act-assert-comments': noDuplicateArrangeActAssertComments,
    'no-text-after-arrange-act-assert': noTextAfterArrangeActAssert,
    'no-arrange-act-assert-in-setup': noArrangeActAssertInSetup,
    'no-vi-mock': noViMock,
  },
};
