import {deckBuilderReducer} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer";
import {deckReducer} from "@/src/client/DeckBuilding/domain/Deck/deckReducer";
import {catalogReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogReducer";
import {catalogReducer as catalogManagementReducer} from '@/src/client/CatalogManagement/domain/Catalog/catalogReducer';
import {catalogFiltersReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogFiltersReducer";
import {catalogSearchReducer} from "@/src/client/DeckBuilding/domain/Catalog/catalogSearchReducer";
import {gameSettingsReducer} from "@/src/client/DeckBuilding/domain/GameSettings/gameSettingsReducer";
import {gameManagementReducer} from '@/src/client/GameManagement/domain/Game/gameReducer';
import {deckBuilderUiReducer} from "@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiReducer";
import {authReducer} from '@/src/client/Authentication/domain/Auth/authReducer';

export const rootReducers = {
  deck: deckReducer,
  deckBuilder: deckBuilderReducer,
  deckBuilderUi: deckBuilderUiReducer,
  catalog: catalogReducer,
  catalogFilters: catalogFiltersReducer,
  catalogSearch: catalogSearchReducer,
  gameSettings: gameSettingsReducer,
  gameManagement: gameManagementReducer,
  catalogManagement: catalogManagementReducer,
  auth: authReducer,
};