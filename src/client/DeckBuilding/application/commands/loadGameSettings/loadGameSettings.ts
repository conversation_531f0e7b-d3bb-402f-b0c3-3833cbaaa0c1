import {
  gameSettingsLoadedEvent,
  gameSettingsLoadingFailedEvent,
  gameSettingsLoadingStartedEvent,
} from '@/src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents';
import {LoadGameSettingsRequest} from './loadGameSettingsRequest';
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export const loadGameSettings = (request: LoadGameSettingsRequest) => (dispatch: AppDispatch): void => {
  if (!request) {
    dispatch(gameSettingsLoadingStartedEvent());
    return;
  }

  if (request.error) {
    dispatch(gameSettingsLoadingFailedEvent({error: request.error}));
    return;
  }

  const settings = {maxCardsInDeck: request.data!.maxCardsInDeck, startingHandSize: request.data!.startingHandSize};
  dispatch(gameSettingsLoadedEvent({gameSettings: settings}));
};
