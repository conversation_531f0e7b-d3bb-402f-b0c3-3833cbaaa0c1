import {createAsyncThunk} from '@reduxjs/toolkit';
import {
  gameSettingsLoadedEvent,
  gameSettingsLoadingFailedEvent,
  gameSettingsLoadingStartedEvent,
} from '@/src/client/DeckBuilding/domain/GameSettings/gameSettingsEvents';
import {RootState} from '@/src/client/Shared/store/appStore/rootState';
import {ThunkExtra} from '@/src/client/Shared/store/appStore/thunkExtra';
import {
  LoadGameSettingsByGameIdRequest
} from "@/src/client/DeckBuilding/application/commands/loadGameSettingsByGameId/loadGameSettingsByGameIdRequest";

export const loadGameSettingsByGameId = createAsyncThunk<
  void,
  LoadGameSettingsByGameIdRequest,
  { state: RootState; extra: ThunkExtra }
>('gameSettings/loadByGameId', async ({gameId}, {dispatch, extra: {gameSettingsService}}) => {
  dispatch(gameSettingsLoadingStartedEvent());
  try {
    const result = await gameSettingsService.loadSettingsByGameId(gameId);
    if (!result) {
      return;
    }
    const settings = {maxCardsInDeck: result.maxCardsInDeck, startingHandSize: result.startingHandSize};
    dispatch(gameSettingsLoadedEvent({gameSettings: settings}));
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error';
    dispatch(gameSettingsLoadingFailedEvent({error: message}));
  }
});
