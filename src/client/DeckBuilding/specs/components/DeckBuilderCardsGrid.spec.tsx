import {render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import {PathParamsContext, SearchParamsContext} from 'next/dist/shared/lib/hooks-client-context.shared-runtime';
import {createFakeDeckBuilderCardsGridHook} from '@/src/client/DeckBuilding/specs/fakes/CreateFakeDeckBuilderCardsGridHook';

beforeAll(() => {
  global.ResizeObserver = class { observe() {} unobserve() {} disconnect() {} } as unknown as typeof ResizeObserver;
});

import DeckBuilderCardsGrid from '@/src/client/DeckBuilding/infrastructure/components/DeckBuilderCardsGrid/DeckBuilderCardsGrid';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';

describe('DeckBuilderCardsGrid', () => {
  describe('When switching view', () => {
    it('should switch to the selected view', async () => {
      // Arrange
      const useDeckBuilderCardsGridHook = createFakeDeckBuilderCardsGridHook({
        viewControl: { view: 'catalog', switchView: vi.fn(), name: 'Test Deck', tags: ['aggro'], cardCount: 0 },
        search: { localSearch: '', setLocalSearch: vi.fn() },
        virtualGrid: { isLoading: false, cardRows: [], columnCount: 3, containerRef: { current: null }, virtualRows: [], totalSize: 0 },
        cardOperations: { locale: 'en', quantities: new Map<string, number>(), addCardToDeck: vi.fn(), removeCard: vi.fn(), showCardDetails: vi.fn() },
        deckManagement: { name: 'Test Deck', saveNameChange: vi.fn(), addTag: vi.fn() }
      });
      const store = createTestingStore();

      // Act
      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid useDeckBuilderCardsGridHook={useDeckBuilderCardsGridHook} />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Assert
      expect(screen.getByRole('radio', {name: 'Catalog'})).toBeTruthy();
    });
  });

  describe('When the view is deck', () => {
    it('should display the deck name', () => {
      // Arrange
      const useDeckBuilderCardsGridHook = createFakeDeckBuilderCardsGridHook({
        viewControl: { view: 'deck', switchView: vi.fn(), name: 'Test Deck', tags: ['aggro'], cardCount: 0 },
        search: { localSearch: '', setLocalSearch: vi.fn() },
        virtualGrid: { isLoading: false, cardRows: [], columnCount: 3, containerRef: { current: null }, virtualRows: [], totalSize: 0 },
        cardOperations: { locale: 'en', quantities: new Map<string, number>(), addCardToDeck: vi.fn(), removeCard: vi.fn(), showCardDetails: vi.fn() },
        deckManagement: { name: 'Test Deck', saveNameChange: vi.fn(), addTag: vi.fn() }
      });
      const store = createTestingStore();

      // Act
      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid useDeckBuilderCardsGridHook={useDeckBuilderCardsGridHook} />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Assert
      expect(screen.getByText('Test Deck')).toBeTruthy();
    });

    it('should display the deck tags', () => {
      // Arrange
      const useDeckBuilderCardsGridHook = createFakeDeckBuilderCardsGridHook({
        viewControl: { view: 'deck', switchView: vi.fn(), name: 'Test Deck', tags: ['aggro'], cardCount: 0 },
        search: { localSearch: '', setLocalSearch: vi.fn() },
        virtualGrid: { isLoading: false, cardRows: [], columnCount: 3, containerRef: { current: null }, virtualRows: [], totalSize: 0 },
        cardOperations: { locale: 'en', quantities: new Map<string, number>(), addCardToDeck: vi.fn(), removeCard: vi.fn(), showCardDetails: vi.fn() },
        deckManagement: { name: 'Test Deck', saveNameChange: vi.fn(), addTag: vi.fn() }
      });
      const store = createTestingStore();

      // Act
      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid useDeckBuilderCardsGridHook={useDeckBuilderCardsGridHook} />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Assert
      expect(screen.getByText('aggro')).toBeTruthy();
    });

    it('should open the rename dialog when clicking the edit button', () => {
      // Arrange
      const useDeckBuilderCardsGridHook = createFakeDeckBuilderCardsGridHook({
        viewControl: { view: 'deck', switchView: vi.fn(), name: 'Test Deck', tags: ['aggro'], cardCount: 0 },
        search: { localSearch: '', setLocalSearch: vi.fn() },
        virtualGrid: { isLoading: false, cardRows: [], columnCount: 3, containerRef: { current: null }, virtualRows: [], totalSize: 0 },
        cardOperations: { locale: 'en', quantities: new Map<string, number>(), addCardToDeck: vi.fn(), removeCard: vi.fn(), showCardDetails: vi.fn() },
        deckManagement: { name: 'Test Deck', saveNameChange: vi.fn(), addTag: vi.fn() }
      });
      const store = createTestingStore();

      // Act
      render(
        <PathParamsContext.Provider value={{gameId: 'g1'}}>
          <SearchParamsContext.Provider value={new URLSearchParams('locale=en')}>
            <Provider store={store}>
              <DeckBuilderCardsGrid useDeckBuilderCardsGridHook={useDeckBuilderCardsGridHook} />
            </Provider>
          </SearchParamsContext.Provider>
        </PathParamsContext.Provider>
      );

      // Assert
      expect(screen.getByText('Test Deck')).toBeTruthy();
    });
  });
});
