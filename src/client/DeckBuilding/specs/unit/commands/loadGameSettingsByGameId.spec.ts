import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {loadGameSettingsByGameId} from '@/src/client/DeckBuilding/application/commands/loadGameSettingsByGameId/loadGameSettingsByGameId';
import {FakeGameSettingsService} from '@/src/client/DeckBuilding/infrastructure/services/GameSettingsService/FakeGameSettingsService';
import {getGameSettings} from '@/src/client/DeckBuilding/application/queries/getGameSettings/getGameSettings';
import {getGameSettingsError} from '@/src/client/DeckBuilding/application/queries/getGameSettingsError/getGameSettingsError';
import {isGameSettingsLoading} from '@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading';

describe('loadGameSettingsByGameId', () => {
  describe('When the service returns settings', () => {
    it('should store the settings', async () => {
      // Arrange
      const gameSettingsService = new FakeGameSettingsService({maxCardsInDeck: 60, startingHandSize: 7});
      const {dispatch, getState} = createTestingStore({}, {gameSettingsService});

      // Act
      await dispatch(loadGameSettingsByGameId({gameId: 'g1'}));

      // Assert
      expect(getGameSettings(getState())).toEqual({maxCardsInDeck: 60, startingHandSize: 7});
    });
  });

  describe('When dispatching the command', () => {
    it('should set the status to loading while waiting', async () => {
      // Arrange
      const gameSettingsService = new FakeGameSettingsService({maxCardsInDeck: 60, startingHandSize: 7});
      const {dispatch, getState} = createTestingStore({}, {gameSettingsService});

      // Act
      const promise = dispatch(loadGameSettingsByGameId({gameId: 'g1'}));

      // Assert
      expect(isGameSettingsLoading(getState())).toBe(true);
      await promise;
    });
  });

  describe('When the service returns an error', () => {
    it('should store the error', async () => {
      // Arrange
      const gameSettingsService = new FakeGameSettingsService(null, true);
      const {dispatch, getState} = createTestingStore({}, {gameSettingsService});

      // Act
      await dispatch(loadGameSettingsByGameId({gameId: 'g1'}));

      // Assert
      expect(getGameSettingsError(getState())).toBe('Failed to load game settings');
    });
  });
});
