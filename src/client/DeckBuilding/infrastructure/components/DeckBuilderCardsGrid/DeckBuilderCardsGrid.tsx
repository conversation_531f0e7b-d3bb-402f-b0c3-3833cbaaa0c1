'use client';

import {memo} from 'react';
import DeckBuilderCardsGridComponent, {DeckBuilderCardsGridComponentProps} from './DeckBuilderCardsGridComponent';
import {useDeckBuilderCardsGrid} from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckBuilderCardsGrid/useDeckBuilderCardsGrid';

type DeckBuilderCardsGridHook = () => DeckBuilderCardsGridComponentProps;

type Props = {
  useDeckBuilderCardsGridHook?: DeckBuilderCardsGridHook;
};

const DeckBuilderCardsGrid = ({
  useDeckBuilderCardsGridHook = useDeckBuilderCardsGrid
}: Props = {}) => {
  const props = useDeckBuilderCardsGridHook();
  return <DeckBuilderCardsGridComponent {...props} />;
};

export default memo(DeckBuilderCardsGrid);
