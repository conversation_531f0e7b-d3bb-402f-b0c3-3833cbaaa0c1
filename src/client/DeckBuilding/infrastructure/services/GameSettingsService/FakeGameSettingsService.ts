import {GameSettingsService} from '../../../application/ports/GameSettingsService';
import {GameSettings} from '@/src/client/DeckBuilding/domain/GameSettings/GameSettings';

export class FakeGameSettingsService implements GameSettingsService {
  private readonly result: GameSettings | null;
  private readonly shouldThrow: boolean;

  constructor(result: GameSettings | null = {maxCardsInDeck: 60, startingHandSize: 7}, shouldThrow = false) {
    this.result = result;
    this.shouldThrow = shouldThrow;
  }

  async loadSettingsByGameId(): Promise<GameSettings | null> {
    if (this.shouldThrow) {
      throw new Error('Failed to load game settings');
    }
    return this.result;
  }
}
