'use client';

import {FC} from 'react';
import {Texture} from 'three';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';
import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';
import {useTextureCache} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';

type CurrentPlayerHook = (matchId: string) => {
  currentPlayerPosition?: 'player1' | 'player2';
  isLoading: boolean;
  getCurrentPlayerRole: () => string;
  getOpponentRole: () => string;
  isCurrentPlayer: () => boolean;
};

type PlayerHandsHook = (matchId: string) => {
  player1Count?: number;
  player2Count?: number;
  player1?: unknown[];
  player2?: unknown[];
  isLoading: boolean;
};

type TextureCacheHook = () => {
  backTexture: unknown;
};

type Props = {
  matchId: string;
  useCurrentPlayerHook?: CurrentPlayerHook;
  usePlayerHandsHook?: PlayerHandsHook;
  useTextureCacheHook?: TextureCacheHook;
};

const RADIUS = 12.5;
const scale = 0.8;
const CARD_WIDTH = 2 * scale;
const CARD_HEIGHT = 3 * scale;

const OpponentHand: FC<Props> = ({
  matchId,
  useCurrentPlayerHook = useCurrentPlayer,
  usePlayerHandsHook = usePlayerHands,
  useTextureCacheHook = useTextureCache
}) => {
  const {currentPlayerPosition, isLoading: isCurrentPlayerLoading} = useCurrentPlayerHook(matchId);
  const {player1Count = 0, player2Count, isLoading: isPlayerHandsLoading} = usePlayerHandsHook(matchId);
  const {backTexture} = useTextureCacheHook();
  
  const cardCount = currentPlayerPosition === 'player1' ? (player2Count ?? 0) : (player1Count ?? 0);

  if (isCurrentPlayerLoading || isPlayerHandsLoading || !currentPlayerPosition) {
    return null;
  }

  const middleIndex = (cardCount - 1) / 2;

  const maxArc = Math.PI / 2.5;
  const minArc = Math.PI / 10;
  const ARC_WIDTH = (() => {
    if (cardCount === 2) return 0.15;
    if (cardCount === 3) return 0.25;
    return Math.min(maxArc, Math.max(minArc, (cardCount - 1) * 0.1));
  })();

  const angleStep = ARC_WIDTH / (cardCount - 1 || 1);

  return (
    <group position={[-2.5, 14.2, 0]}>
      {Array.from({length: cardCount}, (_, index) => index).map((index: number) => {
        const angle = (index - middleIndex) * angleStep;
        const x = Math.sin(angle) * RADIUS;
        const baseY = -Math.cos(angle) * RADIUS * 0.7;

        return (
          <group
            key={index}
            position={[x, baseY, -15 + index * 0.001]}
            rotation={[0, 0, angle + Math.PI]}
          >
            <mesh
              position={[0, -CARD_HEIGHT / 2, 0]}
            >
              <planeGeometry args={[CARD_WIDTH, CARD_HEIGHT]} />
              <meshBasicMaterial
                map={backTexture as Texture}
                transparent={true}
                depthWrite={false}
                polygonOffset={true}
                polygonOffsetFactor={-1}
              />
            </mesh>
          </group>
        );
      })}
    </group>
  );
};

export default OpponentHand;
