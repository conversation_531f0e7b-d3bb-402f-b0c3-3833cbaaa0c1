import {FC, useState} from "react";
import {Id} from "@/convex/_generated/dataModel";
import {
  LeaveMatchButton
} from "@/src/client/Gaming/infrastructure/components/LeaveMatchButton/LeaveMatchButton";
import {
  CardZoom
} from "@/src/client/Gaming/infrastructure/components/CardZoom/CardZoom";
import PlayersHUD from "@/src/client/Gaming/infrastructure/components/PlayersHUD/PlayersHUD";
import {
  PassTurnButton
} from "@/src/client/Gaming/infrastructure/components/PassTurnButton/PassTurnButton";
import {
  ErrorNotification
} from "@/src/client/Gaming/infrastructure/components/ErrorNotification/ErrorNotification";
import {useGameErrorContext} from "@/src/client/Gaming/infrastructure/providers/GameErrorProvider/useGameErrorContext";
import DebugButton from "@/src/client/Gaming/infrastructure/components/DebugButton/DebugButton";
import DebugEventDialog from "@/src/client/Gaming/infrastructure/components/DebugEventDialog/DebugEventDialog";

type Props = {
  matchId: string;
};

const HUD: FC<Props> = ({matchId}) => {
  const {error, clearError} = useGameErrorContext();
  const [debugDialogOpen, setDebugDialogOpen] = useState(false);

  return (
    <>
      <ErrorNotification message={error} onDismiss={clearError} />
      
      <div style={{
        position: 'absolute',
        bottom: '20px',
        right: '20px',
        zIndex: 10,
        display: 'flex',
        alignItems: 'center',
        gap: '16px'
      }}>
        <PassTurnButton matchId={matchId} />
      </div>

      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 10,
        display: 'flex',
        flexDirection: 'column',
        gap: '12px'
      }}>
        <LeaveMatchButton matchId={matchId as Id<"matches">}/>
        <DebugButton onClick={() => setDebugDialogOpen(true)} />
      </div>

      <DebugEventDialog 
        open={debugDialogOpen}
        onOpenChange={setDebugDialogOpen}
        matchId={matchId as Id<"matches">}
      />

      <PlayersHUD matchId={matchId} />

      <CardZoom matchId={matchId} />
    </>
  );
};

export default HUD;