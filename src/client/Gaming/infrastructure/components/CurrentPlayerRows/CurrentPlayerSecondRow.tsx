'use client';

import {FC, useState, useEffect, useRef} from 'react';
import PreloadedCard from '@/src/client/Gaming/infrastructure/components/PreloadedCard/PreloadedCard';
import AnimatedCard from '@/src/client/Gaming/infrastructure/components/AnimatedCard/AnimatedCard';
import EmptySlot from '@/src/client/Gaming/infrastructure/components/EmptySlot/EmptySlot';
import {useCardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/useCardZoomContext';
import {useTextureCache} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';
import {useBoardState} from '@/src/client/Gaming/infrastructure/hooks/useBoardState/useBoardState';
import {useTurnBasedCursor} from '@/src/client/Gaming/infrastructure/hooks/useTurnBasedCursor/useTurnBasedCursor';

const CARD_Y = -1.380;
const CARD_Z = 3.6;
const CARD_SPACING = 3;
const SLOT_COUNT = 8;

type Props = {
  matchId: string;
  position?: [number, number, number];
  selectedCard?: {cardId: string, image: string} | null;
  placingCard?: {cardId: string, image: string, rowType: string, slotIndex: number} | null;
  onSlotClick?: (slotIndex: number) => void;
  zPosition?: number;
  shouldMirror?: boolean;
};

const HOVER_OFFSET = 0.05;

const CurrentPlayerSecondRow: FC<Props> = ({
  matchId,
  position = [0, 0, 0],
  selectedCard,
  placingCard,
  onSlotClick,
  zPosition,
  shouldMirror = false
}) => {
  const {showZoomedCard} = useCardZoomContext();
  const {currentPlayerPosition, isLoading: isCurrentPlayerLoading} = useCurrentPlayer(matchId);
  const {boardState, isLoading: isBoardLoading} = useBoardState(matchId);
  const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;
  const turnBasedCursor = useTurnBasedCursor({matchId, allowInteraction: !!selectedCard});
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [newlyPlacedCards, setNewlyPlacedCards] = useState(() => new Set<string>());
  const prevBoardStateRef = useRef<typeof boardState>(undefined);
  const cardZ = zPosition ?? CARD_Z;
  const {getTexture, backTexture} = useTextureCache();

  const boardRow = currentPlayerPosition && boardState
    ? (currentPlayerPosition === 'player1' ? boardState.player1Board.secondRow : boardState.player2Board.secondRow)
    : [];

  useEffect(() => {
    if (boardState && prevBoardStateRef.current && currentPlayerPosition) {
      const prevRow = currentPlayerPosition === 'player1' 
        ? prevBoardStateRef.current.player1Board.secondRow 
        : prevBoardStateRef.current.player2Board.secondRow;
      
      const currentRow = currentPlayerPosition === 'player1' 
        ? boardState.player1Board.secondRow 
        : boardState.player2Board.secondRow;

      const newPlacedCards = new Set<string>();
      
      for (let i = 0; i < currentRow.length; i++) {
        const currentCard = currentRow[i];
        const prevCard = prevRow[i];
        
        if (currentCard && !prevCard && !(placingCard && placingCard.slotIndex === i)) {
          newPlacedCards.add(currentCard.cardId);
        }
      }
      
      if (newPlacedCards.size > 0) {
        setNewlyPlacedCards(newPlacedCards);
      }
    }
    
    prevBoardStateRef.current = boardState;
  }, [boardState, currentPlayerPosition, placingCard]);

  if (isCurrentPlayerLoading || isBoardLoading || !currentPlayerPosition) {
    return null;
  }

  const handleSlotClick = (slotIndex: number) => {
    if (selectedCard && boardRow[slotIndex] === null) {
      onSlotClick?.(slotIndex);
    }
  };

  const getXPosition = (index: number) => {
    return shouldMirror
      ? (SLOT_COUNT - 1 - index) * CARD_SPACING - offset
      : index * CARD_SPACING - offset;
  };

  const handleAnimationComplete = (cardId: string) => {
    setNewlyPlacedCards(prev => {
      const newSet = new Set(prev);
      newSet.delete(cardId);
      return newSet;
    });
  };

  return (
    <group position={position}>
      {Array.from({length: SLOT_COUNT}, (_, index) => (
        <group
          key={index}
          onPointerOver={(e) => {
            turnBasedCursor.onPointerOver(e);
            setHoveredIndex(index);
          }}
          onPointerOut={(e) => {
            turnBasedCursor.onPointerOut(e);
            setHoveredIndex(null);
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleSlotClick(index);
          }}
          onContextMenu={(e) => {
            e.stopPropagation();
            if (boardRow[index]) {
              const card = boardRow[index]!;
              const imageUrl = card.image.startsWith('/game-assets/') || card.image.startsWith('http') 
                ? card.image 
                : `/game-assets/cards/en/thumbnail/${card.image.replace('.jpg', '')}.jpg`;
              showZoomedCard(card.cardId, imageUrl);
            }
          }}
          onPointerDown={(e) => {
            e.stopPropagation();
            if (e.button === 2 && boardRow[index]) {
              const card = boardRow[index]!;
              const imageUrl = card.image.startsWith('/game-assets/') || card.image.startsWith('http') 
                ? card.image 
                : `/game-assets/cards/en/thumbnail/${card.image.replace('.jpg', '')}.jpg`;
              showZoomedCard(card.cardId, imageUrl);
            }
          }}
        >
          {boardRow[index] ? (
            newlyPlacedCards.has(boardRow[index]!.cardId) ? (
              <AnimatedCard
                key={`animated-${boardRow[index]!.cardId}`}
                frontTexture={getTexture(boardRow[index]!.image) || backTexture}
                backTexture={backTexture}
                width={2}
                height={2.1}
                position={[
                  getXPosition(index),
                  CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                  cardZ,
                ]}
                visibleHeight={340}
                isNewlyPlaced={true}
                onAnimationComplete={() => handleAnimationComplete(boardRow[index]!.cardId)}
              />
            ) : (
              <PreloadedCard
                key={`placed-${boardRow[index]!.cardId}`}
                frontTexture={getTexture(boardRow[index]!.image) || backTexture}
                backTexture={backTexture}
                width={2}
                height={2.1}
                position={[
                  getXPosition(index),
                  CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                  cardZ,
                ]}
                visibleHeight={340}
              />
            )
          ) : placingCard && placingCard.slotIndex === index ? (
            <AnimatedCard
              key={`placing-${placingCard.cardId}`}
              frontTexture={getTexture(placingCard.image) || backTexture}
              backTexture={backTexture}
              width={2}
              height={2.1}
              position={[
                getXPosition(index),
                CARD_Y,
                cardZ,
              ]}
              visibleHeight={340}
              isNewlyPlaced={true}
            />
          ) : (
            <EmptySlot
              key={`empty-${index}`}
              width={2}
              height={2.1}
              position={[
                getXPosition(index),
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                cardZ,
              ]}
            />
          )}
        </group>
      ))}
    </group>
  );
};

export default CurrentPlayerSecondRow;