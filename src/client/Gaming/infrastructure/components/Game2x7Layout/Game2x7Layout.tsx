import {FC} from "react";
import CurrentPlayerFirstRow from "@/src/client/Gaming/infrastructure/components/CurrentPlayerRows/CurrentPlayerFirstRow";
import CurrentPlayerSecondRow from "@/src/client/Gaming/infrastructure/components/CurrentPlayerRows/CurrentPlayerSecondRow";
import OpponentFirstRow from "@/src/client/Gaming/infrastructure/components/OpponentRows/OpponentFirstRow";
import OpponentSecondRow from "@/src/client/Gaming/infrastructure/components/OpponentRows/OpponentSecondRow";
import PlayerDecks from "@/src/client/Gaming/infrastructure/components/PlayerDecks/PlayerDecks";

type Props = {
  matchId: string;
  selectedCard?: {cardId: string, image: string} | null;
  placingCard?: {cardId: string, image: string, rowType: string, slotIndex: number} | null;
  onSlotClick?: (rowType: string, slotIndex: number) => void;
};

const Game2x7Layout: FC<Props> = ({matchId, selectedCard, placingCard, onSlotClick}) => {
  return (
    <>
      <CurrentPlayerFirstRow
        matchId={matchId}
        selectedCard={selectedCard}
        placingCard={placingCard?.rowType === 'first' ? placingCard : null}
        onSlotClick={(slotIndex) => onSlotClick?.('current-first', slotIndex)}
        zPosition={0.4}
      />
      <CurrentPlayerSecondRow
        matchId={matchId}
        selectedCard={selectedCard}
        placingCard={placingCard?.rowType === 'second' ? placingCard : null}
        onSlotClick={(slotIndex) => onSlotClick?.('current-second', slotIndex)}
        zPosition={3.6}
      />
      <PlayerDecks matchId={matchId} />
      <OpponentFirstRow
        matchId={matchId}
        zPosition={-3.2}
        shouldMirror={true}
      />
      <OpponentSecondRow
        matchId={matchId}
        zPosition={-6.4}
        shouldMirror={true}
      />
    </>
  );
};

export default Game2x7Layout;
