'use client';

import {Text, Card, Flex} from "@radix-ui/themes";
import {useEffect, useState} from "react";

type Props = {
  message: string | null;
  onDismiss: () => void;
  autoHideDelay?: number;
};

export const ErrorNotification = ({message, onDismiss, autoHideDelay = 4000}: Props) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (message) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onDismiss, 300);
      }, autoHideDelay);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [message, autoHideDelay, onDismiss]);

  if (!message) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      left: '50%',
      zIndex: 1000,
      transition: 'all 0.3s ease-in-out',
      transform: isVisible ? 'translate(-50%, 0px)' : 'translate(-50%, -150px)',
      maxWidth: '500px',
      width: '90%',
    }}>
      <Card size="1" className="backdrop-blur-md">
        <Flex align="center" justify="center" gap="2">
          <Text size="3" color="red" weight="bold">
            {message}
          </Text>
        </Flex>
      </Card>
    </div>
  );
};