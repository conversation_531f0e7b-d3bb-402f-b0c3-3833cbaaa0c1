'use client';

import {Button, Flex, Text} from "@radix-ui/themes";
import {useCallback, useState} from "react";
import {useLeaveMatch} from '@/src/client/Gaming/infrastructure/hooks/useLeaveMatch/useLeaveMatch';
import ConfirmGameAlert from "@/src/client/Gaming/infrastructure/components/ConfirmGameAlert/ConfirmGameAlert";

type Props = {
  matchId: string;
};

export const LeaveMatchButton = ({matchId}: Props) => {
  const {leaveMatch} = useLeaveMatch();
  const [error, setError] = useState<string | null>(null);

  const handleLeaveMatch = useCallback(async () => {
    try {
      setError(null);
      await leaveMatch({matchId});
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to leave match';
      setError(errorMessage);
      console.error('Failed to leave match:', err);
    }
  }, [leaveMatch, matchId]);

  return (
    <Flex direction="column" gap="3">
      {error && <Text color="red">{error}</Text>}
      <ConfirmGameAlert
        title="Give up"
        text="Are you sure you want to leave the match?"
        onAccept={handleLeaveMatch}
      >
        <Button
          size="3"
          color="red"
          variant="surface"
        >
          Leave Match
        </Button>
      </ConfirmGameAlert>
    </Flex>
  );
};
