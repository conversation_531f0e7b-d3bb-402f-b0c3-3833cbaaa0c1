'use client';

import {createContext, useContext, ReactNode, useMemo, useState, useEffect, useRef} from 'react';
import {TextureLoader, Texture} from 'three';
import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';
import {useBoardState} from '@/src/client/Gaming/infrastructure/hooks/useBoardState/useBoardState';

type PlayerHandsHook = (matchId: string | undefined) => {
  player1?: Array<{ image?: string }>;
  player2?: Array<{ image?: string }>;
  player1Count?: number;
  player2Count?: number;
  isLoading: boolean;
};

type BoardStateHook = (matchId: string | undefined) => {
  boardState?: {
    player1Board: {
      firstRow: Array<{ image?: string } | null>;
      secondRow: Array<{ image?: string } | null>;
    };
    player2Board: {
      firstRow: Array<{ image?: string } | null>;
      secondRow: Array<{ image?: string } | null>;
    };
  };
  currentTurn?: string;
  gamePhase?: string;
  isLoading: boolean;
  error?: string;
};

interface TextureCacheContextType {
  getTexture: (cardId: string) => Texture | undefined;
  backTexture: Texture;
  isLoading: boolean;
  hasError: boolean;
}

const TextureCacheContext = createContext<TextureCacheContextType | null>(null);

interface Props {
  children: ReactNode;
  matchId?: string;
  usePlayerHandsHook?: PlayerHandsHook;
  useBoardStateHook?: BoardStateHook;
}

export const TextureCacheProvider = ({
  children, 
  matchId, 
  usePlayerHandsHook = usePlayerHands,
  useBoardStateHook = useBoardState
}: Props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const loaderRef = useRef<TextureLoader | null>(null);
  const texturesRef = useRef<Map<string, Texture>>(new Map());
  const [backTexture, setBackTexture] = useState<Texture>(() => new Texture());
  
  const {player1, player2} = usePlayerHandsHook(matchId);
  const {boardState} = useBoardStateHook(matchId);

  const allCardIds = useMemo(() => {
    const cardIds = new Set<string>();

    if (player1) {
      player1
        .filter(card => card && card.image && typeof card.image === 'string' && card.image.trim() !== '')
        .forEach(card => cardIds.add(card.image!.trim()));
    }
    
    if (player2) {
      player2
        .filter(card => card && card.image && typeof card.image === 'string' && card.image.trim() !== '')
        .forEach(card => cardIds.add(card.image!.trim()));
    }
    
    if (boardState) {
      [
        boardState.player1Board.firstRow, 
        boardState.player1Board.secondRow,
        boardState.player2Board.firstRow, 
        boardState.player2Board.secondRow
      ].forEach(row => {
        row.forEach(placedCard => {
          if (placedCard && placedCard.image && placedCard.image.trim() !== '') {
            cardIds.add(placedCard.image.trim());
          }
        });
      });
    }
    
    return Array.from(cardIds);
  }, [player1, player2, boardState]);

  const toUrl = (cardId: string) => {
    if (!cardId) return '';
    if (cardId.startsWith('/game-assets/') || cardId.startsWith('http')) {
      return cardId;
    }
    const clean = cardId.endsWith('.jpg') ? cardId.slice(0, -4) : cardId;
    return `/game-assets/cards/en/thumbnail/${clean}.jpg`;
  };

  const textureUrls = useMemo(() => {
    const set = new Set<string>();
    allCardIds.forEach((id) => set.add(toUrl(id)));
    return Array.from(set).sort();
  }, [allCardIds]);

  useEffect(() => {
    if (!loaderRef.current) {
      loaderRef.current = new TextureLoader();
    }
    const loader = loaderRef.current;
    const backUrl = '/game-assets/cards/back.png';
    if (!texturesRef.current.has(backUrl)) {
      setIsLoading(true);
      loader.load(
        backUrl,
        (tex) => {
          texturesRef.current.set(backUrl, tex);
          setBackTexture(tex);
          setIsLoading(false);
        },
        undefined,
        () => {
          setHasError(true);
          setIsLoading(false);
        },
      );
    } else {
      const existing = texturesRef.current.get(backUrl)!;
      setBackTexture(existing);
    }
  }, []);

  useEffect(() => {
    if (!loaderRef.current) {
      loaderRef.current = new TextureLoader();
    }
    const loader = loaderRef.current;
    let cancelled = false;

    const pending: string[] = [];
    for (const url of textureUrls) {
      if (!texturesRef.current.has(url)) pending.push(url);
    }

    if (pending.length === 0) return;

    setIsLoading(true);
    let remaining = pending.length;

    pending.forEach((url) => {
      loader.load(
        url,
        (tex) => {
          if (cancelled) return;
          texturesRef.current.set(url, tex);
          remaining -= 1;
          if (remaining === 0) {
            setIsLoading(false);
          }
        },
        undefined,
        () => {
          if (cancelled) return;
          setHasError(true);
          remaining -= 1;
          if (remaining === 0) setIsLoading(false);
        },
      );
    });

    return () => {
      cancelled = true;
    };
  }, [textureUrls]);

  return (
    <TextureCacheContext.Provider
      value={{
        getTexture: (cardId: string) => texturesRef.current.get(toUrl(cardId)),
        backTexture,
        isLoading,
        hasError,
      }}
    >
      {children}
    </TextureCacheContext.Provider>
  );
};

export const useTextureCache = () => {
  const context = useContext(TextureCacheContext);
  if (!context) {
    throw new Error('useTextureCache must be used within a TextureCacheProvider');
  }
  return context;
};
