'use client';

import {FC} from 'react';
import {Box, Button, Container, Flex, Heading, Text} from '@radix-ui/themes';
import {useTranslations} from 'next-intl';
import MulliganCard from '../MulliganCard/MulliganCard';
import {useMulliganScreen} from '@/src/client/Gaming/infrastructure/hooks/useMulliganScreen/useMulliganScreen';
import Image from 'next/image';

type Props = {
  matchId: string;
};

const MulliganScreen: FC<Props> = ({matchId}) => {
  const t = useTranslations('games');
  const {
    handCards,
    finalHandCards,
    selectedCardIds,
    isLoading,
    error,
    isSubmitted,
    currentRound,
    maxRounds,
    hasCompletedAllRounds,
    onToggleCard,
    onSkip,
    onSubmit
  } = useMulliganScreen({matchId});

  if (isLoading) {
    return (
      <Container size="3" className="py-8">
        <Flex direction="column" align="center" gap="4">
          <Heading size="6">{t('mulligan.loading')}</Heading>
          <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full" />
        </Flex>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="3" className="py-8">
        <Flex direction="column" align="center" gap="4">
          <Heading size="6" color="red">{t('mulligan.error')}</Heading>
          <Text color="red">{error}</Text>
        </Flex>
      </Container>
    );
  }

  if (isSubmitted || hasCompletedAllRounds) {
    return (
      <Container size="4" className="py-8">
        <Flex direction="column" align="center" gap="6">
          <Flex direction="column" align="center" gap="2">
            <Heading size="6">{t('mulligan.allRoundsComplete')}</Heading>
            <Text>{t('mulligan.waitingForOpponent')}</Text>
            <div className="animate-pulse w-8 h-8 bg-blue-500 rounded-full" />
          </Flex>
          
          <Flex direction="column" align="center" gap="4">
            <Text size="4" weight="medium">{t('mulligan.finalHand')}</Text>
            <div className="grid grid-cols-7 gap-3">
              {finalHandCards.map(card => (
                <div key={card.id} className="w-16 h-22 bg-gray-100 rounded border">
                  <Image
                    width={64}
                    height={88}
                    src={card.imageUrl} 
                    alt={`Card ${card.id}`}
                    className="w-full h-full object-cover rounded"
                  />
                </div>
              ))}
            </div>
          </Flex>
        </Flex>
      </Container>
    );
  }

  if (!handCards || handCards.length === 0) {
    return (
      <Container size="3" className="py-8">
        <Flex direction="column" align="center" gap="4">
          <Heading size="6">{t('mulligan.noCards')}</Heading>
        </Flex>
      </Container>
    );
  }

  return (
    <Box className="py-8">
      <Flex direction="column" align="center" gap="6">
        <Flex direction="column" align="center" gap="2">
          <Heading size="6">{t('mulligan.title')}</Heading>
          <Text size="3" color="gray" className="text-center max-w-md">
            {t('mulligan.description')}
          </Text>
          <Text size="2" color="blue">
            {t('mulligan.roundInfo', {current: currentRound, max: maxRounds})}
          </Text>
          <Text size="2" color="gray">
            {t('mulligan.selectedCount', {selected: selectedCardIds.length, max: 7})}
          </Text>
        </Flex>

        <div className="grid grid-cols-7 gap-4">
          {handCards.map(card => (
            <MulliganCard
              key={card.instanceId}
              cardId={card.id}
              imageUrl={card.imageUrl}
              isSelected={selectedCardIds.includes(card.id)}
              onToggle={onToggleCard}
            />
          ))}
        </div>

        <Flex gap="6" direction="column" className="w-full max-w-md">
          <Button
            size="4"
            variant="outline"
            className="flex-1"
            onClick={onSkip}
            disabled={isLoading}
          >
            {t('mulligan.skip')}
          </Button>
          <Button
            size="4"
            color="red"
            className="flex-1"
            onClick={onSubmit}
            disabled={isLoading || selectedCardIds.length === 0}
          >
            {t('mulligan.submit')}
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};

export default MulliganScreen;
