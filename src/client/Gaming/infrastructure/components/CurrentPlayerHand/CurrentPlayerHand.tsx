'use client';

import {FC, useState} from 'react';
import {useCardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/useCardZoomContext';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';
import {useTextureCache} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';
import {usePlayerHands, PlayerCard} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';
import {useTurnBasedCursor} from '@/src/client/Gaming/infrastructure/hooks/useTurnBasedCursor/useTurnBasedCursor';

type Props = {
  matchId: string;
  onCardSelect?: (cardId: string, image: string) => void;
};

const RADIUS = 12.5;
const scale = 1;
const CARD_WIDTH = 2 * scale;
const CARD_HEIGHT = 3 * scale;

const CurrentPlayerHand: FC<Props> = ({matchId, onCardSelect}) => {
  const {currentPlayerPosition, isLoading: isCurrentPlayerLoading} = useCurrentPlayer(matchId);
  const playerHands = usePlayerHands(matchId);
  const {showZoomedCard} = useCardZoomContext();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const {getTexture, backTexture} = useTextureCache();
  const turnBasedCursor = useTurnBasedCursor({matchId});
  
  const cards = currentPlayerPosition && playerHands 
    ? (currentPlayerPosition === 'player1' ? playerHands.player1 : playerHands.player2)
    : [];

  const filteredCards = cards.filter((card: PlayerCard) => card && card.instanceId && typeof card.instanceId === 'string' && card.instanceId !== '');

  if (isCurrentPlayerLoading || playerHands.isLoading || !currentPlayerPosition) {
    return null;
  }

  const cardCount = filteredCards.length;
  
  if (cardCount === 0) {
    return null;
  }
  const middleIndex = (cardCount - 1) / 2;

  const maxArc =






    Math.PI / 2.5;
  const minArc = Math.PI / 10;
  const ARC_WIDTH = (() => {
    if (cardCount === 2) return 0.15;
    if (cardCount === 3) return 0.25;
    return Math.min(maxArc, Math.max(minArc, (cardCount - 1) * 0.1));
  })();

  const angleStep = ARC_WIDTH / (cardCount - 1 || 1);
  const hoverScale = 1.8;

  return (
    <group position={[-2.5, -9.7, 0]}>
      {filteredCards.map((card: PlayerCard, index: number) => {
        const angle = (index - middleIndex) * angleStep;
        const x = Math.sin(angle) * RADIUS;
        const baseY = Math.cos(angle) * RADIUS * 0.7;

        return (
          <group
            key={card.instanceId}
            position={[x, baseY, -15 + index * 0.001]}
            rotation={[0, 0, -angle]}
          >
            <mesh
              position={[0, -CARD_HEIGHT / 2, 0]}
              onPointerOver={(e) => {
                turnBasedCursor.onPointerOver(e);
                setHoveredIndex(index);
              }}
              onPointerOut={(e) => {
                turnBasedCursor.onPointerOut(e);
                setHoveredIndex(null);
              }}
              onContextMenu={(e) => {
                e.stopPropagation();
                showZoomedCard(card.instanceId);
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (turnBasedCursor.isPlayerTurn) {
                  onCardSelect?.(card.instanceId, card.image);
                }
              }}
              onPointerDown={(e) => {
                e.stopPropagation();
                if (e.button === 2) {
                  showZoomedCard(card.instanceId);
                }
              }}
            >
              <planeGeometry args={[CARD_WIDTH, CARD_HEIGHT]} />
              <meshBasicMaterial
                map={getTexture(card.image) || backTexture}
                transparent={true}
                depthWrite={false}
                polygonOffset={true}
                polygonOffsetFactor={-1}
              />
            </mesh>
          </group>
        );
      })}

      {hoveredIndex !== null && (() => {
        const hoveredCard = filteredCards[hoveredIndex];
        const texture = getTexture(hoveredCard.image) || backTexture;
        const angle = (hoveredIndex - middleIndex) * angleStep;
        const x = Math.sin(angle) * RADIUS;
        const y = 10.9;

        return (
          <group
            position={[x, y, -13]}
            rotation={[0, 0, 0]}
          >
            <mesh position={[0, -CARD_HEIGHT / 2, 0]} scale={hoverScale}>
              <planeGeometry args={[CARD_WIDTH, CARD_HEIGHT]} />
              <meshBasicMaterial
                map={texture}
                transparent={true}
                depthWrite={false}
                polygonOffset={true}
                polygonOffsetFactor={-1}
              />
            </mesh>
          </group>
        );
      })()}
    </group>
  );
};

export default CurrentPlayerHand;