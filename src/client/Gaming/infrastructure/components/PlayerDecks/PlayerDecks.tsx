'use client';

import {FC} from 'react';
import {useTextureCache} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';
import {usePlayerDeckSizes as realUsePlayerDeckSizes, PlayerDeckSizes} from '@/src/client/Gaming/infrastructure/hooks/usePlayerDeckSizes/usePlayerDeckSizes';
import {useCurrentPlayer as realUseCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';

type PlayerDeckSizesHook = (matchId: string) => PlayerDeckSizes;
type CurrentPlayerHook = (matchId: string) => {
  currentPlayerPosition?: 'player1' | 'player2';
  isLoading: boolean;
};
type TextureCacheHook = () => { backTexture: unknown };

type Props = {
  matchId: string;
  usePlayerDeckSizesHook?: PlayerDeckSizesHook;
  useCurrentPlayerHook?: CurrentPlayerHook;
  useTextureCacheHook?: TextureCacheHook;
};

const CARD_WIDTH = 2;
const CARD_HEIGHT = 3;
const SLOT_COUNT = 8;
const CARD_SPACING = 3;
const ROW_CARD_Y = -1;
const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;

const deckRightPadding = 3.2;
const currentRowsZ: [number, number] = [0.4, 3.6];
const opponentRowsZ: [number, number] = [-6.4, -3.2];

const computeDeckPosition = (isOpponent: boolean): [number, number, number] => {
  const x = isOpponent ? -(offset + deckRightPadding) : (offset + deckRightPadding);
  const y = ROW_CARD_Y;
  const rows = isOpponent ? opponentRowsZ : currentRowsZ;
  const z = (rows[0] + rows[1]) / 2;
  return [x, y, z];
};

const computeDiscardPosition = (isOpponent: boolean): [number, number, number] => {
  const x = isOpponent ? (offset + deckRightPadding) : -(offset + deckRightPadding);
  const y = ROW_CARD_Y;
  const rows = isOpponent ? opponentRowsZ : currentRowsZ;
  const z = (rows[0] + rows[1]) / 2;
  return [x, y, z];
};

const DECK_THICKNESS = 0.5;

const DeckStack: FC<{ position: [number, number, number]; backTexture: unknown }>
  = ({ position, backTexture }) => {
  const [x, y, z] = position;

  return (
    <group position={[x, y, z]} scale={1.4}>
      <mesh castShadow={false}>
        <boxGeometry args={[CARD_WIDTH, DECK_THICKNESS, CARD_HEIGHT]} />
        <meshStandardMaterial attach="material-0" color="#1d1d1f"/>
        <meshStandardMaterial attach="material-1" color="#1d1d1f"/>
        <meshStandardMaterial attach="material-2" map={backTexture as never}/>
        <meshStandardMaterial attach="material-3" map={backTexture as never}/>
        <meshStandardMaterial attach="material-4" color="#1d1d1f"/>
        <meshStandardMaterial attach="material-5" color="#1d1d1f"/>
      </mesh>
    </group>
  );
};

const PlayerDecks: FC<Props> = ({
  matchId,
  usePlayerDeckSizesHook = realUsePlayerDeckSizes,
  useCurrentPlayerHook = realUseCurrentPlayer,
  useTextureCacheHook = useTextureCache,
}) => {
  const { backTexture } = useTextureCacheHook();
  const { currentPlayerPosition, isLoading } = usePlayerDeckSizesHook(matchId);
  const { isLoading: isCurrentLoading } = useCurrentPlayerHook(matchId);

  if (isLoading || isCurrentLoading || !currentPlayerPosition) {
    return null;
  }

  const currentPlayerPos = computeDeckPosition(false);
  const opponentPos = computeDeckPosition(true);
  const currentDiscardPos = computeDiscardPosition(false);
  const opponentDiscardPos = computeDiscardPosition(true);

  return (
    <group>
      <DeckStack position={currentPlayerPos} backTexture={backTexture} />
      <DeckStack position={opponentPos} backTexture={backTexture} />
      <DeckStack position={currentDiscardPos} backTexture={backTexture} />
      <DeckStack position={opponentDiscardPos} backTexture={backTexture} />
    </group>
  );
};

export default PlayerDecks;
