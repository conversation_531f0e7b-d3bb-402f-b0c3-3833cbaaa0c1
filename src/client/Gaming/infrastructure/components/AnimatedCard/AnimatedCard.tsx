'use client';

import {FC, useMemo, useEffect, useRef, useState} from 'react';
import {Texture, Group} from 'three';
import {RoundedBoxGeometry} from 'three-stdlib';
import {useFrame} from '@react-three/fiber';

type Props = {
  position: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
  frontTexture: Texture;
  backTexture?: Texture;
  visibleHeight?: number;
  width?: number;
  height?: number;
  thickness?: number;
  isNewlyPlaced?: boolean;
  onAnimationComplete?: () => void;
};

export const CARD_WIDTH = 2;
export const CARD_HEIGHT = 3;
const CARD_THICKNESS = 0.05;
const CARD_RADIUS = 0.15;
const SEGMENTS = 8;

const AnimatedCard: FC<Props> = ({
  position,
  rotation = [0, 0, 0],
  scale = 1.4,
  frontTexture,
  backTexture,
  visibleHeight,
  width = CARD_WIDTH,
  height = CARD_HEIGHT,
  thickness = CARD_THICKNESS,
  isNewlyPlaced = false,
  onAnimationComplete,
}) => {
  const groupRef = useRef<Group>(null);
  const [_animationProgress, setAnimationProgress] = useState(0);
  const [glowIntensity, setGlowIntensity] = useState(isNewlyPlaced ? 1.0 : 0);
  const animationStartTime = useRef<number | null>(null);

  const [front, back] = useMemo(() => {
    if (!frontTexture) {
      return [null, null];
    }
    const frontClone = frontTexture.clone();
    const backClone = backTexture ? backTexture.clone() : frontTexture.clone();
    
    if (typeof visibleHeight === 'number' && frontClone.image && visibleHeight < frontClone.image.height) {
      const ratio = visibleHeight / frontClone.image.height;
      frontClone.repeat.set(1, ratio);
      frontClone.offset.set(0, 1 - ratio);
      frontClone.needsUpdate = true;
    }
    
    return [frontClone, backClone];
  }, [frontTexture, backTexture, visibleHeight]);

  const geometry = useMemo(() => {
    return new RoundedBoxGeometry(width, thickness, height, SEGMENTS, CARD_RADIUS);
  }, [width, height, thickness]);

  const easeOutQuart = (t: number): number => {
    return 1 - Math.pow(1 - t, 4);
  };

  useFrame((state, _delta) => {
    if (!isNewlyPlaced || !groupRef.current) return;

    if (animationStartTime.current === null) {
      animationStartTime.current = state.clock.elapsedTime;
      groupRef.current.position.set(position[0], position[1] + 1.5, position[2]);
      groupRef.current.scale.set(scale * 1.1, scale * 1.1, scale * 1.1);
      return;
    }

    const elapsed = state.clock.elapsedTime - animationStartTime.current;
    const duration = 0.6;
    const progress = Math.min(elapsed / duration, 1);

    const easedProgress = easeOutQuart(progress);
    setAnimationProgress(easedProgress);

    const startY = position[1] + 1.5;
    const targetY = position[1];
    const currentY = startY + (targetY - startY) * easedProgress;
    
    const scaleMultiplier = 1.1 + (-0.1 * easedProgress);
    const currentScale = scale * scaleMultiplier;

    groupRef.current.position.set(position[0], currentY, position[2]);
    groupRef.current.rotation.set(rotation[0], rotation[1], rotation[2]);
    groupRef.current.scale.set(currentScale, currentScale, currentScale);

    const glowProgress = Math.max(0, 1 - progress * 2);
    setGlowIntensity(glowProgress);

    if (progress >= 1 && onAnimationComplete) {
      onAnimationComplete();
    }
  });

  useEffect(() => {
    if (isNewlyPlaced) {
      animationStartTime.current = null;
      setAnimationProgress(0);
      setGlowIntensity(1.0);
    }
  }, [isNewlyPlaced]);

  if (!front) {
    return null;
  }

  return (
    <group 
      ref={groupRef}
      position={isNewlyPlaced ? [position[0], position[1] + 1.5, position[2]] : position}
      rotation={rotation}
      scale={isNewlyPlaced ? [scale * 1.1, scale * 1.1, scale * 1.1] : [scale, scale, scale]}
    >
      {isNewlyPlaced && glowIntensity > 0 && (
        <pointLight
          intensity={glowIntensity * 0.4}
          color="#4ade80"
          distance={3}
          decay={2}
          position={[0, 0.5, 0]}
        />
      )}
      <mesh
        geometry={geometry}
        castShadow={true}
      >
        <meshStandardMaterial attach="material-0" color="#000000" transparent/>
        <meshStandardMaterial attach="material-1" color="#000000" transparent/>
        <meshStandardMaterial attach="material-2" map={front} transparent/>
        <meshStandardMaterial attach="material-3" map={back} transparent/>
        <meshStandardMaterial attach="material-4" color="#000000" transparent/>
        <meshStandardMaterial attach="material-5" color="#000000" transparent/>
      </mesh>
    </group>
  );
};

export default AnimatedCard;