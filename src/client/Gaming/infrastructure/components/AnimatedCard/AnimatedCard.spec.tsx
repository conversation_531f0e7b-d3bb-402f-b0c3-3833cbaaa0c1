import { render } from '@testing-library/react';
import { Mock } from 'vitest';

interface TestTexture {
  image: { height: number; width: number };
  repeat: { set: Mock };
  offset: { set: Mock };
  needsUpdate: boolean;
  clone: () => TestTexture;
}

const createMockTexture = (): TestTexture => {
  return {
    image: { height: 100, width: 100 },
    repeat: { set: vi.fn() },
    offset: { set: vi.fn() },
    needsUpdate: false,
    clone: vi.fn(() => createMockTexture()),
  };
};

vi.mock('@react-three/fiber', () => ({
  useFrame: vi.fn(),
}));

vi.mock('three', () => ({
  Group: class {},
  Texture: class {},
}));

vi.mock('three-stdlib', () => ({
  RoundedBoxGeometry: class {},
}));

describe('AnimatedCard', () => {
  it('should render without crashing', async () => {
    // Arrange
    const mockTexture = createMockTexture();
    const position: [number, number, number] = [0, 0, 0];
    const { default: AnimatedCard } = await import('./AnimatedCard');

    // Act
    render(
      <AnimatedCard
        position={position}
        frontTexture={mockTexture as unknown as import('three').Texture}
        backTexture={mockTexture as unknown as import('three').Texture}
      />
    );

    // Assert
    expect(mockTexture).toBeDefined();
  });

  it('should render with animation when newly placed', async () => {
    // Arrange
    const mockTexture = createMockTexture();
    const position: [number, number, number] = [0, 0, 0];
    const onAnimationComplete = vi.fn();
    const { default: AnimatedCard } = await import('./AnimatedCard');

    // Act
    render(
      <AnimatedCard
        position={position}
        frontTexture={mockTexture as unknown as import('three').Texture}
        backTexture={mockTexture as unknown as import('three').Texture}
        isNewlyPlaced={true}
        onAnimationComplete={onAnimationComplete}
      />
    );

    // Assert
    expect(onAnimationComplete).toBeDefined();
  });

  it('should render with custom dimensions', async () => {
    // Arrange
    const mockTexture = createMockTexture();
    const position: [number, number, number] = [1, 2, 3];
    const { default: AnimatedCard } = await import('./AnimatedCard');

    // Act
    render(
      <AnimatedCard
        position={position}
        frontTexture={mockTexture as unknown as import('three').Texture}
        backTexture={mockTexture as unknown as import('three').Texture}
        width={3}
        height={4}
        scale={1.5}
      />
    );

    // Assert
    expect(mockTexture).toBeDefined();
  });
});