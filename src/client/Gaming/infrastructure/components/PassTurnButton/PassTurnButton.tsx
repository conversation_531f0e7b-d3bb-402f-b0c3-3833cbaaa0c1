'use client';

import {But<PERSON>, <PERSON>lex, <PERSON>} from "@radix-ui/themes";
import {useCallback, useState} from "react";
import {usePassTurn} from '@/src/client/Gaming/infrastructure/hooks/usePassTurn/usePassTurn';
import {useIsCurrentPlayerTurn} from '@/src/client/Gaming/infrastructure/hooks/useIsCurrentPlayerTurn/useIsCurrentPlayerTurn';
import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';

type Props = {
  matchId: string;
};

export const PassTurnButton = ({matchId}: Props) => {
  const {passTurn} = usePassTurn();
  const {isCurrentPlayerTurn, isLoading, noTurnSet} = useIsCurrentPlayerTurn(matchId);
  const initializeTurnMutation = useMutation(api.mutations.initializeMatchTurn.endpoint);
  const [error, setError] = useState<string | null>(null);
  const [isPassingTurn, setIsPassingTurn] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  const handlePassTurn = useCallback(async () => {
    try {
      setError(null);
      setIsPassingTurn(true);
      await passTurn({matchId});
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pass turn';
      setError(errorMessage);
      console.error('Failed to pass turn:', err);
    } finally {
      setIsPassingTurn(false);
    }
  }, [passTurn, matchId]);

  const handleInitializeTurn = useCallback(async () => {
    try {
      setError(null);
      setIsInitializing(true);
      await initializeTurnMutation({matchId: matchId as Id<'matches'>});
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize turn';
      setError(errorMessage);
      console.error('Failed to initialize turn:', err);
    } finally {
      setIsInitializing(false);
    }
  }, [initializeTurnMutation, matchId]);

  if (isLoading) {
    return null;
  }

  if (noTurnSet) {
    return (
      <Flex direction="column" gap="3">
        {error && <Text color="red">{error}</Text>}
        <Button
          size="4"
          color="blue"
          variant="solid"
          onClick={handleInitializeTurn}
          disabled={isInitializing}
          style={{ fontSize: '16px', fontWeight: 'bold' }}
        >
          {isInitializing ? 'Starting Turn...' : 'Start Turn'}
        </Button>
      </Flex>
    );
  }

  if (!isCurrentPlayerTurn) {
    return null;
  }

  return (
    <Flex direction="column" gap="3">
      {error && <Text color="red">{error}</Text>}
      <Button
        size="4"
        color="violet"
        variant="solid"
        onClick={handlePassTurn}
        disabled={isPassingTurn}
        style={{ fontSize: '16px', fontWeight: 'bold' }}
      >
        {isPassingTurn ? 'Passing Turn...' : 'Pass Turn'}
      </Button>
    </Flex>
  );
};