'use client';

import {FC, useState, useRef, useCallback} from "react";
import Camera from "@/src/client/Gaming/infrastructure/components/Camera/Camera";
import Lights from "@/src/client/Gaming/infrastructure/components/Lights/Lights";
import Table from "@/src/client/Gaming/infrastructure/components/Table/Table";
import Shadow from "@/src/client/Gaming/infrastructure/components/Shadow/Shadow";
import FixedToCamera from "@/src/client/Gaming/infrastructure/components/FixedToCamera/FixedToCamera";
import CurrentPlayerHand from "@/src/client/Gaming/infrastructure/components/CurrentPlayerHand/CurrentPlayerHand";
import OpponentHand from "@/src/client/Gaming/infrastructure/components/OpponentHand/OpponentHand";
import Game2x7Layout from "@/src/client/Gaming/infrastructure/components/Game2x7Layout/Game2x7Layout";
import {useBoardStateSync} from '@/src/client/Gaming/infrastructure/hooks/useBoardStateSync/useBoardStateSync';
import {TextureCacheProvider} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';
import {usePlaceCard} from '@/src/client/Gaming/infrastructure/hooks/usePlaceCard/usePlaceCard';

type Props = {
  matchId?: string;
};

const RAPID_CLICK_THRESHOLD_MS = 100;

const GameScene: FC<Props> = ({matchId}) => {
  const [selectedCard, setSelectedCard] = useState<{cardId: string, image: string} | null>(null);
  const [placingCard, setPlacingCard] = useState<{cardId: string, image: string, rowType: string, slotIndex: number} | null>(null);
  const [isPlacing, setIsPlacing] = useState(false);
  const lastClickTimeRef = useRef<number>(0);
  const lastSelectTimeRef = useRef<number>(0);
  const placingCardsRef = useRef<Set<string>>(new Set()); // Track cards currently being placed
  const {placeCard} = usePlaceCard();
  
  useBoardStateSync(matchId);

  const handleCardSelect = useCallback((cardId: string, image: string) => {
    const now = Date.now();
    if (now - lastSelectTimeRef.current < RAPID_CLICK_THRESHOLD_MS) {
      return;
    }
    lastSelectTimeRef.current = now;
    
    setSelectedCard(selectedCard?.cardId === cardId ? null : {cardId, image});
  }, [selectedCard?.cardId]);

  const handleSlotClick = useCallback(async (rowType: string, slotIndex: number) => {
    if (!selectedCard || !matchId || isPlacing) {
      return;
    }

    const now = Date.now();
    if (now - lastClickTimeRef.current < RAPID_CLICK_THRESHOLD_MS) {
      return;
    }
    lastClickTimeRef.current = now;

    const normalizedRowType = rowType.includes('first') ? 'first' : 'second';

    const isCurrentPlayerRow = rowType.includes('current');
    if (!isCurrentPlayerRow) {
      return;
    }

    // Store the selected card before clearing it, so we can restore it on error
    const cardToPlace = selectedCard;

    // Check if this card is already being placed
    if (placingCardsRef.current.has(cardToPlace.cardId)) {
      console.log(`Card ${cardToPlace.cardId} is already being placed, ignoring duplicate request`);
      return;
    }

    // Set isPlacing immediately to prevent race conditions
    setIsPlacing(true);
    placingCardsRef.current.add(cardToPlace.cardId);
    setPlacingCard({cardId: cardToPlace.cardId, image: cardToPlace.image, rowType: normalizedRowType, slotIndex});
    setSelectedCard(null);

    try {
      const result = await placeCard({
        cardId: cardToPlace.cardId,
        rowType: normalizedRowType as 'first' | 'second',
        slotIndex,
        matchId
      });

      // If the mutation was successful or silently handled (duplicate), clear the placing card
      if (result !== undefined || result === undefined) {
        setTimeout(() => setPlacingCard(null), 100);
      }
    } catch (error) {
      console.error('Failed to place card:', error);

      // For any error that reaches here, restore the selected card state so user can try again
      setSelectedCard(cardToPlace);
      setPlacingCard(null);
    } finally {
      setIsPlacing(false);
      placingCardsRef.current.delete(cardToPlace.cardId);
    }
  }, [selectedCard, matchId, isPlacing, placeCard]);

  return (
    <TextureCacheProvider matchId={matchId}>
      <Camera/>
      <Lights/>
      <Table/>
      <Shadow/>
      {matchId && (
        <Game2x7Layout
          matchId={matchId}
          selectedCard={selectedCard}
          placingCard={placingCard}
          onSlotClick={handleSlotClick}
        />
      )}
      <FixedToCamera>
        {matchId && (
          <>
            <OpponentHand matchId={matchId} />
            <CurrentPlayerHand
              matchId={matchId}
              onCardSelect={handleCardSelect}
            />
          </>
        )}
      </FixedToCamera>
    </TextureCacheProvider>
  );
};

export default GameScene;
