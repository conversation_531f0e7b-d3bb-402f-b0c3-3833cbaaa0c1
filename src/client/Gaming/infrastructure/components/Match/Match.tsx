'use client';

import {FC} from "react";
import {Canvas} from "@react-three/fiber";
import {Id} from "@/convex/_generated/dataModel";
import {useMatchEndNavigation} from "@/src/client/Gaming/infrastructure/hooks/useMatchEndNavigation/useMatchEndNavigation";
import MulliganScreen from "../MulliganScreen/MulliganScreen";
import GameScene from "../GameScene/GameScene";

type Props = {
  locale: string;
  matchId: Id<"matches">;
  match: {
    players: string[];
    status: string;
    gameId: Id<"games">;
  };
};

const Match: FC<Props> = ({locale, matchId, match}) => {
  useMatchEndNavigation({matchId, locale, gameId: match.gameId});

  if (match.status === 'waiting_for_mulligan') {
    return <MulliganScreen matchId={matchId as string} />;
  }

  if (match.status === 'active') {
    return (
      <div style={{width: '100vw', height: '100vh'}}>
        <Canvas camera={{position: [0, 10, 20], fov: 75}}>
          <GameScene matchId={matchId as string} />
        </Canvas>
      </div>
    );
  }

  return (
    <div>
      <p>Match ID: {matchId}</p>
      <p>Players: {match?.players.join(" vs ")}</p>
      <p>Status: {match?.status}</p>
      <p>Setting up match...</p>
    </div>
  );
};

export default Match;