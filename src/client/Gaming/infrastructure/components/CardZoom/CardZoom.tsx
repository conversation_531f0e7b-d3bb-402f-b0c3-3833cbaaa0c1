'use client';

import {FC} from "react";
import {useCardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/useCardZoomContext';
import {useCardLookup} from '@/src/client/Gaming/infrastructure/hooks/useCardLookup/useCardLookup';
import {Box} from "@radix-ui/themes";
import Image from 'next/image';

interface Props {
  matchId: string;
}

export const CardZoom: FC<Props> = ({matchId}) => {
  const {zoomedCard, hideZoomedCard} = useCardZoomContext();
  const {getCardImageUrl} = useCardLookup(matchId);

  if (!zoomedCard?.isVisible || !zoomedCard?.cardId) {
    return null;
  }

  const imageUrl = zoomedCard.imageUrl || getCardImageUrl(zoomedCard.cardId);

  return (
    <Box
      style={{
        position: 'absolute',
        top: '80px',
        left: '20px',
        zIndex: 20,
        width: '300px',
        height: '450px',
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        cursor: 'pointer'
      }}
      onClick={hideZoomedCard}
    >
      <Image
        src={imageUrl}
        alt="Zoomed card"
        width={300}
        height={450}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      />
    </Box>
  );
};
