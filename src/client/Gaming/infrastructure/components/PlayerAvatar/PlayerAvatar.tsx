import {FC} from 'react';
import Image from 'next/image';
import {Box, Card, Flex, Text} from "@radix-ui/themes";

interface PlayerAvatarProps {
  name: string;
  avatar: string;
  size?: 'small' | 'medium' | 'large';
}

const PlayerAvatar: FC<PlayerAvatarProps> = ({name, avatar, size = 'medium'}) => {
  const sizeMap = {
    small: { width: 32, height: 32, textSize: '2' },
    medium: { width: 45, height: 45, textSize: '3' },
    large: { width: 64, height: 64, textSize: '4' }
  };
  
  const dimensions = sizeMap[size];

  return (
    <Card size="1" className="backdrop-blur-lg">
      <Flex align="center" gap="2">
      <Box className="rounded-full overflow-hidden">
        <Image
          src={avatar}
          alt={`${name}'s avatar`}
          width={dimensions.width}
          height={dimensions.height}
          className="w-full h-full object-cover"
        />
      </Box>
      <Flex direction="column" gap="1">
        <Text color="orange" size={dimensions.textSize as '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9'}>
          {name}
        </Text>
        <Text size="2" color="gray">
          #123456
        </Text>
      </Flex>
      </Flex>
    </Card>
  );
};

export default PlayerAvatar;