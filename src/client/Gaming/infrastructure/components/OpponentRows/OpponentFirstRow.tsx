'use client';

import {FC, useState} from 'react';
import PreloadedCard from '@/src/client/Gaming/infrastructure/components/PreloadedCard/PreloadedCard';
import EmptySlot from '@/src/client/Gaming/infrastructure/components/EmptySlot/EmptySlot';
import {useCardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/useCardZoomContext';
import {useTextureCache} from '@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';
import {useBoardState} from '@/src/client/Gaming/infrastructure/hooks/useBoardState/useBoardState';

const CARD_Y = -1.380;
const CARD_Z = -3.2;
const CARD_ROTATION: [number, number, number] = [0, 0, 0];
const CARD_SPACING = 3;
const SLOT_COUNT = 8;

type Props = {
  matchId: string;
  position?: [number, number, number];
  zPosition?: number;
  shouldMirror?: boolean;
};

const HOVER_OFFSET = 0.1;

const OpponentFirstRow: FC<Props> = ({
  matchId,
  position = [0, 0, 0],
  zPosition,
  shouldMirror = false
}) => {
  const {showZoomedCard} = useCardZoomContext();
  const {currentPlayerPosition, isLoading: isCurrentPlayerLoading} = useCurrentPlayer(matchId);
  const {boardState, isLoading: isBoardLoading} = useBoardState(matchId);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const offset = ((SLOT_COUNT - 1) * CARD_SPACING) / 2;
  const cardZ = zPosition ?? CARD_Z;
  const {getTexture, backTexture} = useTextureCache();

  const boardRow = currentPlayerPosition && boardState
    ? (currentPlayerPosition === 'player1' ? boardState.player2Board.firstRow : boardState.player1Board.firstRow)
    : [];

  if (isCurrentPlayerLoading || isBoardLoading || !currentPlayerPosition) {
    return null;
  }

  const getXPosition = (index: number) => {
    return shouldMirror
      ? (SLOT_COUNT - 1 - index) * CARD_SPACING - offset
      : index * CARD_SPACING - offset;
  };

  return (
    <group position={position}>
      {Array.from({length: SLOT_COUNT}, (_, index) => (
        <group
          key={index}
          onPointerOver={(e) => {
            e.stopPropagation();
            setHoveredIndex(index);
          }}
          onPointerOut={(e) => {
            e.stopPropagation();
            setHoveredIndex(null);
          }}
          onContextMenu={(e) => {
            e.stopPropagation();
            if (boardRow[index]) {
              const card = boardRow[index]!;
              const imageUrl = card.image.startsWith('/game-assets/') || card.image.startsWith('http') 
                ? card.image 
                : `/game-assets/cards/en/thumbnail/${card.image.replace('.jpg', '')}.jpg`;
              showZoomedCard(card.cardId, imageUrl);
            }
          }}
          onPointerDown={(e) => {
            e.stopPropagation();
            if (e.button === 2 && boardRow[index]) {
              const card = boardRow[index]!;
              const imageUrl = card.image.startsWith('/game-assets/') || card.image.startsWith('http') 
                ? card.image 
                : `/game-assets/cards/en/thumbnail/${card.image.replace('.jpg', '')}.jpg`;
              showZoomedCard(card.cardId, imageUrl);
            }
          }}
        >
          {boardRow[index] ? (
            <PreloadedCard
              key={`placed-${boardRow[index]!.cardId}`}
              frontTexture={getTexture(boardRow[index]!.image) || backTexture}
              backTexture={backTexture}
              width={2}
              height={2.1}
              position={[
                getXPosition(index),
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                cardZ,
              ]}
              rotation={CARD_ROTATION}
              visibleHeight={340}
            />
          ) : (
            <EmptySlot
              key={`empty-${index}`}
              width={2}
              height={2.1}
              position={[
                getXPosition(index),
                CARD_Y + (hoveredIndex === index ? HOVER_OFFSET : 0),
                cardZ,
              ]}
              rotation={CARD_ROTATION}
            />
          )}
        </group>
      ))}
    </group>
  );
};

export default OpponentFirstRow;