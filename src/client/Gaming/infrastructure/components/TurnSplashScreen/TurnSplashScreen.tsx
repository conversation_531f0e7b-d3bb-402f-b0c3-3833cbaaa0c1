import {FC, useEffect} from 'react';
import {motion} from 'framer-motion';
import {Heading} from '@radix-ui/themes';

type Props = {
  show: boolean;
  onDismiss: () => void;
};

const TurnSplashScreen: FC<Props> = ({show, onDismiss}) => {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(onDismiss, 1000);
      return () => clearTimeout(timer);
    }
  }, [show, onDismiss]);

  if (!show) return null;

  return (
    <motion.div
      initial={{opacity: 0}}
      animate={{opacity: 1}}
      exit={{opacity: 0}}
      transition={{duration: 0.3}}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(8px)',
        WebkitBackdropFilter: 'blur(8px)',
      }}
    >
      <motion.div
        initial={{scale: 0.5, opacity: 0}}
        animate={{scale: 1, opacity: 1}}
        transition={{
          type: 'spring',
          stiffness: 200,
          damping: 20,
          duration: 0.6,
        }}
      >
        <Heading
          size="9"
          style={{
            color: '#fff',
            textShadow: '0 0 20px var(--accent-a9)',
            fontSize: '6rem',
            fontWeight: 'bold',
            textAlign: 'center',
            userSelect: 'none',
          }}
        >
          YOUR TURN!
        </Heading>
      </motion.div>
    </motion.div>
  );
};

export default TurnSplashScreen;