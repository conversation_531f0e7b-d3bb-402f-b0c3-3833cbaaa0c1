'use client';

import {FC} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lex, Badge, Card, Code} from '@radix-ui/themes';
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {Id} from '@/convex/_generated/dataModel';

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  matchId: Id<'matches'>;
};

const DebugEventDialog: FC<Props> = ({open, onOpenChange, matchId}) => {
  const viewModel = useQuery(api.queries.match.matchEvents, {matchId});
  const allEvents = viewModel?.data?.events || [];
  const lastFifteenEvents = allEvents.slice(-15);

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Content style={{maxWidth: '90vw', maxHeight: '85vh'}}>
        <Dialog.Title>Debug Events - Last 15 Events</Dialog.Title>
        <Dialog.Description size="2" mb="4">
          Showing the most recent 15 match events ({allEvents.length} total events)
        </Dialog.Description>
        <div style={{
          height: '65vh',
          overflow: 'auto',
          marginBottom: '16px',
          border: '1px solid #e1e5e9',
          borderRadius: '8px',
          padding: '12px'
        }}>
          <Card>
            <Flex direction="column" gap="2">
              {!allEvents.length && <div>Loading...</div>}
              {allEvents.length === 0 && <div>No events</div>}
              {lastFifteenEvents.slice().reverse().map((event, index) => (
                <div 
                  key={event.id}
                  style={{
                    padding: '8px',
                    borderRadius: '4px',
                    backgroundColor: index % 2 === 0 ? '#000000' : '#191919',
                    border: '1px solid #e9ecef'
                  }}
                >
                  <Flex direction="column" gap="2">
                    <Flex align="center" justify="between" gap="3" wrap="wrap">
                      <Badge variant="outline" color="red" size="2" ml="2">
                        {new Date(event.occurredAt).toLocaleTimeString()}
                      </Badge>
                      <Badge
                        variant="surface"
                        color={event.type === 'MatchEnded' ? 'red' : 'blue'}
                        size="2"
                      >
                        {event.type}
                      </Badge>
                    </Flex>
                    <div style={{ marginLeft: '8px' }}>
                      <Code color="teal" style={{ fontSize: '12px', display: 'block', whiteSpace: 'pre-wrap' }}>
                        {JSON.stringify(event.payload, null, 2)}
                      </Code>
                    </div>
                  </Flex>
                </div>
              ))}
            </Flex>
          </Card>
        </div>
        <Flex gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray">
              Close
            </Button>
          </Dialog.Close>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default DebugEventDialog;