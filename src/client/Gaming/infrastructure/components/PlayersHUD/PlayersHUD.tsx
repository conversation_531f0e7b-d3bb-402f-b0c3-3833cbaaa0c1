import { FC } from 'react';
import { usePlayersInfo } from '../../hooks/usePlayersInfo/usePlayersInfo';
import PlayerAvatar from '../PlayerAvatar/PlayerAvatar';

interface PlayersHUDProps {
  matchId: string;
}

const PlayersHUD: FC<PlayersHUDProps> = ({ matchId }) => {
  const { currentPlayer, opponent, isLoading } = usePlayersInfo(matchId);

  if (isLoading || !currentPlayer || !opponent) {
    return null;
  }

  return (
    <>
      <div 
        style={{
          position: 'absolute',
          bottom: '20px',
          left: '20px',
          zIndex: 15,
        }}
      >
        <PlayerAvatar
          name={currentPlayer.name}
          avatar={currentPlayer.avatar}
          size="large"
        />
      </div>

      <div 
        style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          zIndex: 15,
        }}
      >
        <PlayerAvatar
          name={opponent.name}
          avatar={opponent.avatar}
          size="large"
        />
      </div>
    </>
  );
};

export default PlayersHUD;