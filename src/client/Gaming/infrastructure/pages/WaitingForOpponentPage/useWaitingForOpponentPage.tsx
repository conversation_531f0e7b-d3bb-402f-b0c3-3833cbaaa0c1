import {useCallback, useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import {useCancelMatchRegistration} from "@/src/client/Gaming/infrastructure/hooks/useCancelMatchRegistration/useCancelMatchRegistration";
import {useQueueStatus} from "@/src/client/Gaming/infrastructure/hooks/useQueueStatus/useQueueStatus";
import {formatSearchTime} from "@/src/client/Gaming/application/queries/formatSearchTime/formatSearchTime";

type QueueStatus = {
  isInQueue: boolean;
  queueItem: unknown;
  match: string | null;
};

type QueueStatusHook = (gameId: string) => { 
  queueStatus?: QueueStatus; 
  isLoading: boolean; 
  error?: string 
};

type CancelMatchRegistrationHook = () => { 
  cancelMatchRegistration: (params: { gameId: string; locale: string }) => Promise<{ success: boolean; redirectUrl?: string }> 
};

type RouterHook = () => { push: (url: string) => void };

export const useWaitingForOpponentPage = (
  {gameId, locale}: {gameId: string; locale: string},
  queueStatusHook: QueueStatusHook = useQueueStatus,
  cancelMatchRegistrationHook: CancelMatchRegistrationHook = useCancelMatchRegistration,
  routerHook: RouterHook = useRouter
) => {
  const router = routerHook();
  const [searchTime, setSearchTime] = useState(0);
  const {queueStatus} = queueStatusHook(gameId);
  const {cancelMatchRegistration} = cancelMatchRegistrationHook();

  useEffect(() => {
    setSearchTime(0);

    const interval = setInterval(() => {
      setSearchTime(prev => prev + 1);
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  useEffect(() => {
    if (queueStatus?.match) {
      router.push(`/${locale}/matches/${queueStatus.match}`);
    }
  }, [queueStatus, router, locale]);

  const handleCancel = useCallback(async () => {
    try {
      const result = await cancelMatchRegistration({gameId, locale});
      if (result.success && result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    } catch (error) {
      console.error('Failed to cancel match registration:', error);
    }
  }, [cancelMatchRegistration, gameId, locale, router]);

  const formatTime = useCallback((seconds: number) => {
    return formatSearchTime(seconds);
  }, []);

  return {
    searchTime,
    formatTime,
    handleCancel,
  };
};
