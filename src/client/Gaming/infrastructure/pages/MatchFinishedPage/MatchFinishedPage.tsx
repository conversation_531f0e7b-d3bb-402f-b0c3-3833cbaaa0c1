'use client';

import {FC} from "react";
import FinishedMatchPage from "@/src/client/Gaming/infrastructure/pages/FinishedMatchPage/FinishedMatchPage";
import {useMatchFinishedPage} from "@/src/client/Gaming/infrastructure/hooks/useMatchFinishedPage/useMatchFinishedPage";

type Props = {
  matchId: string;
};

const MatchFinishedPage: FC<Props> = ({matchId}) => {
  const { matchData } = useMatchFinishedPage(matchId);

  if (!matchData) {
    return <div>Loading...</div>;
  }

  return <FinishedMatchPage matchId={matchId} match={matchData}/>;
};

export default MatchFinishedPage;