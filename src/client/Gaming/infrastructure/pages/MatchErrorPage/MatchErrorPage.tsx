'use client';

import {FC} from "react";
import ErrorLoadingMatchPage from "@/src/client/Gaming/infrastructure/pages/ErrorLoadingMatchPage/ErrorLoadingMatchPage";
import {useMatchErrorPage} from "@/src/client/Gaming/infrastructure/hooks/useMatchErrorPage/useMatchErrorPage";

type Props = {
  matchId: string;
  errorMessage: string;
};

const MatchErrorPage: FC<Props> = ({matchId, errorMessage}) => {
  useMatchErrorPage(matchId);

  return <ErrorLoadingMatchPage errorMessage={errorMessage}/>;
};

export default MatchErrorPage;