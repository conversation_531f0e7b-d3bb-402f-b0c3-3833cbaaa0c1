'use client';

import {FC, useEffect} from "react";
import {useRouter} from "next/navigation";
import {useParams} from "next/navigation";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";

type Props = {
  matchId: string;
};

const MatchPage: FC<Props> = ({matchId}) => {
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  
  const viewModel = useQuery(api.queries.match.getMatchMeta, {matchId});

  useEffect(() => {
    if (viewModel?.error) {
      router.push(`/${locale}/matches/${matchId}/error?error=${encodeURIComponent(viewModel.error)}`);
      return;
    }

    const status = viewModel?.data?.status;
    if (status === 'finished') {
      router.push(`/${locale}/matches/${matchId}/finished`);
    } else if (status === 'waiting_for_mulligan') {
      router.push(`/${locale}/matches/${matchId}/mulligan`);
    } else if (status) {
      router.push(`/${locale}/matches/${matchId}/game`);
    }
  }, [viewModel, router, locale, matchId]);

  return (
    <div style={{width: '100vw', height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
      <div>Loading match...</div>
    </div>
  );
};

export default MatchPage;
