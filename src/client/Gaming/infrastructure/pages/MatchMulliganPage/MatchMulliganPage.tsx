'use client';

import {FC} from 'react';
import MulliganScreen from '@/src/client/Gaming/infrastructure/components/MulliganScreen/MulliganScreen';
import {CardZoomProvider} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/CardZoomProvider';
import {useMatchMulliganPage} from '@/src/client/Gaming/infrastructure/hooks/useMatchMulliganPage/useMatchMulliganPage';

type Props = {
  matchId: string;
};

const MatchMulliganPage: FC<Props> = ({matchId}) => {
  useMatchMulliganPage(matchId);

  return (
    <CardZoomProvider>
      <MulliganScreen matchId={matchId}/>
    </CardZoomProvider>
  );
};

export default MatchMulliganPage;