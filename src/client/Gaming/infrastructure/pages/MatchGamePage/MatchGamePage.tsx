'use client';

import {FC} from "react";
import {Canvas} from "@react-three/fiber";
import GameScene from "@/src/client/Gaming/infrastructure/components/GameScene/GameScene";
import HUD from "@/src/client/Gaming/infrastructure/components/HUD/HUD";
import TurnSplashScreen from "@/src/client/Gaming/infrastructure/components/TurnSplashScreen/TurnSplashScreen";
import {CardZoomProvider} from "@/src/client/Gaming/infrastructure/providers/CardZoomProvider/CardZoomProvider";
import {GameErrorProvider} from "@/src/client/Gaming/infrastructure/providers/GameErrorProvider/GameErrorProvider";
import {useMatchGamePage} from "@/src/client/Gaming/infrastructure/hooks/useMatchGamePage/useMatchGamePage";
import {useTurnTransition} from "@/src/client/Gaming/infrastructure/hooks/useTurnTransition/useTurnTransition";

type Props = {
  matchId: string;
};

const MatchGamePage: FC<Props> = ({matchId}) => {
  const { handleCanvasInteraction } = useMatchGamePage(matchId);
  const { showTurnSplash, dismissTurnSplash } = useTurnTransition(matchId);

  return (
    <GameErrorProvider>
      <CardZoomProvider>
        <div style={{width: '100vw', height: '100vh', position: 'relative'}}>
          <Canvas 
            style={{width: '100%', height: '100%'}} 
            shadows={false}
            onContextMenu={(e) => e.preventDefault()}
            onClick={handleCanvasInteraction}
            onPointerDown={(e) => {
              if (e.button === 2) {
                handleCanvasInteraction();
              }
            }}
          >
            <GameScene matchId={matchId}/>
          </Canvas>

          <HUD matchId={matchId}/>
          
          <TurnSplashScreen 
            show={showTurnSplash} 
            onDismiss={dismissTurnSplash}
          />
        </div>
      </CardZoomProvider>
    </GameErrorProvider>
  );
};

export default MatchGamePage;