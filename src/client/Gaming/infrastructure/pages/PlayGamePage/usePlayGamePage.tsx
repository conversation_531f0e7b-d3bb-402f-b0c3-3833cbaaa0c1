import {useCallback, useState, useEffect} from "react";
import {useRouter} from "next/navigation";
import {useAvailableDecks} from "@/src/client/Gaming/infrastructure/hooks/useAvailableDecks/useAvailableDecks";
import {useJoinQueue} from "@/src/client/Gaming/infrastructure/hooks/useJoinQueue/useJoinQueue";

type Deck = {
  id: string;
  name: string;
  images: string[];
};

type AvailableDecksHook = (gameId: string, locale: string) => { 
  availableDecks: Deck[]; 
  isLoading: boolean; 
  error?: string 
};

type JoinQueueHook = () => { 
  joinQueue: (params: { gameId: string; deckId: string }) => Promise<{ success: boolean; redirectUrl?: string }> 
};

type RouterHook = () => { push: (url: string) => void };

export const usePlayGamePage = (
  {gameId, locale}: { gameId: string, locale: string },
  availableDecksHook: AvailableDecksHook = useAvailableDecks,
  joinQueueHook: JoinQueueHook = useJoinQueue,
  routerHook: RouterHook = useRouter
) => {
  const router = routerHook();
  const {availableDecks, isLoading: loadingDecks} = availableDecksHook(gameId, locale);
  const {joinQueue} = joinQueueHook();
  const [selectedDeckId, setSelectedDeckId] = useState<string | null>(null);

  const decks = availableDecks;
  const selectedDeck = selectedDeckId ? decks.find((deck) => deck.id === selectedDeckId) : null;

  useEffect(() => {
    if (!loadingDecks && decks.length > 0 && !selectedDeckId) {
      setSelectedDeckId(decks[0].id);
    }
  }, [loadingDecks, decks, selectedDeckId]);

  const handleStart = useCallback(async () => {
    if (!selectedDeckId) return;

    try {
      const result = await joinQueue({gameId, deckId: selectedDeckId});
      if (result.success && result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    } catch (error) {
      console.error('Failed to join match making queue:', error);
    }
  }, [joinQueue, gameId, selectedDeckId, router]);

  const setDeckId = useCallback((deckId: string) => {
    setSelectedDeckId(deckId);
  }, []);

  return {
    loadingDecks,
    deckId: selectedDeckId,
    decks,
    selectedDeckImages: selectedDeck?.images || [],
    selectedDeckName: selectedDeck?.name,
    handleStart,
    setDeckId,
  };
}
