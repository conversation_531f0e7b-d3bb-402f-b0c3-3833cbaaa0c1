'use client'

import {FC, useEffect} from "react";
import {useRouter} from "next/navigation";
import {Container, Flex, Text} from "@radix-ui/themes";
import {useQueueStatus} from "@/src/client/Gaming/infrastructure/hooks/useQueueStatus/useQueueStatus";
import {buildWaitingForOpponentUrl} from "@/src/client/Shared/helpers/UrlBuilder/urlBuilder";
import PlayGamePage from "@/src/client/Gaming/infrastructure/pages/PlayGamePage/PlayGamePage";

type Props = {
  gameId: string;
  locale: string;
};

const PlayGamePageWithQueueCheck: FC<Props> = ({gameId, locale}) => {
  const router = useRouter();
  const {queueStatus, isLoading: isCheckingQueue} = useQueueStatus(gameId);

  useEffect(() => {
    if (!isCheckingQueue && queueStatus?.match) {
      router.push(`/${locale}/matches/${queueStatus.match}`);
    } else if (!isCheckingQueue && queueStatus?.isInQueue) {
      const waitingUrl = buildWaitingForOpponentUrl(locale, gameId);
      router.push(waitingUrl);
    }
  }, [queueStatus, isCheckingQueue, gameId, locale, router]);

  if (isCheckingQueue) {
    return (
      <Container p="3">
        <Flex direction="column" align="center" justify="center" className="h-screen">
          <Text size="3" color="gray">
            Loading...
          </Text>
        </Flex>
      </Container>
    );
  }

  if (queueStatus?.isInQueue) {
    return (
      <Container p="3">
        <Flex direction="column" align="center" justify="center" className="h-screen">
          <Text size="3" color="gray">
            Redirecting to waiting room...
          </Text>
        </Flex>
      </Container>
    );
  }

  return <PlayGamePage gameId={gameId} locale={locale} />;
};

export default PlayGamePageWithQueueCheck;