import {MatchService, LeaveMatchResult, PlayCardResult, BoardStateResult} from '@/src/client/Gaming/application/ports/MatchService';

export class FakeMatchService implements MatchService {
  private readonly leaveResult: LeaveMatchResult;
  private readonly playCardResult: PlayCardResult;
  private readonly boardStateResult: BoardStateResult;
  private readonly shouldThrow: boolean;

  constructor(
    leaveResult: LeaveMatchResult = {},
    playCardResult: PlayCardResult = {},
    boardStateResult: BoardStateResult = {
      boardState: {
        player1Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        },
        player2Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        }
      }
    },
    shouldThrow = false
  ) {
    this.leaveResult = leaveResult;
    this.playCardResult = playCardResult;
    this.boardStateResult = boardStateResult;
    this.shouldThrow = shouldThrow;
  }

  async leaveMatch(): Promise<LeaveMatchResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to leave match');
    }
    return this.leaveResult;
  }

  async playCard(_matchId?: string, _cardId?: string, _rowType?: 'first' | 'second', _slotIndex?: number): Promise<PlayCardResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to play card');
    }
    return this.playCardResult;
  }

  async getBoardState(): Promise<BoardStateResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to get board state');
    }
    return this.boardStateResult;
  }
};
