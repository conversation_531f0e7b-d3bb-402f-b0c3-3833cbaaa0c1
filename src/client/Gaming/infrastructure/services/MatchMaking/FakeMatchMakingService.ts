import {MatchMakingService, CancelMatchRegistrationResult, JoinMatchMakingQueueResult, MatchCreatedData, PlayerQueueStatusResult} from '@/src/client/Gaming/application/ports/MatchMakingService';

export class FakeMatchMakingService implements MatchMakingService {
  private readonly cancelResult: CancelMatchRegistrationResult;
  private readonly joinResult: JoinMatchMakingQueueResult;
  private readonly matchCreatedData: MatchCreatedData | null;
  private readonly queueStatusResult: PlayerQueueStatusResult;
  private readonly shouldThrow: boolean;

  constructor(
    cancelResult: CancelMatchRegistrationResult = {},
    matchCreatedData: MatchCreatedData | null = null,
    shouldThrow = false,
    joinResult: JoinMatchMakingQueueResult = {},
    queueStatusResult: PlayerQueueStatusResult = {isInQueue: false, queueItem: null, match: null}
  ) {
    this.cancelResult = cancelResult;
    this.joinResult = joinResult;
    this.matchCreatedData = matchCreatedData;
    this.queueStatusResult = queueStatusResult;
    this.shouldThrow = shouldThrow;
  }

  async cancelMatchRegistration(): Promise<CancelMatchRegistrationResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to cancel match registration');
    }
    return this.cancelResult;
  }

  async joinMatchMakingQueue(): Promise<JoinMatchMakingQueueResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to join match making queue');
    }
    return this.joinResult;
  }

  async watchForMatchCreated(): Promise<MatchCreatedData | null> {
    if (this.shouldThrow) {
      throw new Error('Failed to watch for match created');
    }
    return this.matchCreatedData;
  }

  async getPlayerQueueStatus(): Promise<PlayerQueueStatusResult> {
    if (this.shouldThrow) {
      throw new Error('Failed to get player queue status');
    }
    return this.queueStatusResult;
  }
};
