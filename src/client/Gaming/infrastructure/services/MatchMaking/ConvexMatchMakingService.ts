import {
  CancelMatchRegistrationResult,
  JoinMatchMakingQueueResult,
  MatchCreatedData,
  MatchMakingService,
  PlayerQueueStatusResult
} from '@/src/client/Gaming/application/ports/MatchMakingService';
import {convexClient} from '@/src/client/Shared/providers/ConvexClientProvider/ConvexClient';
import {api} from '@/convex/_generated/api';
import {Id} from '@/convex/_generated/dataModel';

export class ConvexMatchMakingService implements MatchMakingService {
  async cancelMatchRegistration(gameId: string): Promise<CancelMatchRegistrationResult> {
    try {
      await convexClient.mutation(
        api.mutations.cancelMatchRegistration.endpoint,
        {gameId: gameId as Id<'games'>}
      );
      return {};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {error: message};
    }
  }

  async joinMatchMakingQueue(gameId: string, deckId: string): Promise<JoinMatchMakingQueueResult> {
    try {
      await convexClient.mutation(
        api.mutations.addPlayerToMatchMakingQueue.endpoint,
        {gameId: gameId as Id<'games'>, deckId: deckId as Id<'decks'>}
      );
      return {};
    } catch (e) {
      const message = e instanceof Error ? e.message : 'Unknown error';
      return {error: message};
    }
  }

  async watchForMatchCreated(gameId: string): Promise<MatchCreatedData | null> {
    try {
      const result = await convexClient.query(
        api.queries.match.onMatchCreated,
        {gameId: gameId as Id<'games'>}
      );
      return result?.data || null;
    } catch (e) {
      console.error('Error watching for match created:', e);
      return null;
    }
  }

  async getPlayerQueueStatus(gameId: string): Promise<PlayerQueueStatusResult> {
    try {
      return await convexClient.query(
        api.queries.matchMakingQueue.getPlayerQueueStatus,
        {gameId: gameId as Id<'games'>}
      );
    } catch (e) {
      console.error('Error getting player queue status:', e);
      return {isInQueue: false, queueItem: null, match: null};
    }
  }
}
