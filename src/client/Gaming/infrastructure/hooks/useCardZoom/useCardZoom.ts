import {useState} from 'react';

type ZoomedCard = {
  cardId: string;
  isVisible: boolean;
} | null;

export const useCardZoom = () => {
  const [zoomedCard, setZoomedCard] = useState<ZoomedCard>(null);

  const showZoomedCard = (cardId: string) => {
    setZoomedCard({ cardId, isVisible: true });
  };

  const hideZoomedCard = () => {
    setZoomedCard(null);
  };

  return {
    zoomedCard,
    showZoomedCard,
    hideZoomedCard,
    isVisible: zoomedCard?.isVisible ?? false,
  };
};