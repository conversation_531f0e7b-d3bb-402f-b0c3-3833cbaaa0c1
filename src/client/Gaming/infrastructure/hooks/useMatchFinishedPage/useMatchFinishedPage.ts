import {useRouter} from "next/navigation";
import {useParams} from "next/navigation";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {useEffect} from "react";

interface MatchData {
  players: string[];
  isWinner: boolean;
}

interface MatchFinishedPageState {
  matchData?: MatchData;
}

interface MatchMetaData {
  status?: string;
  players?: string[];
  isWinner?: boolean;
}

const createMatchData = (data?: MatchMetaData): MatchData | undefined => {
  if (!data?.players || typeof data.isWinner !== 'boolean') return undefined;
  return {
    players: data.players,
    isWinner: data.isWinner
  };
};

export const useMatchFinishedPage = (matchId: string): MatchFinishedPageState => {
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  
  const viewModel = useQuery(api.queries.match.getMatchMeta, {matchId});

  useEffect(() => {
    if (viewModel?.error) {
      router.push(`/${locale}/matches/${matchId}/error?error=${encodeURIComponent(viewModel.error)}`);
      return;
    }

    const status = viewModel?.data?.status;
    if (status === 'waiting_for_mulligan') {
      router.push(`/${locale}/matches/${matchId}/mulligan`);
    } else if (status !== 'finished') {
      router.push(`/${locale}/matches/${matchId}/game`);
    }
  }, [viewModel, router, locale, matchId]);

  const matchData = viewModel?.data?.status === 'finished' 
    ? createMatchData(viewModel?.data) 
    : undefined;

  return {
    matchData
  };
};