import {useRouter} from "next/navigation";
import {useParams} from "next/navigation";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {useCardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/useCardZoomContext';
import {useEffect} from "react";

interface MatchGamePageState {
  handleCanvasInteraction: () => void;
}

export const useMatchGamePage = (matchId: string): MatchGamePageState => {
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  const {hideZoomedCard} = useCardZoomContext();
  
  const viewModel = useQuery(api.queries.match.getMatchMeta, {matchId});

  const handleCanvasInteraction = () => {
    hideZoomedCard();
  };

  useEffect(() => {
    if (viewModel?.error) {
      router.push(`/${locale}/matches/${matchId}/error?error=${encodeURIComponent(viewModel.error)}`);
      return;
    }

    const status = viewModel?.data?.status;
    if (status === 'finished') {
      router.push(`/${locale}/matches/${matchId}/finished`);
    } else if (status === 'waiting_for_mulligan') {
      router.push(`/${locale}/matches/${matchId}/mulligan`);
    }
  }, [viewModel, router, locale, matchId]);

  return {
    handleCanvasInteraction
  };
};