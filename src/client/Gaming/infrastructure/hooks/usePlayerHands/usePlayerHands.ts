import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';

export type PlayerCard = {
  instanceId: string;
  catalogCardId: string;
  image: string;
};

export const usePlayerHands = (
  matchId: string | undefined
): {
  player1: PlayerCard[];
  player2: PlayerCard[];
  player1Count?: number;
  player2Count: number;
  isLoading: boolean;
} => {
  const queryResult = useQuery(
    api.queries.gaming.getPlayerHands,
    matchId ? { matchId: matchId as Id<'matches'> } : 'skip'
  );

  if (matchId === undefined) {
    return { player1: [], player2: [], player1Count: 0, player2Count: 0, isLoading: false };
  }

  if (queryResult === undefined) {
    return {
      player1: [],
      player2: [],
      player1Count: 0,
      player2Count: 0,
      isLoading: true,
    };
  }

  if (queryResult === null) {
    return { player1: [], player2: [], player1Count: 0, player2Count: 0, isLoading: false };
  }

  return {
    player1: queryResult.player1,
    player2: queryResult.player2,
    player1Count: ((queryResult as { player1Count?: number }).player1Count) ?? 0,
    player2Count: queryResult.player2Count,
    isLoading: false,
  };
};
