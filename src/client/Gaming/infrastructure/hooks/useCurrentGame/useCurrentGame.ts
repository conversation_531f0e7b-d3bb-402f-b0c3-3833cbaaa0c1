import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';

type GameData = {
  id: string;
  name: string;
};

type ViewModel = {
  error?: string | null;
  data?: GameData | null;
};

export const useCurrentGame = (
  gameId: string | undefined
): {
  currentGame: GameData | undefined;
  isLoading: boolean;
  error: string | undefined;
} => {
  const viewModel = useQuery(
    api.queries.catalog.loadGameById,
    gameId ? { gameId } : 'skip'
  ) as ViewModel | undefined;

  if (gameId === undefined) {
    return { currentGame: undefined, isLoading: false, error: undefined };
  }

  if (viewModel === undefined) {
    return { currentGame: undefined, isLoading: true, error: undefined };
  }

  return {
    currentGame: viewModel.data ?? undefined,
    isLoading: false,
    error: viewModel.error ?? undefined,
  };
};

