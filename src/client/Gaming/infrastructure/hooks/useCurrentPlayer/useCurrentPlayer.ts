import {useRef} from 'react';
import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {PlayerRole, LegacyPlayerId} from '@/src/client/Gaming/domain/PlayerRole/PlayerRole';

export const useCurrentPlayer = (matchId: string | undefined): {
  currentPlayerPosition: LegacyPlayerId | undefined;
  isLoading: boolean;
  getCurrentPlayerRole: () => PlayerRole | undefined;
  getOpponentRole: () => PlayerRole | undefined;
  isCurrentPlayer: (playerId: LegacyPlayerId) => boolean;
} => {
  const queriesNs = (api as Record<string, unknown>).queries as Record<string, unknown>;
  const matchNs = (queriesNs['match'] ?? {}) as Record<string, unknown>;
  const getMatchMetaRef = matchNs['getMatchMeta'] as unknown;

  const meta = useQuery(
    getMatchMetaRef as never,
    (matchId ? ({ matchId } as never) : ('skip' as never))
  ) as
    | { error: string | null; data: { currentPlayerPosition?: LegacyPlayerId } | null }
    | undefined;

  const lastKnownPosition = useRef<LegacyPlayerId | undefined>(undefined);

  if (matchId === undefined) {
    return {
      currentPlayerPosition: undefined,
      isLoading: false,
      getCurrentPlayerRole: () => undefined,
      getOpponentRole: () => undefined,
      isCurrentPlayer: () => false,
    };
  }

  if (meta && meta.data?.currentPlayerPosition) {
    lastKnownPosition.current = meta.data.currentPlayerPosition as LegacyPlayerId;
  }

  const position = lastKnownPosition.current;
  const isFirstLoad = meta === undefined && position === undefined;

  if (isFirstLoad || (meta && meta.error)) {
    return {
      currentPlayerPosition: undefined,
      isLoading: isFirstLoad,
      getCurrentPlayerRole: () => undefined,
      getOpponentRole: () => undefined,
      isCurrentPlayer: () => false,
    };
  }

  return {
    currentPlayerPosition: position,
    isLoading: false,
    getCurrentPlayerRole: () => (position ? 'currentPlayer' : undefined),
    getOpponentRole: () => (position ? 'opponent' : undefined),
    isCurrentPlayer: (playerId: LegacyPlayerId) => (position ? playerId === position : false),
  };
};
