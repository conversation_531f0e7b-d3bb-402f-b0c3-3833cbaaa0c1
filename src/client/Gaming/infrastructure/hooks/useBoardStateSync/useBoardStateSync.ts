import {useBoardState} from '@/src/client/Gaming/infrastructure/hooks/useBoardState/useBoardState';
import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';

type BoardStateHook = (matchId: string | undefined) => { isLoading: boolean; boardState?: unknown };
type PlayerHandsHook = (matchId: string | undefined) => { isLoading: boolean; player1: unknown[]; player2: unknown[] };

export const useBoardStateSync = (
  matchId: string | undefined,
  boardStateHook: BoardStateHook = useBoardState,
  playerHandsHook: PlayerHandsHook = usePlayerHands
) => {
  const {isLoading: isBoardStateLoading} = boardStateHook(matchId);
  const {isLoading: isPlayerHandsLoading} = playerHandsHook(matchId);

  return {
    isLoading: isBoardStateLoading || isPlayerHandsLoading,
    error: false
  };
};
