import {useRouter} from "next/navigation";
import {useParams} from "next/navigation";
import {useQuery} from "convex/react";
import {api} from "@/convex/_generated/api";
import {useEffect} from "react";

export const useMatchMulliganPage = (matchId: string): void => {
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  
  const viewModel = useQuery(api.queries.match.getMatchMeta, {matchId});

  useEffect(() => {
    if (viewModel?.error) {
      router.push(`/${locale}/matches/${matchId}/error?error=${encodeURIComponent(viewModel.error)}`);
      return;
    }

    const status = viewModel?.data?.status;
    if (status === 'finished') {
      router.push(`/${locale}/matches/${matchId}/finished`);
    } else if (status !== 'waiting_for_mulligan') {
      router.push(`/${locale}/matches/${matchId}/game`);
    }
  }, [viewModel, router, locale, matchId]);
};