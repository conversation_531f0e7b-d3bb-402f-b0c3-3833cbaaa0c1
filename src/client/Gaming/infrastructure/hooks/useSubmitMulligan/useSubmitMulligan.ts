import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';

type SubmitMulliganArgs = {
  matchId: string;
  selectedCardIds: string[];
  skipped: boolean;
  round: number;
};

export const useSubmitMulligan = () => {
  const mutation = useMutation(api.mutations.submitMulliganSelection.endpoint);

  const submitMulligan = async (args: SubmitMulliganArgs) => {
    return await mutation({
      matchId: args.matchId,
      selectedCardIds: args.selectedCardIds,
      skipped: args.skipped,
      round: args.round,
    });
  };

  return { submitMulligan };
};