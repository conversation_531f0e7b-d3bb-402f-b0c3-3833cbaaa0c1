import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';

export type PlayerDeckSizes = {
  currentPlayerPosition?: 'player1' | 'player2';
  myDeckSize: number;
  opponentDeckSize: number;
  isLoading: boolean;
  error?: string;
};

export const usePlayerDeckSizes = (matchId: string | undefined): PlayerDeckSizes => {
  const queryResult = useQuery(
    api.queries.match.loadMatchData,
    matchId ? { matchId } : 'skip'
  ) as { error: string | null; data: { myCardIds: unknown[]; opponentCardCount: number; currentPlayerPosition?: 'player1' | 'player2' } | null } | undefined;

  if (!matchId) {
    return {
      currentPlayerPosition: undefined,
      myDeckSize: 0,
      opponentDeckSize: 0,
      isLoading: false,
      error: undefined,
    };
  }

  if (queryResult === undefined) {
    return {
      currentPlayerPosition: undefined,
      myDeckSize: 0,
      opponentDeckSize: 0,
      isLoading: true,
      error: undefined,
    };
  }

  if (queryResult === null || queryResult.error) {
    return {
      currentPlayerPosition: undefined,
      myDeckSize: 0,
      opponentDeckSize: 0,
      isLoading: false,
      error: queryResult?.error ?? 'Unknown error',
    };
  }

  return {
    currentPlayerPosition: queryResult.data?.currentPlayerPosition,
    myDeckSize: queryResult.data ? queryResult.data.myCardIds.length : 0,
    opponentDeckSize: queryResult.data?.opponentCardCount ?? 0,
    isLoading: false,
    error: undefined,
  };
};

