import {useIsCurrentPlayerTurn} from '@/src/client/Gaming/infrastructure/hooks/useIsCurrentPlayerTurn/useIsCurrentPlayerTurn';
import {ThreeEvent} from '@react-three/fiber';

type UseTurnBasedCursorProps = {
  matchId: string;
  allowInteraction?: boolean;
};

type TurnBasedCursorHandlers = {
  onPointerOver: (event: ThreeEvent<PointerEvent>) => void;
  onPointerOut: (event: ThreeEvent<PointerEvent>) => void;
};

export const useTurnBasedCursor = ({
  matchId,
  allowInteraction = true,
}: UseTurnBasedCursorProps): TurnBasedCursorHandlers & {
  shouldShowPointer: boolean;
  isPlayerTurn: boolean;
} => {
  const {isCurrentPlayerTurn} = useIsCurrentPlayerTurn(matchId);

  const shouldShowPointer = isCurrentPlayerTurn && allowInteraction;

  const onPointerOver = (event: ThreeEvent<PointerEvent>) => {
    event.stopPropagation();
    document.body.style.cursor = shouldShowPointer ? 'pointer' : 'default';
  };

  const onPointerOut = (event: ThreeEvent<PointerEvent>) => {
    event.stopPropagation();
    document.body.style.cursor = 'default';
  };

  return {
    onPointerOver,
    onPointerOut,
    shouldShowPointer,
    isPlayerTurn: isCurrentPlayerTurn,
  };
};