import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';
import {useGameErrorContext} from '@/src/client/Gaming/infrastructure/providers/GameErrorProvider/useGameErrorContext';
import {useTranslations} from 'next-intl';
import {useRef} from 'react';

type PlaceCardArgs = {
  matchId: string;
  cardId: string;
  rowType: 'first' | 'second';
  slotIndex: number;
};

const ERROR_MESSAGE_KEYS: Record<string, string> = {
  'It is not your turn to play a card': 'notYourTurn',
  'No current turn is set in this match': 'noTurnSet', 
  'Cannot play card in non-active match': 'matchNotActive',
  'Player is not part of this match': 'playerNotInMatch'
};

const getErrorKey = (errorMessage: string): string | undefined => {
  if (ERROR_MESSAGE_KEYS[errorMessage]) {
    return ERROR_MESSAGE_KEYS[errorMessage];
  }
  
  if (errorMessage.includes('not your turn')) {
    return 'notYourTurn';
  }
  if (errorMessage.includes('No current turn')) {
    return 'noTurnSet';
  }
  if (errorMessage.includes('non-active match')) {
    return 'matchNotActive';
  }
  if (errorMessage.includes('not part of this match')) {
    return 'playerNotInMatch';
  }
  if (errorMessage.includes('has already been played')) {
    return 'cardAlreadyPlayed';
  }

  return undefined;
};

export const usePlaceCard = () => {
  const mutation = useMutation(api.mutations.playCard.endpoint);
  const {showError} = useGameErrorContext();
  const t = useTranslations('games.errors');
  const pendingMutationsRef = useRef<Set<string>>(new Set());

  const placeCard = async (args: PlaceCardArgs) => {
    // Create a unique key for this specific card placement request
    const mutationKey = `${args.matchId}-${args.cardId}-${args.rowType}-${args.slotIndex}`;

    // If this exact mutation is already pending, ignore the duplicate
    if (pendingMutationsRef.current.has(mutationKey)) {
      console.log(`Ignoring duplicate mutation for ${mutationKey}`);
      return;
    }

    // Mark this mutation as pending
    pendingMutationsRef.current.add(mutationKey);

    try {
      const result = await mutation({
        matchId: args.matchId as Id<'matches'>,
        cardId: args.cardId,
        rowType: args.rowType,
        slotIndex: args.slotIndex,
      });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // If it's a "card already played" error, don't show it to the user
      // This is expected behavior when rapid clicking
      if (errorMessage.includes('has already been played')) {
        console.log(`Card ${args.cardId} already played - ignoring error from rapid clicking`);
        return; // Don't throw, just return silently
      }

      const errorKey = getErrorKey(errorMessage);
      const translatedMessage = errorKey ? t(errorKey) : t('unknownError');
      showError(translatedMessage);
      throw error;
    } finally {
      // Always remove the mutation from pending set
      pendingMutationsRef.current.delete(mutationKey);
    }
  };

  return { placeCard };
};