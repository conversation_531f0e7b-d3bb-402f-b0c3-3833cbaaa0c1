import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';
import {useGameErrorContext} from '@/src/client/Gaming/infrastructure/providers/GameErrorProvider/useGameErrorContext';
import {useTranslations} from 'next-intl';

type PlaceCardArgs = {
  matchId: string;
  cardId: string;
  rowType: 'first' | 'second';
  slotIndex: number;
};

const ERROR_MESSAGE_KEYS: Record<string, string> = {
  'It is not your turn to play a card': 'notYourTurn',
  'No current turn is set in this match': 'noTurnSet', 
  'Cannot play card in non-active match': 'matchNotActive',
  'Player is not part of this match': 'playerNotInMatch'
};

const getErrorKey = (errorMessage: string): string | undefined => {
  if (ERROR_MESSAGE_KEYS[errorMessage]) {
    return ERROR_MESSAGE_KEYS[errorMessage];
  }
  
  if (errorMessage.includes('not your turn')) {
    return 'notYourTurn';
  }
  if (errorMessage.includes('No current turn')) {
    return 'noTurnSet';
  }
  if (errorMessage.includes('non-active match')) {
    return 'matchNotActive';
  }
  if (errorMessage.includes('not part of this match')) {
    return 'playerNotInMatch';
  }
  
  return undefined;
};

export const usePlaceCard = () => {
  const mutation = useMutation(api.mutations.playCard.endpoint);
  const {showError} = useGameErrorContext();
  const t = useTranslations('games.errors');

  const placeCard = async (args: PlaceCardArgs) => {
    try {
      return await mutation({
        matchId: args.matchId as Id<'matches'>,
        cardId: args.cardId,
        rowType: args.rowType,
        slotIndex: args.slotIndex,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorKey = getErrorKey(errorMessage);
      const translatedMessage = errorKey ? t(errorKey) : t('unknownError');
      showError(translatedMessage);
      throw error;
    }
  };

  return { placeCard };
};