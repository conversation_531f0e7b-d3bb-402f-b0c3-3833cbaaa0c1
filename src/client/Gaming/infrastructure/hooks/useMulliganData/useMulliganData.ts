import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';

type MulliganData = {
  currentRound: number;
  maxRounds: number;
  cardsInHand: Array<{
    id: string;
    catalogCardId: string;
    name: string;
    imageUrl: string;
  }>;
  selectedCardIds: string[];
  canSkip: boolean;
  timeLimit?: number;
  hasCompletedAllRounds: boolean;
};

type ViewModel = {
  error?: string | null;
  data?: MulliganData | null;
};

export const useMulliganData = (
  matchId: string | undefined
): {
  mulliganData: MulliganData | undefined;
  isLoading: boolean;
  error: string | undefined;
} => {
  const viewModel = useQuery(
    api.queries.gaming.loadMulliganData,
    matchId ? { matchId } : 'skip'
  ) as ViewModel | undefined;

  if (matchId === undefined) {
    return { mulliganData: undefined, isLoading: false, error: undefined };
  }

  if (viewModel === undefined) {
    return { mulliganData: undefined, isLoading: true, error: undefined };
  }

  return {
    mulliganData: viewModel.data ?? undefined,
    isLoading: false,
    error: viewModel.error ?? undefined,
  };
};
