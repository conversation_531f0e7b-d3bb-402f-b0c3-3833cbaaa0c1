import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';

type QueueItem = {
  id: string;
  gameId: string;
  deckId: string;
  queuedAt: number;
};

type QueueStatus = {
  isInQueue: boolean;
  queueItem: QueueItem | null;
  match: string | null;
};

export const useQueueStatus = (
  gameId: string | undefined
): {
  queueStatus: QueueStatus | undefined;
  isLoading: boolean;
  error: string | undefined;
} => {
  const queryResult = useQuery(
    api.queries.matchMakingQueue.getPlayerQueueStatus,
    gameId ? { gameId } : 'skip'
  );

  if (gameId === undefined) {
    return {
      queueStatus: undefined,
      isLoading: false,
      error: undefined,
    };
  }

  if (queryResult === undefined) {
    return {
      queueStatus: undefined,
      isLoading: true,
      error: undefined,
    };
  }

  return {
    queueStatus: queryResult,
    isLoading: false,
    error: undefined,
  };
};