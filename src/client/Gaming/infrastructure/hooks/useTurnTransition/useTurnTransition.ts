import {useEffect, useRef, useState} from 'react';
import {useIsCurrentPlayerTurn} from '../useIsCurrentPlayerTurn/useIsCurrentPlayerTurn';

export const useTurnTransition = (matchId: string | undefined): {
  showTurnSplash: boolean;
  dismissTurnSplash: () => void;
} => {
  const {isCurrentPlayerTurn, isLoading} = useIsCurrentPlayerTurn(matchId);
  const [showTurnSplash, setShowTurnSplash] = useState(false);
  const previousTurnState = useRef<boolean>(false);
  const hasInitialized = useRef<boolean>(false);

  useEffect(() => {
    if (isLoading) {
      return;
    }

    if (!hasInitialized.current) {
      hasInitialized.current = true;
      previousTurnState.current = isCurrentPlayerTurn;
      return;
    }

    if (!previousTurnState.current && isCurrentPlayerTurn) {
      setShowTurnSplash(true);
    }

    previousTurnState.current = isCurrentPlayerTurn;
  }, [isCurrentPlayerTurn, isLoading]);

  const dismissTurnSplash = () => {
    setShowTurnSplash(false);
  };

  return {
    showTurnSplash,
    dismissTurnSplash,
  };
};