import {useState} from 'react';
import {useMulliganData} from '@/src/client/Gaming/infrastructure/hooks/useMulliganData/useMulliganData';
import {useSubmitMulligan} from '@/src/client/Gaming/infrastructure/hooks/useSubmitMulligan/useSubmitMulligan';

type UseMulliganScreenProps = {
  matchId: string;
};

type HandCard = {
  instanceId: string;
  id: string;
  imageUrl: string;
};

type MulliganData = {
  cardsInHand?: Array<{ id: string; imageUrl?: string }>;
  currentRound?: number;
  maxRounds?: number;
  hasCompletedAllRounds?: boolean;
};

type SubmitMulliganArgs = {
  matchId: string;
  selectedCardIds: string[];
  skipped: boolean;
  round: number;
};

type MulliganDataHook = (matchId: string) => { mulliganData?: MulliganData; isLoading: boolean; error?: string };
type SubmitMulliganHook = () => { submitMulligan: (params: SubmitMulliganArgs) => Promise<unknown> };

export const useMulliganScreen = (
  {matchId}: UseMulliganScreenProps,
  mulliganDataHook: MulliganDataHook = useMulliganData,
  submitMulliganHook: SubmitMulliganHook = useSubmitMulligan
) => {
  const {mulliganData, isLoading, error} = mulliganDataHook(matchId);
  const {submitMulligan} = submitMulliganHook();
  const [selectedCardIds, setSelectedCardIds] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handCards: HandCard[] = mulliganData && mulliganData.cardsInHand ? 
    mulliganData.cardsInHand.map((card: { id: string; imageUrl?: string }) => ({
      instanceId: card.id,
      id: card.id,
      imageUrl: card.imageUrl || '/placeholder-card.png'
    })) : [];

  const finalHandCards: HandCard[] = [];

  const onToggleCard = (cardId: string) => {
    setSelectedCardIds(prev => 
      prev.includes(cardId) 
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const onSkip = async () => {
    try {
      setIsSubmitted(true);
      await submitMulligan({
        matchId,
        selectedCardIds: [],
        skipped: true,
        round: mulliganData?.currentRound || 1
      });
    } catch (error) {
      setIsSubmitted(false);
      console.error('Failed to skip mulligan:', error);
    }
  };

  const onSubmit = async () => {
    try {
      const isLastRound = (mulliganData?.currentRound || 1) >= (mulliganData?.maxRounds || 1);
      if (isLastRound) {
        setIsSubmitted(true);
      }
      await submitMulligan({
        matchId,
        selectedCardIds,
        skipped: false,
        round: mulliganData?.currentRound || 1
      });
    } catch (error) {
      setIsSubmitted(false);
      console.error('Failed to submit mulligan:', error);
    }
  };

  return {
    handCards,
    finalHandCards,
    selectedCardIds,
    isLoading,
    error,
    isSubmitted,
    currentRound: mulliganData?.currentRound || 1,
    maxRounds: mulliganData?.maxRounds || 1,
    hasCompletedAllRounds: mulliganData?.hasCompletedAllRounds ?? false,
    onToggleCard,
    onSkip,
    onSubmit
  };
};
