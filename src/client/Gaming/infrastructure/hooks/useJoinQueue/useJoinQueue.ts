import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';

type JoinQueueArgs = {
  gameId: string;
  deckId: string;
};

type JoinQueueResult = {
  success: boolean;
  redirectUrl?: string;
};

export const useJoinQueue = () => {
  const mutation = useMutation(api.mutations.addPlayerToMatchMakingQueue.endpoint);

  const joinQueue = async (args: JoinQueueArgs): Promise<JoinQueueResult> => {
    try {
      await mutation({
        gameId: args.gameId,
        deckId: args.deckId,
      });
      
      return {
        success: true,
        redirectUrl: `/waiting-for-opponent?gameId=${args.gameId}`
      };
    } catch {
      return {
        success: false
      };
    }
  };

  return { joinQueue };
};