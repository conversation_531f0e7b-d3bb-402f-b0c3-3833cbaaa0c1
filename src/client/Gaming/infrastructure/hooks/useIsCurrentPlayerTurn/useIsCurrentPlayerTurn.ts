import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';

export const useIsCurrentPlayerTurn = (matchId: string | undefined): {
  isCurrentPlayerTurn: boolean;
  isLoading: boolean;
  noTurnSet: boolean;
} => {
  const meta = useQuery(
    api.queries.match.getMatchMeta,
    matchId ? { matchId } : 'skip'
  ) as
    | { error: string | null; data: { currentPlayerPosition?: string; currentTurn?: string; players?: string[] } | null }
    | undefined;

  if (!matchId || !meta || meta.error || !meta.data) {
    return {
      isCurrentPlayerTurn: false,
      isLoading: !meta,
      noTurnSet: false,
    };
  }

  const { currentPlayerPosition, currentTurn, players } = meta.data;
  
  if (!currentPlayerPosition || !players) {
    return {
      isCurrentPlayerTurn: false,
      isLoading: false,
      noTurnSet: false,
    };
  }

  if (!currentTurn) {
    return {
      isCurrentPlayerTurn: false,
      isLoading: false,
      noTurnSet: true,
    };
  }

  const currentUserId = currentPlayerPosition === 'player1' ? players[0] : players[1];
  
  return {
    isCurrentPlayerTurn: currentTurn === currentUserId,
    isLoading: false,
    noTurnSet: false,
  };
};