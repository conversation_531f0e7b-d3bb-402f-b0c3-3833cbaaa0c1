import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';

export const useCardLookup = (matchId: string | undefined) => {
  const {player1, player2} = usePlayerHands(matchId);
  
  // Create a map from instance ID to image identifier
  const cardMap = new Map<string, string>();
  
  // Add all hand cards to the lookup
  [...player1, ...player2].forEach(card => {
    if (card?.instanceId && card?.image) {
      cardMap.set(card.instanceId, card.image);
    }
  });
  
  const getCardImage = (instanceId: string): string | undefined => {
    return cardMap.get(instanceId);
  };
  
  const getCardImageUrl = (instanceId: string): string => {
    const imageId = cardMap.get(instanceId);
    if (!imageId) {
      return '/game-assets/cards/back.png';
    }
    
    if (imageId.startsWith('/game-assets/') || imageId.startsWith('http')) {
      return imageId;
    }
    
    const cleanImageId = imageId.endsWith('.jpg') ? imageId.replace('.jpg', '') : imageId;
    return `/game-assets/cards/en/thumbnail/${cleanImageId}.jpg`;
  };
  
  return {
    getCardImage,
    getCardImageUrl
  };
};