import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';

export type CancelMatchRegistrationArgs = {
  gameId: string;
  locale: string;
};

export const useCancelMatchRegistration = () => {
  const mutation = useMutation(api.mutations.cancelMatchRegistration.endpoint);

  const cancelMatchRegistration = async (args: CancelMatchRegistrationArgs) => {
    await mutation({
      gameId: args.gameId,
    });
    
    return {
      success: true,
      redirectUrl: `/${args.locale}/games/${args.gameId}/play`
    };
  };

  return { cancelMatchRegistration };
};