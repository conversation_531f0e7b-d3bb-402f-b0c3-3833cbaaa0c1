import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';

type DeckPreview = {
  id: string;
  name: string;
  cardCount: number;
  gameId: string;
  images: string[];
};

type ViewModel = {
  error?: string | null;
  data?: { decks: DeckPreview[] } | null;
};

export const useAvailableDecks = (
  gameId: string | undefined,
  locale: string = 'en'
): {
  availableDecks: DeckPreview[];
  isLoading: boolean;
  error: string | undefined;
} => {
  const viewModel = useQuery(
    api.queries.deck.loadDecksByUserIdAndGameId,
    gameId ? { gameId: gameId as Id<'games'>, locale } : 'skip'
  ) as ViewModel | undefined;

  if (gameId === undefined) {
    return {
      availableDecks: [],
      isLoading: false,
      error: undefined,
    };
  }

  if (viewModel === undefined) {
    return {
      availableDecks: [],
      isLoading: true,
      error: undefined,
    };
  }

  return {
    availableDecks: viewModel.data?.decks ?? [],
    isLoading: false,
    error: viewModel.error ?? undefined,
  };
};