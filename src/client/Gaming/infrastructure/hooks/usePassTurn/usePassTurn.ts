import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';

type PassTurnArgs = {
  matchId: string;
};

export const usePassTurn = () => {
  const mutation = useMutation(api.mutations.passTurn.endpoint);

  const passTurn = async (args: PassTurnArgs) => {
    return await mutation({
      matchId: args.matchId as Id<'matches'>,
    });
  };

  return { passTurn };
};