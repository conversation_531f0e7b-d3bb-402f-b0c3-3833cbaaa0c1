import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

export interface PlayerInfo {
  id: string;
  name: string;
  avatar: string;
}

export const usePlayersInfo = (matchId: string | undefined): {
  currentPlayer: PlayerInfo | null;
  opponent: PlayerInfo | null;
  isLoading: boolean;
} => {
  const result = useQuery(
    api.queries.gaming.getMatchPlayers,
    matchId ? { matchId } : 'skip'
  );

  return {
    currentPlayer: result?.data?.currentPlayer || null,
    opponent: result?.data?.opponent || null,
    isLoading: result === undefined && matchId !== undefined,
  };
};