import {useQuery} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';

type PlacedCard = {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: string;
  image: string;
};

type PlayerBoard = {
  firstRow: (PlacedCard | null)[];
  secondRow: (PlacedCard | null)[];
};

type BoardState = {
  player1Board: PlayerBoard;
  player2Board: PlayerBoard;
};

export const useBoardState = (
  matchId: string | undefined
): {
  boardState: BoardState | undefined;
  currentTurn: string | undefined;
  gamePhase: string | undefined;
  isLoading: boolean;
  error: string | undefined;
} => {
  const queryResult = useQuery(
    api.queries.board.getBoardState,
    matchId ? { matchId: matchId as Id<'matches'> } : 'skip'
  );

  if (matchId === undefined) {
    return {
      boardState: undefined,
      currentTurn: undefined,
      gamePhase: undefined,
      isLoading: false,
      error: undefined,
    };
  }

  if (queryResult === undefined) {
    return {
      boardState: undefined,
      currentTurn: undefined,
      gamePhase: undefined,
      isLoading: true,
      error: undefined,
    };
  }

  if (queryResult === null) {
    return {
      boardState: undefined,
      currentTurn: undefined,
      gamePhase: undefined,
      isLoading: false,
      error: 'Match not found',
    };
  }

  return {
    boardState: queryResult.boardState,
    currentTurn: queryResult.currentTurn,
    gamePhase: queryResult.gamePhase,
    isLoading: false,
    error: undefined,
  };
};
