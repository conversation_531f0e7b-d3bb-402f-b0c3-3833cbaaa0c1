import {useMutation} from 'convex/react';
import {api} from '@/convex/_generated/api';
import type {Id} from '@/convex/_generated/dataModel';

type LeaveMatchArgs = {
  matchId: string;
};

export const useLeaveMatch = () => {
  const mutation = useMutation(api.mutations.leaveMatch.endpoint);

  const leaveMatch = async (args: LeaveMatchArgs) => {
    return await mutation({
      matchId: args.matchId as Id<'matches'>,
    });
  };

  return { leaveMatch };
};