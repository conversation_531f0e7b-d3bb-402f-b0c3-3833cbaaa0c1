'use client';

import {ReactNode} from 'react';
import {GameErrorContext} from './GameErrorContext';
import {useGameError} from '@/src/client/Gaming/infrastructure/hooks/useGameError/useGameError';

type Props = {
  children: ReactNode;
};

export const GameErrorProvider = ({children}: Props) => {
  const gameError = useGameError();

  return (
    <GameErrorContext.Provider value={gameError}>
      {children}
    </GameErrorContext.Provider>
  );
};