import {createContext} from "react";

type ZoomedCard = {
  cardId: string;
  imageUrl?: string;
  isVisible: boolean;
} | null;

type CardZoomContextValue = {
  zoomedCard: ZoomedCard;
  showZoomedCard: (cardId: string, imageUrl?: string) => void;
  hideZoomedCard: () => void;
  isVisible: boolean;
};

export const CardZoomContext = createContext<CardZoomContextValue>({
  zoomedCard: null,
  showZoomedCard: () => {},
  hideZoomedCard: () => {},
  isVisible: false,
});