'use client';

import {FC, PropsWithChildren, useState} from 'react';
import {CardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/CardZoomContext';

type ZoomedCard = {
  cardId: string;
  imageUrl?: string;
  isVisible: boolean;
} | null;

export const CardZoomProvider: FC<PropsWithChildren> = ({children}) => {
  const [zoomedCard, setZoomedCard] = useState<ZoomedCard>(null);

  const showZoomedCard = (cardId: string, imageUrl?: string) => {
    setZoomedCard({ cardId, imageUrl, isVisible: true });
  };

  const hideZoomedCard = () => {
    setZoomedCard(null);
  };

  const isVisible = zoomedCard?.isVisible ?? false;

  return (
    <CardZoomContext.Provider value={{
      zoomedCard,
      showZoomedCard,
      hideZoomedCard,
      isVisible,
    }}>
      {children}
    </CardZoomContext.Provider>
  );
};