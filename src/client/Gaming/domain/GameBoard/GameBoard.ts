import {GameBoardState, PlacedCard, PlayerBoard} from './GameBoardTypes';

export type {GameBoardState, PlacedCard, PlayerBoard};
import {PlayerRole, LegacyPlayerId, convertRoleToLegacy} from '../PlayerRole/PlayerRole';

type GameBoardEvent = {
  type: 'cardPlacedOnBoard' | 'cardRemovedFromBoard' | 'boardCleared' | 'cardPlacedByRole' | 'cardRemovedByRole';
  payload: unknown;
};

export class GameBoard {
  private events: GameBoardEvent[] = [];
  private state: GameBoardState;

  private constructor(state: GameBoardState) {
    this.state = {
      player1Board: {
        firstRow: [...state.player1Board.firstRow],
        secondRow: [...state.player1Board.secondRow],
      },
      player2Board: {
        firstRow: [...state.player2Board.firstRow],
        secondRow: [...state.player2Board.secondRow],
      },
      maxSlotsPerRow: state.maxSlotsPerRow,
    };
  }

  static fromState(state: GameBoardState): GameBoard {
    return new GameBoard(state);
  }

  placeCard(
    playerId: 'player1' | 'player2',
    cardId: string,
    rowType: 'first' | 'second',
    slotIndex: number
  ): void {
    const board = this.state[`${playerId}Board`];
    const row = rowType === 'first' ? board.firstRow : board.secondRow;

    if (slotIndex < 0 || slotIndex >= this.state.maxSlotsPerRow) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }

    if (row[slotIndex] !== null) {
      throw new Error(`Slot ${slotIndex} in ${playerId} ${rowType} row is already occupied`);
    }

    const placedCard: PlacedCard = {
      cardId,
      placedAt: new Date().toISOString(),
      slotIndex,
      rowType,
    };

    row[slotIndex] = placedCard;
  }

  removeCard(
    playerId: 'player1' | 'player2',
    rowType: 'first' | 'second',
    slotIndex: number
  ): void {
    const board = this.state[`${playerId}Board`];
    const row = rowType === 'first' ? board.firstRow : board.secondRow;

    if (slotIndex < 0 || slotIndex >= this.state.maxSlotsPerRow) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }

    if (row[slotIndex] === null) {
      throw new Error(`No card to remove at slot ${slotIndex} in ${playerId} ${rowType} row`);
    }

    row[slotIndex] = null;
  }

  clearBoard(): void {
    this.state.player1Board.firstRow.fill(null);
    this.state.player1Board.secondRow.fill(null);
    this.state.player2Board.firstRow.fill(null);
    this.state.player2Board.secondRow.fill(null);
  }

  isSlotEmpty(
    playerId: 'player1' | 'player2',
    rowType: 'first' | 'second',
    slotIndex: number
  ): boolean {
    const board = this.state[`${playerId}Board`];
    const row = rowType === 'first' ? board.firstRow : board.secondRow;
    return slotIndex >= 0 && slotIndex < this.state.maxSlotsPerRow && row[slotIndex] === null;
  }

  getPlacedCard(
    playerId: 'player1' | 'player2',
    rowType: 'first' | 'second',
    slotIndex: number
  ): PlacedCard | null {
    const board = this.state[`${playerId}Board`];
    const row = rowType === 'first' ? board.firstRow : board.secondRow;
    return slotIndex >= 0 && slotIndex < this.state.maxSlotsPerRow ? row[slotIndex] : null;
  }

  placeCardByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    cardId: string,
    rowType: 'first' | 'second',
    slotIndex: number
  ): void {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    const board = this.state[`${legacyPlayerId}Board`];
    const row = rowType === 'first' ? board.firstRow : board.secondRow;

    if (slotIndex < 0 || slotIndex >= this.state.maxSlotsPerRow) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }

    if (row[slotIndex] !== null) {
      throw new Error(`Slot ${slotIndex} in ${role} ${rowType} row is already occupied`);
    }

    const placedCard: PlacedCard = {
      cardId,
      placedAt: new Date().toISOString(),
      slotIndex,
      rowType,
    };

    row[slotIndex] = placedCard;
  }

  removeCardByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    rowType: 'first' | 'second',
    slotIndex: number
  ): void {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    const board = this.state[`${legacyPlayerId}Board`];
    const row = rowType === 'first' ? board.firstRow : board.secondRow;

    if (slotIndex < 0 || slotIndex >= this.state.maxSlotsPerRow) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }

    if (row[slotIndex] === null) {
      throw new Error(`No card to remove at slot ${slotIndex} in ${role} ${rowType} row`);
    }

    row[slotIndex] = null;
  }

  isSlotEmptyByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    rowType: 'first' | 'second',
    slotIndex: number
  ): boolean {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    return this.isSlotEmpty(legacyPlayerId, rowType, slotIndex);
  }

  getPlacedCardByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    rowType: 'first' | 'second',
    slotIndex: number
  ): PlacedCard | null {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    return this.getPlacedCard(legacyPlayerId, rowType, slotIndex);
  }

  getDomainEvents(): GameBoardEvent[] {
    const evts = [...this.events];
    this.events = [];
    return evts;
  }
}