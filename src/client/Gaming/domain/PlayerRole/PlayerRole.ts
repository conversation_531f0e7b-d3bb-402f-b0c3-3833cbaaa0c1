export type PlayerRole = 'currentPlayer' | 'opponent';
export type LegacyPlayerId = 'player1' | 'player2';

export const convertLegacyToRole = (
  playerId: LegacyPlayerId, 
  currentPlayerPosition: LegacyPlayerId
): PlayerRole => {
  return playerId === currentPlayerPosition ? 'currentPlayer' : 'opponent';
};

export const convertRoleToLegacy = (
  role: PlayerRole,
  currentPlayerPosition: LegacyPlayerId
): LegacyPlayerId => {
  if (role === 'currentPlayer') {
    return currentPlayerPosition;
  }
  return currentPlayerPosition === 'player1' ? 'player2' : 'player1';
};

export const getOppositeRole = (role: PlayerRole): PlayerRole => {
  return role === 'currentPlayer' ? 'opponent' : 'currentPlayer';
};

export const isValidPlayerRole = (value: string): value is PlayerRole => {
  return value === 'currentPlayer' || value === 'opponent';
};

export const isValidLegacyPlayerId = (value: string): value is LegacyPlayerId => {
  return value === 'player1' || value === 'player2';
};