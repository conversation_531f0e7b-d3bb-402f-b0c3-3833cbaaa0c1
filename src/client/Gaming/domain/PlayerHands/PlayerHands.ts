import {PlayerHandsState} from './PlayerHandsTypes';
import {PlayerRole, LegacyPlayerId, convertRoleToLegacy} from '../PlayerRole/PlayerRole';

export type {PlayerHandsState};

type PlayerHandsEvent = {
  type: 'player1HandUpdated' | 'player2HandUpdated' | 'player2HandCountUpdated';
  payload: unknown;
};

export class PlayerHands {
  private events: PlayerHandsEvent[] = [];
  private state: PlayerHandsState;

  private constructor(state: PlayerHandsState) {
    this.state = state;
  }

  static fromState(state: PlayerHandsState): PlayerHands {
    return new PlayerHands({
      player1: [...state.player1],
      player2: [...state.player2],
      player2Count: state.player2Count,
    });
  }

  updateHand(player: 'player1' | 'player2', cardIds: string[]): void {
    if (player === 'player1') {
      this.state.player1 = [...cardIds];
    } else {
      this.state.player2 = [...cardIds];
      this.state.player2Count = cardIds.length;
    }
  }

  removeCardFromHand(player: 'player1' | 'player2', cardId: string): void {
    if (player === 'player1') {
      const cardIndex = this.state.player1.indexOf(cardId);
      if (cardIndex === -1) {
        throw new Error(`Card ${cardId} is not in ${player} hand`);
      }
      this.state.player1 = this.state.player1.filter(id => id !== cardId);
    } else {
      const cardIndex = this.state.player2.indexOf(cardId);
      if (cardIndex === -1) {
        throw new Error(`Card ${cardId} is not in ${player} hand`);
      }
      this.state.player2 = this.state.player2.filter(id => id !== cardId);
      this.state.player2Count = this.state.player2.length;
    }
  }

  addCardToHand(player: 'player1' | 'player2', cardId: string): void {
    if (player === 'player1') {
      this.state.player1 = [...this.state.player1, cardId];
    } else {
      this.state.player2 = [...this.state.player2, cardId];
      this.state.player2Count = this.state.player2.length;
    }
  }

  updateHandByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    cardIds: string[]
  ): void {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    this.updateHand(legacyPlayerId, cardIds);
  }

  removeCardFromHandByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    cardId: string
  ): void {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    this.removeCardFromHand(legacyPlayerId, cardId);
  }

  addCardToHandByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId,
    cardId: string
  ): void {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    this.addCardToHand(legacyPlayerId, cardId);
  }

  getHandCountByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId
  ): number {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    if (legacyPlayerId === 'player1') {
      return this.state.player1.length;
    } else if (legacyPlayerId === 'player2') {
      return this.state.player2.length;
    }
    return this.state.player2Count;
  }

  getHandCardsByRole(
    role: PlayerRole,
    currentPlayerPosition: LegacyPlayerId
  ): string[] {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    if (legacyPlayerId === 'player1') {
      return [...this.state.player1];
    } else if (legacyPlayerId === 'player2') {
      return [...this.state.player2];
    }
    return [];
  }

  getDomainEvents(): PlayerHandsEvent[] {
    const evts = [...this.events];
    this.events = [];
    return evts;
  }
}

