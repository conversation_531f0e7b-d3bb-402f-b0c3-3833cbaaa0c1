import {renderHook, act} from '@testing-library/react';
import {CardZoomProvider} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/CardZoomProvider';
import {useCardZoomContext} from '@/src/client/Gaming/infrastructure/providers/CardZoomProvider/useCardZoomContext';

const wrapper = ({children}: {children: React.ReactNode}) => (
  <CardZoomProvider>{children}</CardZoomProvider>
);

describe('When using CardZoomProvider', () => {
  it('should initialize with no zoomed card', () => {
    // Act
    const {result} = renderHook(() => useCardZoomContext(), {wrapper});

    // Assert
    expect(result.current.zoomedCard).toBe(null);
    expect(result.current.isVisible).toBe(false);
  });

  it('should show zoomed card when showZoomedCard is called without image URL', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoomContext(), {wrapper});

    // Act
    act(() => {
      result.current.showZoomedCard('card123');
    });

    // Assert
    expect(result.current.zoomedCard).toEqual({
      cardId: 'card123',
      imageUrl: undefined,
      isVisible: true,
    });
    expect(result.current.isVisible).toBe(true);
  });

  it('should show zoomed card with image URL when showZoomedCard is called with image URL', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoomContext(), {wrapper});

    // Act
    act(() => {
      result.current.showZoomedCard('card456', 'https://example.com/card456.jpg');
    });

    // Assert
    expect(result.current.zoomedCard).toEqual({
      cardId: 'card456',
      imageUrl: 'https://example.com/card456.jpg',
      isVisible: true,
    });
    expect(result.current.isVisible).toBe(true);
  });

  it('should hide zoomed card when hideZoomedCard is called', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoomContext(), {wrapper});

    act(() => {
      result.current.showZoomedCard('card456', 'https://example.com/card456.jpg');
    });

    // Act
    act(() => {
      result.current.hideZoomedCard();
    });

    // Assert
    expect(result.current.zoomedCard).toBe(null);
    expect(result.current.isVisible).toBe(false);
  });

  it('should replace current zoomed card when showing a different card', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoomContext(), {wrapper});

    act(() => {
      result.current.showZoomedCard('card123');
    });

    // Act
    act(() => {
      result.current.showZoomedCard('card456', 'https://example.com/card456.jpg');
    });

    // Assert
    expect(result.current.zoomedCard).toEqual({
      cardId: 'card456',
      imageUrl: 'https://example.com/card456.jpg',
      isVisible: true,
    });
    expect(result.current.isVisible).toBe(true);
  });
});