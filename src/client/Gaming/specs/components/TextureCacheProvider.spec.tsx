import {renderHook} from '@testing-library/react';
import {ReactNode} from 'react';

const loadCallbacks: Array<{ url: string; onLoad: (t: unknown) => void }> = [];

vi.mock('three', () => ({
  TextureLoader: class {
    load(url: string, onLoad: (t: unknown) => void) {
      const tex = { __url: url } as unknown;
      onLoad(tex);
      loadCallbacks.push({ url, onLoad });
    }
  },
  Texture: class {},
}));

const createFakePlayerHandsHook = (
  player1: Array<{ image?: string }> = [],
  player2: Array<{ image?: string }> = [],
  player1Count = 0,
  player2Count = 0,
  isLoading = false
) => () => ({
  player1,
  player2,
  player1Count,
  player2Count,
  isLoading,
});

const createFakeBoardStateHook = (
  boardState?: {
    player1Board: {
      firstRow: Array<{ image?: string }>;
      secondRow: Array<{ image?: string }>;
    };
    player2Board: {
      firstRow: Array<{ image?: string }>;
      secondRow: Array<{ image?: string }>;
    };
  },
  isLoading = false
) => () => ({
  boardState,
  isLoading,
});

describe('When using TextureCacheProvider', () => {
  const setupProvider = async (
    usePlayerHandsHook: ReturnType<typeof createFakePlayerHandsHook>,
    useBoardStateHook: ReturnType<typeof createFakeBoardStateHook>
  ) => {
    const mod = await import('@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache');
    const Provider = mod.TextureCacheProvider;
    return ({ wrapperProps }: { wrapperProps?: { matchId?: string } }) => ({
      wrapper: ({ children: c }: { children: ReactNode }) => (
        <Provider 
          matchId={wrapperProps?.matchId}
          usePlayerHandsHook={usePlayerHandsHook}
          useBoardStateHook={useBoardStateHook}
        >
          {c}
        </Provider>
      ),
    });
  };

  afterEach(() => {
    loadCallbacks.length = 0;
  });

  it('should expose a back texture without suspending', async () => {
    // Arrange
    const usePlayerHandsHook = createFakePlayerHandsHook([], [], 0, 0, false);
    const useBoardStateHook = createFakeBoardStateHook(undefined, false);
    const mod = await import('@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache');
    const useTextureCache = mod.useTextureCache;
    const withProvider = await setupProvider(usePlayerHandsHook, useBoardStateHook);
    
    // Act
    const { result } = renderHook(() => useTextureCache(), withProvider({ wrapperProps: { matchId: 'm1' } }));
    
    // Assert
    expect(result.current.backTexture).toBeDefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.hasError).toBe(false);
  });

  it('should resolve textures for both simple and canonical ids', async () => {
    // Arrange
    const usePlayerHandsHook = createFakePlayerHandsHook([{ image: 'alpha' }], [], 1, 0, false);
    const useBoardStateHook = createFakeBoardStateHook(undefined, false);
    const mod = await import('@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache');
    const useTextureCache = mod.useTextureCache;
    const withProvider = await setupProvider(usePlayerHandsHook, useBoardStateHook);
    
    // Act
    const { result } = renderHook(() => useTextureCache(), withProvider({ wrapperProps: { matchId: 'm1' } }));
    
    // Assert
    expect(result.current.getTexture('alpha')).toBeDefined();
    expect(
      result.current.getTexture('/game-assets/cards/en/thumbnail/alpha.jpg')
    ).toBeDefined();
  });
});
