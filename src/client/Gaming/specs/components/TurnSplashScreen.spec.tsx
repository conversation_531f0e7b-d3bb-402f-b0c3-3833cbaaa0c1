import {render, screen} from '@testing-library/react';
import TurnSplashScreen from '@/src/client/Gaming/infrastructure/components/TurnSplashScreen/TurnSplashScreen';
import {Theme} from '@radix-ui/themes';
import '@testing-library/jest-dom';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <Theme>
      {component}
    </Theme>
  );
};

describe('When rendering TurnSplashScreen', () => {
  it('should not render when show is false', () => {
    // Arrange
    const mockOnDismiss = vi.fn();

    // Act
    renderWithTheme(<TurnSplashScreen show={false} onDismiss={mockOnDismiss} />);

    // Assert
    expect(screen.queryByText('YOUR TURN!')).not.toBeInTheDocument();
  });

  it('should render splash screen when show is true', () => {
    // Arrange
    const mockOnDismiss = vi.fn();

    // Act
    renderWithTheme(<TurnSplashScreen show={true} onDismiss={mockOnDismiss} />);

    // Assert
    expect(screen.getByText('YOUR TURN!')).toBeInTheDocument();
  });

  it('should call onDismiss after 2 seconds when shown', async () => {
    // Arrange
    const mockOnDismiss = vi.fn();
    vi.useFakeTimers();

    // Act
    renderWithTheme(<TurnSplashScreen show={true} onDismiss={mockOnDismiss} />);
    vi.advanceTimersByTime(2000);

    // Assert
    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
    
    vi.useRealTimers();
  });

  it('should not call onDismiss when show becomes false before timeout', () => {
    // Arrange
    const mockOnDismiss = vi.fn();
    vi.useFakeTimers();
    const {rerender} = renderWithTheme(<TurnSplashScreen show={true} onDismiss={mockOnDismiss} />);

    // Act
    rerender(
      <Theme>
        <TurnSplashScreen show={false} onDismiss={mockOnDismiss} />
      </Theme>
    );
    vi.advanceTimersByTime(2000);

    // Assert
    expect(mockOnDismiss).not.toHaveBeenCalled();
    
    vi.useRealTimers();
  });
});