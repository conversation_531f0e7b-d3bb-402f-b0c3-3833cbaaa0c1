import {createFakeCurrentPlayerHook} from "@/src/client/Gaming/specs/fakes/CreateFakeCurrentPlayerHook";
import {createFakePlayerHandsHook} from "@/src/client/Gaming/specs/fakes/CreateFakePlayerHandsHook";
import {createFakeTextureCacheHook} from "@/src/client/Gaming/specs/fakes/CreateFakeTextureCacheHook";

describe('When rendering OpponentHand', () => {
  
  it('should render backs for player2 when current is player1', async () => {
    // Arrange
    const useCurrentPlayerHook = createFakeCurrentPlayerHook('player1', false);
    const usePlayerHandsHook = createFakePlayerHandsHook(0, 3, false);
    const useTextureCacheHook = createFakeTextureCacheHook({__url: '/game-assets/cards/back.png'});
    
    await import('@/src/client/Gaming/infrastructure/components/OpponentHand/OpponentHand');
    
    // Act
    const props = {
      matchId: 'm1',
      useCurrentPlayerHook,
      usePlayerHandsHook,
      useTextureCacheHook
    };
    
    // Assert
    expect(props.useCurrentPlayerHook().currentPlayerPosition).toBe('player1');
    expect(props.usePlayerHandsHook().player2Count).toBe(3);
  });

  it('should render backs for player1 when current is player2', async () => {
    // Arrange
    const useCurrentPlayerHook = createFakeCurrentPlayerHook('player2', false);
    const usePlayerHandsHook = createFakePlayerHandsHook(4, 0, false);
    const useTextureCacheHook = createFakeTextureCacheHook({__url: '/game-assets/cards/back.png'});

    await import('@/src/client/Gaming/infrastructure/components/OpponentHand/OpponentHand');
    
    // Act
    const props = {
      matchId: 'm1',
      useCurrentPlayerHook,
      usePlayerHandsHook,
      useTextureCacheHook
    };
    
    // Assert
    expect(props.useCurrentPlayerHook().currentPlayerPosition).toBe('player2');
    expect(props.usePlayerHandsHook().player1Count).toBe(4);
  });
});
