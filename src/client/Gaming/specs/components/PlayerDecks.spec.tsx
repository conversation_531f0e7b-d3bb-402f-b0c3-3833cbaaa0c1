import {createFakeCurrentPlayerHook} from "@/src/client/Gaming/specs/fakes/CreateFakeCurrentPlayerHook";
import {createFakeTextureCacheHook} from "@/src/client/Gaming/specs/fakes/CreateFakeTextureCacheHook";
import {createFakePlayerDeckSizesHook} from "@/src/client/Gaming/specs/fakes/CreateFakePlayerDeckSizesHook";

describe('When rendering PlayerDecks', () => {

  it('should show current and opponent deck sizes for player1 context', async () => {
    // Arrange
    const useCurrentPlayerHook = createFakeCurrentPlayerHook('player1', false);
    const usePlayerDeckSizesHook = createFakePlayerDeckSizesHook('player1', 30, 40, false);
    const useTextureCacheHook = createFakeTextureCacheHook({__url: '/game-assets/cards/back.png'});

    await import('@/src/client/Gaming/infrastructure/components/PlayerDecks/PlayerDecks');

    // Act
    const props = {
      matchId: 'm1',
      useCurrentPlayerHook,
      usePlayerDeckSizesHook,
      useTextureCacheHook,
    };

    // Assert
    expect(props.useCurrentPlayerHook().currentPlayerPosition).toBe('player1');
    expect(props.usePlayerDeckSizesHook().myDeckSize).toBe(30);
    expect(props.usePlayerDeckSizesHook().opponentDeckSize).toBe(40);
  });

  it('should show current and opponent deck sizes for player2 context', async () => {
    // Arrange
    const useCurrentPlayerHook = createFakeCurrentPlayerHook('player2', false);
    const usePlayerDeckSizesHook = createFakePlayerDeckSizesHook('player2', 25, 33, false);
    const useTextureCacheHook = createFakeTextureCacheHook({__url: '/game-assets/cards/back.png'});

    await import('@/src/client/Gaming/infrastructure/components/PlayerDecks/PlayerDecks');

    // Act
    const props = {
      matchId: 'm1',
      useCurrentPlayerHook,
      usePlayerDeckSizesHook,
      useTextureCacheHook,
    };

    // Assert
    expect(props.useCurrentPlayerHook().currentPlayerPosition).toBe('player2');
    expect(props.usePlayerDeckSizesHook().myDeckSize).toBe(25);
    expect(props.usePlayerDeckSizesHook().opponentDeckSize).toBe(33);
  });
});
vi.mock('@react-three/drei', () => ({
  Text: () => null,
}));
