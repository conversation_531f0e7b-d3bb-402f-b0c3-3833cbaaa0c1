import { renderHook, waitFor } from '@testing-library/react';
import { useWaitingForOpponentPage } from '@/src/client/Gaming/infrastructure/pages/WaitingForOpponentPage/useWaitingForOpponentPage';

const pushMock = vi.fn();
const mockUseQuery = vi.fn();

vi.mock('next/navigation', () => ({
  useRouter: () => ({ push: pushMock }),
}));

vi.mock('convex/react', () => ({
  useQuery: () => mockUseQuery(),
}));

const createFakeQueueStatusHook = (queueStatus?: {
  isInQueue: boolean;
  queueItem: {
    id: string;
    gameId: string;
    deckId: string;
    queuedAt: number;
  } | null;
  match: string | null;
}) => () => ({ queueStatus, isLoading: false, error: undefined });

const createFakeCancelMatchRegistrationHook = (cancelMatchRegistrationFn = vi.fn()) => 
  () => ({ cancelMatchRegistration: cancelMatchRegistrationFn });

const createFakeRouterHook = (pushFn = pushMock) => () => ({ push: pushFn });

beforeEach(() => {
  pushMock.mockClear();
  mockUseQuery.mockReset();
});

describe("When two players join match making queue", () => {
  it("should redirect both players to game when match is created", async () => {
    // Arrange
    const gameId = "test-game-id";
    const matchId = "test-match-id";
    
    const queueStatusHook = createFakeQueueStatusHook({
      isInQueue: true,
      queueItem: {
        id: "queue-1", 
        gameId,
        deckId: "deck-1",
        queuedAt: Date.now()
      },
      match: matchId
    });
    const cancelMatchRegistrationHook = createFakeCancelMatchRegistrationHook();
    const routerHook = createFakeRouterHook();

    // Act
    renderHook(() => 
      useWaitingForOpponentPage(
        { gameId, locale: "en" },
        queueStatusHook,
        cancelMatchRegistrationHook,
        routerHook
      )
    );

    // Assert
    await waitFor(() => {
      expect(pushMock).toHaveBeenCalledWith(`/en/matches/${matchId}`);
    }, { timeout: 3000 });
  });

  it("should keep first player in waiting room until match is created", async () => {
    // Arrange  
    const gameId = "test-game-id";
    
    const queueStatusHook = createFakeQueueStatusHook({
      isInQueue: true,
      queueItem: {
        id: "queue-1",
        gameId,
        deckId: "deck-1", 
        queuedAt: Date.now()
      },
      match: null
    });
    const cancelMatchRegistrationHook = createFakeCancelMatchRegistrationHook();
    const routerHook = createFakeRouterHook();

    // Act
    const { result } = renderHook(() => 
      useWaitingForOpponentPage(
        { gameId, locale: "en" },
        queueStatusHook,
        cancelMatchRegistrationHook,
        routerHook
      )
    );

    // Assert
    await waitFor(() => {
      expect(result.current).toBeTruthy();
    });
    
    expect(pushMock).not.toHaveBeenCalled();
  });

  it("should redirect second player to game immediately when match is created", async () => {
    // Arrange
    const gameId = "test-game-id";  
    const matchId = "test-match-id";
    
    const queueStatusHook = createFakeQueueStatusHook({
      isInQueue: true,
      queueItem: {
        id: "queue-2",
        gameId,
        deckId: "deck-2",
        queuedAt: Date.now()
      },
      match: matchId
    });
    const cancelMatchRegistrationHook = createFakeCancelMatchRegistrationHook();
    const routerHook = createFakeRouterHook();

    // Act
    renderHook(() => useWaitingForOpponentPage(
      { gameId, locale: "en" },
      queueStatusHook,
      cancelMatchRegistrationHook,
      routerHook
    ));

    // Assert
    await waitFor(() => {
      expect(pushMock).toHaveBeenCalledWith(`/en/matches/${matchId}`);
    });
  });

  it("should find match with setup status immediately after creation", async () => {
    // Arrange
    const gameId = "test-game-id";
    const matchId = "test-match-id";
    
    const queueStatusHook = createFakeQueueStatusHook({
      isInQueue: true,
      queueItem: {
        id: "queue-1",
        gameId,
        deckId: "deck-1",
        queuedAt: Date.now()
      },
      match: matchId
    });
    const cancelMatchRegistrationHook = createFakeCancelMatchRegistrationHook();
    const routerHook = createFakeRouterHook();

    // Act
    renderHook(() => useWaitingForOpponentPage(
      { gameId, locale: "en" },
      queueStatusHook,
      cancelMatchRegistrationHook,
      routerHook
    ));

    // Assert
    await waitFor(() => {
      expect(pushMock).toHaveBeenCalledWith(`/en/matches/${matchId}`);
    });
  });

  it("should find match with waiting_for_mulligan status during mulligan phase", async () => {
    // Arrange
    const gameId = "test-game-id";
    const matchId = "test-match-id";
    
    const queueStatusHook = createFakeQueueStatusHook({
      isInQueue: true,
      queueItem: {
        id: "queue-1",
        gameId,
        deckId: "deck-1", 
        queuedAt: Date.now()
      },
      match: matchId
    });
    const cancelMatchRegistrationHook = createFakeCancelMatchRegistrationHook();
    const routerHook = createFakeRouterHook();

    // Act
    renderHook(() => useWaitingForOpponentPage(
      { gameId, locale: "en" },
      queueStatusHook,
      cancelMatchRegistrationHook,
      routerHook
    ));

    // Assert
    await waitFor(() => {
      expect(pushMock).toHaveBeenCalledWith(`/en/matches/${matchId}`);
    });
  });

  it("should not return finished matches when player tries to rejoin matchmaking", async () => {
    // Arrange
    const _gameId = "test-game-id";
    
    const queueStatusHook = createFakeQueueStatusHook({
      isInQueue: false,
      queueItem: null,
      match: null
    });

    // Act
    const { result } = renderHook(() => queueStatusHook());

    // Assert
    await waitFor(() => {
      expect(result.current.queueStatus?.match).toBeNull();
    });
    expect(result.current.queueStatus?.isInQueue).toBe(false);
  });
});