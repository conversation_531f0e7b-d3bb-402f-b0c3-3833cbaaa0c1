export const createFakeIsCurrentPlayerTurnHook = () => {
  let currentState = {
    isCurrentPlayerTurn: false,
    isLoading: false,
    noTurnSet: false,
  };

  const useIsCurrentPlayerTurn = () => currentState;

  const setState = (newState: Partial<typeof currentState>) => {
    currentState = { ...currentState, ...newState };
  };

  const reset = () => {
    currentState = {
      isCurrentPlayerTurn: false,
      isLoading: false,
      noTurnSet: false,
    };
  };

  return {
    useIsCurrentPlayerTurn,
    setState,
    reset,
  };
};