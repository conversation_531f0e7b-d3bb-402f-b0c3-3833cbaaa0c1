import {renderHook} from '@testing-library/react';
import {usePlayerDeckSizes} from '@/src/client/Gaming/infrastructure/hooks/usePlayerDeckSizes/usePlayerDeckSizes';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using usePlayerDeckSizes', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return idle empty state when matchId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => usePlayerDeckSizes(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.myDeckSize).toBe(0);
    expect(result.current.opponentDeckSize).toBe(0);
    expect(result.current.currentPlayerPosition).toBeUndefined();
  });

  it('should retain idle state while pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => usePlayerDeckSizes('m1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.myDeckSize).toBe(0);
    expect(result.current.opponentDeckSize).toBe(0);
    expect(result.current.currentPlayerPosition).toBeUndefined();
  });

  it('should map deck sizes from query data', () => {
    // Arrange
    mockUseQuery.mockReturnValue({ error: null, data: { myCardIds: new Array(30).fill('x'), opponentCardCount: 42, currentPlayerPosition: 'player2' } });

    // Act
    const {result} = renderHook(() => usePlayerDeckSizes('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.myDeckSize).toBe(30);
    expect(result.current.opponentDeckSize).toBe(42);
    expect(result.current.currentPlayerPosition).toBe('player2');
  });

  it('should handle error by returning empty sizes', () => {
    // Arrange
    mockUseQuery.mockReturnValue({ error: 'oops', data: null });

    // Act
    const {result} = renderHook(() => usePlayerDeckSizes('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.myDeckSize).toBe(0);
    expect(result.current.opponentDeckSize).toBe(0);
    expect(result.current.error).toBe('oops');
  });
});
