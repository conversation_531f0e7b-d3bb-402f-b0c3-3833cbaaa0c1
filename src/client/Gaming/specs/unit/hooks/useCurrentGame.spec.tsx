import {renderHook} from '@testing-library/react';
import {useCurrentGame} from '@/src/client/Gaming/infrastructure/hooks/useCurrentGame/useCurrentGame';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using useCurrentGame', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return idle state when gameId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useCurrentGame(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentGame).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return loading when query is pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useCurrentGame('g1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.currentGame).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return game data when available', () => {
    // Arrange
    mockUseQuery.mockReturnValue({ error: null, data: { id: 'g1', name: 'Game' } });

    // Act
    const {result} = renderHook(() => useCurrentGame('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentGame).toEqual({ id: 'g1', name: 'Game' });
    expect(result.current.error).toBeUndefined();
  });

  it('should return error when query fails', () => {
    // Arrange
    mockUseQuery.mockReturnValue({ error: 'not found', data: null });

    // Act
    const {result} = renderHook(() => useCurrentGame('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentGame).toBeUndefined();
    expect(result.current.error).toBe('not found');
  });
});

