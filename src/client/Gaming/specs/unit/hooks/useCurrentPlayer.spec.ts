import {renderHook, act} from '@testing-library/react'

// Mock convex/react to control useQuery return values
vi.mock('convex/react', async (orig) => {
  const actual = await (orig as unknown as () => object)()
  let value: unknown = undefined
  const mockUseQuery = (_q: unknown, _args: unknown) => value
  ;(mockUseQuery as unknown as { _set: (v: unknown) => void })._set = (v: unknown) => { value = v }
  return {
    ...(actual as Record<string, unknown>),
    useQuery: mockUseQuery,
  }
})

// eslint-disable-next-line import/first
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer'
// eslint-disable-next-line import/first
import {useQuery} from 'convex/react'

describe('useCurrentPlayer', () => {
  describe('When refreshing match meta', () => {
    it('should load initial current player correctly', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        error: null,
        data: { currentPlayerPosition: 'player1' },
      })

      // Act
      const {result} = renderHook(({matchId}) => useCurrentPlayer(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(result.current.currentPlayerPosition).toBe('player1')
    })

    it('should retain current player during undefined refresh', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        error: null,
        data: { currentPlayerPosition: 'player1' },
      })

      const {result, rerender} = renderHook(({matchId}) => useCurrentPlayer(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      // Act
      act(() => {
        uq._set(undefined)
        rerender({matchId: 'm1'})
      })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(result.current.currentPlayerPosition).toBe('player1')
      expect(result.current.getCurrentPlayerRole()).toBe('currentPlayer')
      expect(result.current.isCurrentPlayer('player1')).toBe(true)
    })

    it('should remain stable when data reconfirms current player', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        error: null,
        data: { currentPlayerPosition: 'player1' },
      })

      const {result, rerender} = renderHook(({matchId}) => useCurrentPlayer(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      uq._set(undefined)
      rerender({matchId: 'm1'})

      // Act
      act(() => {
        uq._set({ error: null, data: { currentPlayerPosition: 'player1' } })
        rerender({matchId: 'm1'})
      })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(result.current.currentPlayerPosition).toBe('player1')
    })
  })
})
