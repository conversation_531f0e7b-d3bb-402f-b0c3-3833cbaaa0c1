import {renderHook, act} from '@testing-library/react';
import {useTurnTransition} from '@/src/client/Gaming/infrastructure/hooks/useTurnTransition/useTurnTransition';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args)
}));

describe('When using useTurnTransition hook', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should not show splash screen on initial render', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user1',
        players: ['user1', 'user2']
      }
    });

    // Act
    const {result} = renderHook(() => useTurnTransition('match-id'));

    // Assert
    expect(result.current.showTurnSplash).toBe(false);
  });

  it('should not show splash screen when loading', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useTurnTransition('match-id'));

    // Assert
    expect(result.current.showTurnSplash).toBe(false);
  });

  it('should show splash screen when transitioning from not player turn to player turn', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user2',
        players: ['user1', 'user2']
      }
    });
    const {result, rerender} = renderHook(() => useTurnTransition('match-id'));

    // Act
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user1',
        players: ['user1', 'user2']
      }
    });
    rerender();

    // Assert
    expect(result.current.showTurnSplash).toBe(true);
  });

  it('should not show splash screen when staying as current player', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user1',
        players: ['user1', 'user2']
      }
    });
    const {result, rerender} = renderHook(() => useTurnTransition('match-id'));
    
    // Act
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user1',
        players: ['user1', 'user2']
      }
    });
    rerender();

    // Assert
    expect(result.current.showTurnSplash).toBe(false);
  });

  it('should dismiss turn splash when dismissTurnSplash is called', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user2',
        players: ['user1', 'user2']
      }
    });
    const {result, rerender} = renderHook(() => useTurnTransition('match-id'));
    
    mockUseQuery.mockReturnValue({
      error: null,
      data: {
        currentPlayerPosition: 'player1',
        currentTurn: 'user1',
        players: ['user1', 'user2']
      }
    });
    rerender();
    
    expect(result.current.showTurnSplash).toBe(true);

    // Act
    act(() => {
      result.current.dismissTurnSplash();
    });

    // Assert
    expect(result.current.showTurnSplash).toBe(false);
  });
});