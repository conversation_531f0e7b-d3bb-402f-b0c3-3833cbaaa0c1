import {renderHook} from '@testing-library/react';
import {useAvailableDecks} from '@/src/client/Gaming/infrastructure/hooks/useAvailableDecks/useAvailableDecks';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using useAvailableDecks', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return loading state while pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useAvailableDecks('g1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.availableDecks).toEqual([]);
    expect(result.current.error).toBeUndefined();
  });

  it('should return idle state when gameId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useAvailableDecks(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.availableDecks).toEqual([]);
    expect(result.current.error).toBeUndefined();
  });

  it('should return data when query resolves successfully', () => {
    // Arrange
    const mockDecks = [
      {id: 'd1', name: 'Deck One', cardCount: 30, gameId: 'g1', images: ['img1.jpg']},
      {id: 'd2', name: 'Deck Two', cardCount: 25, gameId: 'g1', images: ['img2.jpg']},
    ];
    
    mockUseQuery.mockReturnValue({
      data: { decks: mockDecks },
      error: null,
    });

    // Act
    const {result} = renderHook(() => useAvailableDecks('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.availableDecks).toEqual(mockDecks);
    expect(result.current.error).toBeUndefined();
  });

  it('should handle query with error', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      data: null,
      error: 'Game not found',
    });

    // Act
    const {result} = renderHook(() => useAvailableDecks('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.availableDecks).toEqual([]);
    expect(result.current.error).toBe('Game not found');
  });

  it('should handle query with no data', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      data: null,
      error: null,
    });

    // Act
    const {result} = renderHook(() => useAvailableDecks('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.availableDecks).toEqual([]);
    expect(result.current.error).toBeUndefined();
  });

  it('should use custom locale when provided', () => {
    // Arrange
    const mockDecks = [{id: 'd1', name: 'Deck Un', cardCount: 30, gameId: 'g1', images: ['img1.jpg']}];
    mockUseQuery.mockReturnValue({data: { decks: mockDecks }, error: null});

    // Act
    const {result} = renderHook(() => useAvailableDecks('g1', 'fr'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.availableDecks).toEqual(mockDecks);
    expect(result.current.error).toBeUndefined();
  });
});