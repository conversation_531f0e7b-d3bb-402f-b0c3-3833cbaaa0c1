import {renderHook} from '@testing-library/react';
import {useQueueStatus} from '@/src/client/Gaming/infrastructure/hooks/useQueueStatus/useQueueStatus';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using useQueueStatus', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return loading state while pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useQueueStatus('g1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.queueStatus).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return idle state when gameId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useQueueStatus(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.queueStatus).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return data when player is not in queue', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      isInQueue: false,
      queueItem: null,
    });

    // Act
    const {result} = renderHook(() => useQueueStatus('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.queueStatus).toEqual({
      isInQueue: false,
      queueItem: null,
    });
    expect(result.current.error).toBeUndefined();
  });

  it('should return data when player is in queue', () => {
    // Arrange
    const mockQueueItem = {
      id: 'q1',
      gameId: 'g1',
      deckId: 'd1',
      queuedAt: 1234567890,
    };
    
    mockUseQuery.mockReturnValue({
      isInQueue: true,
      queueItem: mockQueueItem,
    });

    // Act
    const {result} = renderHook(() => useQueueStatus('g1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.queueStatus).toEqual({
      isInQueue: true,
      queueItem: mockQueueItem,
    });
    expect(result.current.error).toBeUndefined();
  });
});