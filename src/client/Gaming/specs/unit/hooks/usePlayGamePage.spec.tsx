import {renderHook, waitFor, act} from '@testing-library/react';
import {usePlayGamePage} from '@/src/client/Gaming/infrastructure/pages/PlayGamePage/usePlayGamePage';

type Deck = {
  id: string;
  name: string;
  images: string[];
};

const createFakeAvailableDecksHook = (availableDecks: Deck[], isLoading = false, error?: string) => 
  () => ({ availableDecks, isLoading, error });

const createFakeJoinQueueHook = (joinQueueFn = vi.fn()) => 
  () => ({ joinQueue: joinQueueFn });

const createFakeRouterHook = (pushFn = vi.fn()) => 
  () => ({ push: pushFn });

describe('usePlayGamePage', () => {
  describe('When decks are loaded', () => {
    it('should expose decks and auto-select first deck', async () => {
      // Arrange
      const mockDecks = [
        {id: 'd1', name: 'Deck 1', images: ['/game-assets/cards/en/thumbnail/1.jpg']},
        {id: 'd2', name: 'Deck 2', images: ['/game-assets/cards/en/thumbnail/2.jpg']},
      ];
      
      const availableDecksHook = createFakeAvailableDecksHook(mockDecks);
      const joinQueueHook = createFakeJoinQueueHook();
      const routerHook = createFakeRouterHook();

      // Act
      const {result} = renderHook(() => usePlayGamePage({gameId: 'g1', locale: 'en'}, availableDecksHook, joinQueueHook, routerHook));

      // Assert
      await waitFor(() => {
        expect(result.current.loadingDecks).toBe(false);
        expect(result.current.decks).toEqual(mockDecks);
        expect(result.current.deckId).toBe('d1');
        expect(result.current.selectedDeckImages).toEqual(['/game-assets/cards/en/thumbnail/1.jpg']);
        expect(result.current.selectedDeckName).toBe('Deck 1');
      });
    });

    it('should allow manual deck selection', async () => {
      // Arrange
      const mockDecks = [
        {id: 'd1', name: 'Deck 1', images: ['/game-assets/cards/en/thumbnail/1.jpg']},
        {id: 'd2', name: 'Deck 2', images: ['/game-assets/cards/en/thumbnail/2.jpg']},
      ];
      
      const availableDecksHook = createFakeAvailableDecksHook(mockDecks);
      const joinQueueHook = createFakeJoinQueueHook();
      const routerHook = createFakeRouterHook();

      const {result} = renderHook(() => usePlayGamePage({gameId: 'g1', locale: 'en'}, availableDecksHook, joinQueueHook, routerHook));

      // Act
      act(() => {
        result.current.setDeckId('d2');
      });

      // Assert
      await waitFor(() => {
        expect(result.current.deckId).toBe('d2');
        expect(result.current.selectedDeckImages).toEqual(['/game-assets/cards/en/thumbnail/2.jpg']);
        expect(result.current.selectedDeckName).toBe('Deck 2');
      });
    });

    it('should auto-select first deck when decks are loaded', async () => {
      // Arrange
      const mockDecks = [
        {id: 'd1', name: 'First Deck', images: ['/game-assets/cards/en/thumbnail/1.jpg']},
        {id: 'd2', name: 'Second Deck', images: ['/game-assets/cards/en/thumbnail/2.jpg']},
      ];
      
      const availableDecksHook = createFakeAvailableDecksHook(mockDecks);
      const joinQueueHook = createFakeJoinQueueHook();
      const routerHook = createFakeRouterHook();

      // Act
      const {result} = renderHook(() => usePlayGamePage({gameId: 'g1', locale: 'en'}, availableDecksHook, joinQueueHook, routerHook));

      // Assert
      await waitFor(() => {
        expect(result.current.deckId).toBe('d1');
        expect(result.current.selectedDeckImages).toEqual(['/game-assets/cards/en/thumbnail/1.jpg']);
        expect(result.current.selectedDeckName).toBe('First Deck');
      });
    });

    it('should not auto-select when no decks are available', async () => {
      // Arrange
      const availableDecksHook = createFakeAvailableDecksHook([]);
      const joinQueueHook = createFakeJoinQueueHook();
      const routerHook = createFakeRouterHook();

      // Act
      const {result} = renderHook(() => usePlayGamePage({gameId: 'g1', locale: 'en'}, availableDecksHook, joinQueueHook, routerHook));

      // Assert
      await waitFor(() => {
        expect(result.current.loadingDecks).toBe(false);
        expect(result.current.decks).toEqual([]);
        expect(result.current.deckId).toBeNull();
        expect(result.current.selectedDeckImages).toEqual([]);
        expect(result.current.selectedDeckName).toBeUndefined();
      });
    });
  });

  describe('When starting a match', () => {
    it('should enqueue the player and navigate to waiting page', async () => {
      // Arrange
      const mockDecks = [{id: 'd1', name: 'Deck', images: []}];
      const mockJoinQueue = vi.fn().mockResolvedValue({
        success: true,
        redirectUrl: '/en/games/g1/waiting-for-opponent'
      });
      const mockPush = vi.fn();
      
      const availableDecksHook = createFakeAvailableDecksHook(mockDecks);
      const joinQueueHook = createFakeJoinQueueHook(mockJoinQueue);
      const routerHook = createFakeRouterHook(mockPush);

      const {result} = renderHook(() => usePlayGamePage({gameId: 'g1', locale: 'en'}, availableDecksHook, joinQueueHook, routerHook));

      // Act
      act(() => {
        result.current.setDeckId('d1');
      });
      
      await act(async () => {
        await result.current.handleStart();
      });

      // Assert
      expect(mockJoinQueue).toHaveBeenCalledWith({
        gameId: 'g1',
        deckId: 'd1'
      });
      expect(mockPush).toHaveBeenCalledWith('/en/games/g1/waiting-for-opponent');
    });
  });
});
