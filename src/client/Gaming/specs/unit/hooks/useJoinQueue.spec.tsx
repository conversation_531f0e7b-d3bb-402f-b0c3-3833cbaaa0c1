import {renderHook} from '@testing-library/react';
import {useJoinQueue} from '@/src/client/Gaming/infrastructure/hooks/useJoinQueue/useJoinQueue';

const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useMutation: (...args: unknown[]) => mockUseMutation(...args),
}));

describe('When using useJoinQueue', () => {
  beforeEach(() => {
    mockUseMutation.mockReset();
  });

  it('should return joinQueue function', () => {
    // Arrange
    const mockMutationFn = vi.fn();
    mockUseMutation.mockReturnValue(mockMutationFn);

    // Act
    const {result} = renderHook(() => useJoinQueue());

    // Assert
    expect(result.current.joinQueue).toBeDefined();
    expect(typeof result.current.joinQueue).toBe('function');
  });

  it('should call mutation with correct arguments when joinQueue is called', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useJoinQueue());

    const args = {
      gameId: 'g1',
      deckId: 'd1',
    };

    // Act
    await result.current.joinQueue(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      gameId: 'g1',
      deckId: 'd1',
    });
  });

  it('should handle different game and deck combinations', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useJoinQueue());

    const args = {
      gameId: 'game-xyz',
      deckId: 'deck-abc-123',
    };

    // Act
    await result.current.joinQueue(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      gameId: 'game-xyz',
      deckId: 'deck-abc-123',
    });
  });

  it('should return success result with redirect URL', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useJoinQueue());

    const args = {
      gameId: 'g1',
      deckId: 'd1',
    };

    // Act
    const mutationResult = await result.current.joinQueue(args);

    // Assert
    expect(mutationResult).toEqual({
      success: true,
      redirectUrl: '/waiting-for-opponent?gameId=g1'
    });
  });
});