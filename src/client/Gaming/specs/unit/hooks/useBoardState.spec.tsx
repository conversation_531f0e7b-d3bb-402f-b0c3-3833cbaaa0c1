import {renderHook} from '@testing-library/react';
import {useBoardState} from '@/src/client/Gaming/infrastructure/hooks/useBoardState/useBoardState';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using useBoardState', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should retain idle state while pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useBoardState('m1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.boardState).toBeUndefined();
    expect(result.current.currentTurn).toBeUndefined();
    expect(result.current.gamePhase).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return idle state when matchId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useBoardState(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.boardState).toBeUndefined();
    expect(result.current.currentTurn).toBeUndefined();
    expect(result.current.gamePhase).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return data when query resolves successfully', () => {
    // Arrange
    const mockBoardState = {
      player1Board: {
        firstRow: ['c1', null, 'c3', null, null, null, null, null],
        secondRow: Array(8).fill(null),
      },
      player2Board: {
        firstRow: Array(8).fill(null),
        secondRow: ['c4', null, null, null, null, null, null, null],
      },
    };
    
    mockUseQuery.mockReturnValue({
      boardState: mockBoardState,
      currentTurn: 'player1',
      gamePhase: 'playing',
    });

    // Act
    const {result} = renderHook(() => useBoardState('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.boardState).toEqual(mockBoardState);
    expect(result.current.currentTurn).toBe('player1');
    expect(result.current.gamePhase).toBe('playing');
    expect(result.current.error).toBeUndefined();
  });

  it('should handle match not found error', () => {
    // Arrange
    mockUseQuery.mockReturnValue(null);

    // Act
    const {result} = renderHook(() => useBoardState('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.boardState).toBeUndefined();
    expect(result.current.currentTurn).toBeUndefined();
    expect(result.current.gamePhase).toBeUndefined();
    expect(result.current.error).toBe('Match not found');
  });
});
