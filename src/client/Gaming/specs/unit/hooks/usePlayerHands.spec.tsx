import {renderHook} from '@testing-library/react';
import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using usePlayerHands', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should retain idle state while pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => usePlayerHands('m1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.player1).toEqual([]);
    expect(result.current.player2).toEqual([]);
    expect(result.current.player2Count).toBe(0);
  });

  it('should return idle empty state when matchId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => usePlayerHands(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.player1).toEqual([]);
    expect(result.current.player2).toEqual([]);
    expect(result.current.player2Count).toBe(0);
  });

  it('should return data when query resolves', () => {
    // Arrange
    mockUseQuery.mockReturnValue({ 
      player1: [
        { instanceId: 'inst1', catalogCardId: 'c1', image: 'card1.jpg' },
        { instanceId: 'inst2', catalogCardId: 'c2', image: 'card2.jpg' }
      ], 
      player2: [], 
      player2Count: 7 
    });

    // Act
    const {result} = renderHook(() => usePlayerHands('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.player1).toEqual([
      { instanceId: 'inst1', catalogCardId: 'c1', image: 'card1.jpg' },
      { instanceId: 'inst2', catalogCardId: 'c2', image: 'card2.jpg' }
    ]);
    expect(result.current.player2).toEqual([]);
    expect(result.current.player2Count).toBe(7);
  });

  it('should handle error by returning empty hands', () => {
    // Arrange
    mockUseQuery.mockReturnValue(null);

    // Act
    const {result} = renderHook(() => usePlayerHands('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.player1).toEqual([]);
    expect(result.current.player2).toEqual([]);
    expect(result.current.player2Count).toBe(0);
  });
});
