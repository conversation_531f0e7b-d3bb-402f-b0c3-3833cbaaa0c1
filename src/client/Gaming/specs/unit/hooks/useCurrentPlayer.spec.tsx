import {renderHook} from '@testing-library/react';
import {useCurrentPlayer} from '@/src/client/Gaming/infrastructure/hooks/useCurrentPlayer/useCurrentPlayer';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args)
}));

describe('useCurrentPlayer', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return loading state when query is pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useCurrentPlayer('m1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.currentPlayerPosition).toBeUndefined();
    expect(result.current.getCurrentPlayerRole()).toBeUndefined();
    expect(result.current.getOpponentRole()).toBeUndefined();
    expect(result.current.isCurrentPlayer('player1')).toBe(false);
  });

  it('should return idle state when matchId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useCurrentPlayer(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentPlayerPosition).toBeUndefined();
    expect(result.current.getCurrentPlayerRole()).toBeUndefined();
    expect(result.current.getOpponentRole()).toBeUndefined();
    expect(result.current.isCurrentPlayer('player2')).toBe(false);
  });

  it('should return roles and helpers when data is available', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      error: null,
      data: { currentPlayerPosition: 'player1' }
    });

    // Act
    const {result} = renderHook(() => useCurrentPlayer('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentPlayerPosition).toBe('player1');
    expect(result.current.getCurrentPlayerRole()).toBe('currentPlayer');
    expect(result.current.getOpponentRole()).toBe('opponent');
    expect(result.current.isCurrentPlayer('player1')).toBe(true);
    expect(result.current.isCurrentPlayer('player2')).toBe(false);
  });

  it('should handle error or missing position gracefully', () => {
    // Arrange
    mockUseQuery.mockReturnValue({ error: 'boom', data: null });

    // Act
    const {result} = renderHook(() => useCurrentPlayer('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.currentPlayerPosition).toBeUndefined();
    expect(result.current.getCurrentPlayerRole()).toBeUndefined();
    expect(result.current.getOpponentRole()).toBeUndefined();
    expect(result.current.isCurrentPlayer('player1')).toBe(false);
  });
});

