import {renderHook} from '@testing-library/react'

vi.mock('convex/react', () => ({
  useMutation: vi.fn()
}))

const mockShowError = vi.fn()

vi.mock('@/src/client/Gaming/infrastructure/providers/GameErrorProvider/useGameErrorProvider', () => ({
  useGameErrorContext: () => ({
    showError: mockShowError
  })
}))

vi.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key
}))

import {usePlaceCard} from '@/src/client/Gaming/infrastructure/hooks/usePlaceCard/usePlaceCard'
import {useMutation} from 'convex/react'

describe('usePlaceCard', () => {
  let mockMutation: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockMutation = vi.fn()
    
    ;(useMutation as ReturnType<typeof vi.fn>).mockReturnValue(mockMutation)
    
    mockShowError.mockClear()
  })

  describe('When placing card successfully', () => {
    it('should call mutation with correct arguments', async () => {
      // Arrange
      mockMutation.mockResolvedValue(undefined)
      const {result} = renderHook(() => usePlaceCard())

      // Act
      await result.current.placeCard({
        matchId: 'match-123',
        cardId: 'card-456',
        rowType: 'first',
        slotIndex: 2
      })

      // Assert
      expect(mockMutation).toHaveBeenCalledWith({
        matchId: 'match-123',
        cardId: 'card-456',
        rowType: 'first',
        slotIndex: 2
      })
    })
  })

  describe('When card duplication error occurs', () => {
    it('should handle error silently without showing user message', async () => {
      // Arrange
      mockMutation.mockRejectedValue(new Error('Card card-123 has already been played'))
      const {result} = renderHook(() => usePlaceCard())

      // Act
      const returnValue = await result.current.placeCard({
        matchId: 'match-123',
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0
      })

      // Assert
      expect(mockShowError).not.toHaveBeenCalled()
      expect(returnValue).toBeUndefined()
    })
  })

  describe('When other errors occur', () => {
    it('should show error message for legitimate errors', async () => {
      // Arrange
      mockMutation.mockRejectedValue(new Error('It is not your turn to play a card'))
      const {result} = renderHook(() => usePlaceCard())

      // Act
      const act = result.current.placeCard({
        matchId: 'match-123',
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0
      })

      // Assert
      await expect(act).rejects.toThrow('It is not your turn to play a card')
      expect(mockShowError).toHaveBeenCalledWith('notYourTurn')
    })
  })
})