import {renderHook} from '@testing-library/react';
import {useLeaveMatch} from '@/src/client/Gaming/infrastructure/hooks/useLeaveMatch/useLeaveMatch';

const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useMutation: (...args: unknown[]) => mockUseMutation(...args),
}));

describe('When using useLeaveMatch', () => {
  beforeEach(() => {
    mockUseMutation.mockReset();
  });

  it('should return leaveMatch function', () => {
    // Arrange
    const mockMutationFn = vi.fn();
    mockUseMutation.mockReturnValue(mockMutationFn);

    // Act
    const {result} = renderHook(() => useLeaveMatch());

    // Assert
    expect(result.current.leaveMatch).toBeDefined();
    expect(typeof result.current.leaveMatch).toBe('function');
  });

  it('should call mutation with correct arguments when leaveMatch is called', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useLeaveMatch());

    const args = {
      matchId: 'm1',
    };

    // Act
    await result.current.leaveMatch(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      matchId: 'm1',
    });
  });

  it('should return mutation result', async () => {
    // Arrange
    const mockResult = { success: true };
    const mockMutationFn = vi.fn().mockResolvedValue(mockResult);
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useLeaveMatch());

    const args = {
      matchId: 'm1',
    };

    // Act
    const mutationResult = await result.current.leaveMatch(args);

    // Assert
    expect(mutationResult).toEqual(mockResult);
  });

  it('should handle different match IDs', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useLeaveMatch());

    const args = {
      matchId: 'match-abc-123',
    };

    // Act
    await result.current.leaveMatch(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      matchId: 'match-abc-123',
    });
  });
});