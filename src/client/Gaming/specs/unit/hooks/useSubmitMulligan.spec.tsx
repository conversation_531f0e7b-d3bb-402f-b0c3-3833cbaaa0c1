import {renderHook} from '@testing-library/react';
import {useSubmitMulligan} from '@/src/client/Gaming/infrastructure/hooks/useSubmitMulligan/useSubmitMulligan';

const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useMutation: (...args: unknown[]) => mockUseMutation(...args),
}));

describe('When using useSubmitMulligan', () => {
  beforeEach(() => {
    mockUseMutation.mockReset();
  });

  it('should return submitMulligan function', () => {
    // Arrange
    const mockMutationFn = vi.fn();
    mockUseMutation.mockReturnValue(mockMutationFn);

    // Act
    const {result} = renderHook(() => useSubmitMulligan());

    // Assert
    expect(result.current.submitMulligan).toBeDefined();
    expect(typeof result.current.submitMulligan).toBe('function');
  });

  it('should call mutation with correct arguments when submitMulligan is called', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useSubmitMulligan());

    const args = {
      matchId: 'm1',
      selectedCardIds: ['c1', 'c2'],
      skipped: false,
      round: 1,
    };

    // Act
    await result.current.submitMulligan(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      matchId: 'm1',
      selectedCardIds: ['c1', 'c2'],
      skipped: false,
      round: 1,
    });
  });

  it('should handle skipped mulligan', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useSubmitMulligan());

    const args = {
      matchId: 'm2',
      selectedCardIds: [],
      skipped: true,
      round: 2,
    };

    // Act
    await result.current.submitMulligan(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      matchId: 'm2',
      selectedCardIds: [],
      skipped: true,
      round: 2,
    });
  });

  it('should return mutation result', async () => {
    // Arrange
    const mockResult = { success: true };
    const mockMutationFn = vi.fn().mockResolvedValue(mockResult);
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useSubmitMulligan());

    const args = {
      matchId: 'm1',
      selectedCardIds: ['c1'],
      skipped: false,
      round: 1,
    };

    // Act
    const mutationResult = await result.current.submitMulligan(args);

    // Assert
    expect(mutationResult).toEqual(mockResult);
  });
});