import {renderHook} from '@testing-library/react';
import {usePlaceCard} from '@/src/client/Gaming/infrastructure/hooks/usePlaceCard/usePlaceCard';
import {ReactNode} from 'react';
import {GameErrorProvider} from '@/src/client/Gaming/infrastructure/providers/GameErrorProvider/GameErrorProvider';

const mockUseMutation = vi.fn();
const mockUseTranslations = vi.fn();

vi.mock('convex/react', () => ({
  useMutation: (...args: unknown[]) => mockUseMutation(...args),
}));

vi.mock('next-intl', () => ({
  useTranslations: (...args: unknown[]) => mockUseTranslations(...args),
}));

const wrapper = ({children}: {children: ReactNode}) => (
  <GameErrorProvider>{children}</GameErrorProvider>
);

describe('When using usePlaceCard', () => {
  beforeEach(() => {
    mockUseMutation.mockReset();
    mockUseTranslations.mockReset();
    mockUseTranslations.mockReturnValue((key: string) => key);
  });

  it('should return placeCard function', () => {
    // Arrange
    const mockMutationFn = vi.fn();
    mockUseMutation.mockReturnValue(mockMutationFn);

    // Act
    const {result} = renderHook(() => usePlaceCard(), { wrapper });

    // Assert
    expect(result.current.placeCard).toBeDefined();
    expect(typeof result.current.placeCard).toBe('function');
  });

  it('should call mutation with correct arguments when placeCard is called', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => usePlaceCard(), { wrapper });

    const args = {
      matchId: 'm1',
      cardId: 'c1',
      rowType: 'first' as const,
      slotIndex: 2,
    };

    // Act
    await result.current.placeCard(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      matchId: 'm1',
      cardId: 'c1',
      rowType: 'first',
      slotIndex: 2,
    });
  });

  it('should handle second row placement', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => usePlaceCard(), { wrapper });

    const args = {
      matchId: 'm2',
      cardId: 'c5',
      rowType: 'second' as const,
      slotIndex: 0,
    };

    // Act
    await result.current.placeCard(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      matchId: 'm2',
      cardId: 'c5',
      rowType: 'second',
      slotIndex: 0,
    });
  });

  it('should return mutation result', async () => {
    // Arrange
    const mockResult = { success: true };
    const mockMutationFn = vi.fn().mockResolvedValue(mockResult);
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => usePlaceCard(), { wrapper });

    const args = {
      matchId: 'm1',
      cardId: 'c1',
      rowType: 'first' as const,
      slotIndex: 1,
    };

    // Act
    const mutationResult = await result.current.placeCard(args);

    // Assert
    expect(mutationResult).toEqual(mockResult);
  });
});