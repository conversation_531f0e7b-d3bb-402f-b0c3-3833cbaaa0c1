type MockThreeEvent = {
  stopPropagation: () => void;
};

type TestUseTurnBasedCursorProps = {
  matchId: string;
  allowInteraction?: boolean;
  isCurrentPlayerTurn: boolean;
};

const createTestUseTurnBasedCursor = () => {
  return ({matchId: _matchId, allowInteraction = true, isCurrentPlayerTurn}: TestUseTurnBasedCursorProps) => {
    const shouldShowPointer = isCurrentPlayerTurn && allowInteraction;

    const onPointerOver = (event: MockThreeEvent) => {
      event.stopPropagation();
      document.body.style.cursor = shouldShowPointer ? 'pointer' : 'default';
    };

    const onPointerOut = (event: MockThreeEvent) => {
      event.stopPropagation();
      document.body.style.cursor = 'default';
    };

    return {
      onPointerOver,
      onPointerOut,
      shouldShowPointer,
      isPlayerTurn: isCurrentPlayerTurn,
    };
  };
};

describe('When using turn based cursor', () => {
  const useTurnBasedCursor = createTestUseTurnBasedCursor();

  beforeEach(() => {
    document.body.style.cursor = 'default';
  });

  describe('When it is the current player turn', () => {
    it('should return isPlayerTurn as true', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, isCurrentPlayerTurn: true});

      // Assert
      expect(result.isPlayerTurn).toBe(true);
    });

    it('should return shouldShowPointer as true when interaction is allowed', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, allowInteraction: true, isCurrentPlayerTurn: true});

      // Assert
      expect(result.shouldShowPointer).toBe(true);
    });

    it('should return shouldShowPointer as false when interaction is not allowed', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, allowInteraction: false, isCurrentPlayerTurn: true});

      // Assert
      expect(result.shouldShowPointer).toBe(false);
    });

    it('should set cursor to pointer on pointer over when interaction is allowed', () => {
      // Arrange
      const matchId = 'match123';
      const result = useTurnBasedCursor({matchId, allowInteraction: true, isCurrentPlayerTurn: true});
      const mockEvent = {
        stopPropagation: vi.fn(),
      };

      // Act
      result.onPointerOver(mockEvent);

      // Assert
      expect(document.body.style.cursor).toBe('pointer');
      expect(mockEvent.stopPropagation).toHaveBeenCalledOnce();
    });

    it('should set cursor to default on pointer over when interaction is not allowed', () => {
      // Arrange
      const matchId = 'match123';
      const result = useTurnBasedCursor({matchId, allowInteraction: false, isCurrentPlayerTurn: true});
      const mockEvent = {
        stopPropagation: vi.fn(),
      };

      // Act
      result.onPointerOver(mockEvent);

      // Assert
      expect(document.body.style.cursor).toBe('default');
      expect(mockEvent.stopPropagation).toHaveBeenCalledOnce();
    });

    it('should set cursor to default on pointer out', () => {
      // Arrange
      const matchId = 'match123';
      const result = useTurnBasedCursor({matchId, isCurrentPlayerTurn: true});
      const mockEvent = {
        stopPropagation: vi.fn(),
      };
      document.body.style.cursor = 'pointer';

      // Act
      result.onPointerOut(mockEvent);

      // Assert
      expect(document.body.style.cursor).toBe('default');
      expect(mockEvent.stopPropagation).toHaveBeenCalledOnce();
    });
  });

  describe('When it is not the current player turn', () => {
    it('should return isPlayerTurn as false', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, isCurrentPlayerTurn: false});

      // Assert
      expect(result.isPlayerTurn).toBe(false);
    });

    it('should return shouldShowPointer as false regardless of allowInteraction', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const resultWithInteraction = useTurnBasedCursor({matchId, allowInteraction: true, isCurrentPlayerTurn: false});
      const resultWithoutInteraction = useTurnBasedCursor({matchId, allowInteraction: false, isCurrentPlayerTurn: false});

      // Assert
      expect(resultWithInteraction.shouldShowPointer).toBe(false);
      expect(resultWithoutInteraction.shouldShowPointer).toBe(false);
    });

    it('should set cursor to default on pointer over', () => {
      // Arrange
      const matchId = 'match123';
      const result = useTurnBasedCursor({matchId, allowInteraction: true, isCurrentPlayerTurn: false});
      const mockEvent = {
        stopPropagation: vi.fn(),
      };

      // Act
      result.onPointerOver(mockEvent);

      // Assert
      expect(document.body.style.cursor).toBe('default');
      expect(mockEvent.stopPropagation).toHaveBeenCalledOnce();
    });

    it('should set cursor to default on pointer out', () => {
      // Arrange
      const matchId = 'match123';
      const result = useTurnBasedCursor({matchId, isCurrentPlayerTurn: false});
      const mockEvent = {
        stopPropagation: vi.fn(),
      };

      // Act
      result.onPointerOut(mockEvent);

      // Assert
      expect(document.body.style.cursor).toBe('default');
      expect(mockEvent.stopPropagation).toHaveBeenCalledOnce();
    });
  });

  describe('When allowInteraction defaults to true', () => {
    it('should allow interaction by default when it is player turn', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, isCurrentPlayerTurn: true});

      // Assert
      expect(result.shouldShowPointer).toBe(true);
    });

    it('should not allow interaction by default when it is not player turn', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, isCurrentPlayerTurn: false});

      // Assert
      expect(result.shouldShowPointer).toBe(false);
    });
  });

  describe('When allowInteraction parameter changes', () => {
    it('should return shouldShowPointer as false when allowInteraction is false during player turn', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, allowInteraction: false, isCurrentPlayerTurn: true});

      // Assert
      expect(result.shouldShowPointer).toBe(false);
    });

    it('should return shouldShowPointer as true when allowInteraction is true during player turn', () => {
      // Arrange
      const matchId = 'match123';

      // Act
      const result = useTurnBasedCursor({matchId, allowInteraction: true, isCurrentPlayerTurn: true});

      // Assert
      expect(result.shouldShowPointer).toBe(true);
    });
  });
});