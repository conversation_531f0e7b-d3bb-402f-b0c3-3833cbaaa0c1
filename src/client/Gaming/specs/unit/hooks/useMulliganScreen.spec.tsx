import {renderHook, act} from '@testing-library/react';
import {useMulliganScreen} from '@/src/client/Gaming/infrastructure/hooks/useMulliganScreen/useMulliganScreen';

type MulliganData = {
  cardsInHand?: Array<{ id: string; imageUrl?: string }>;
  currentRound?: number;
  maxRounds?: number;
  hasCompletedAllRounds?: boolean;
};

type SubmitMulliganArgs = {
  matchId: string;
  selectedCardIds: string[];
  skipped: boolean;
  round: number;
};

const createFakeMulliganDataHook = (mulliganData?: MulliganData, isLoading = false, error?: string) => 
  () => ({ mulliganData, isLoading, error });

const createFakeSubmitMulliganHook = (submitMulliganFn = vi.fn()) => 
  () => ({ submitMulligan: submitMulliganFn as (params: SubmitMulliganArgs) => Promise<unknown> });

describe('When using useMulliganScreen', () => {
  it('should select only the clicked card instance when catalog ids are duplicated', () => {
    // Arrange
    const mulliganDataHook = createFakeMulliganDataHook({
      currentRound: 1,
      maxRounds: 2,
      cardsInHand: [
        { id: 'A-instance', imageUrl: '/a.jpg' },
        { id: 'C-instance', imageUrl: '/c.jpg' },
      ],
      hasCompletedAllRounds: false,
    });

    const submitMulliganHook = createFakeSubmitMulliganHook();

    const {result} = renderHook(() => useMulliganScreen({matchId: 'm1'}, mulliganDataHook, submitMulliganHook));

    // Act
    act(() => {
      result.current.onToggleCard(result.current.handCards[0].id);
    });

    // Assert
    const firstCard = result.current.handCards[0];
    const secondCard = result.current.handCards[1];
    const selectedCardIds = result.current.selectedCardIds;

    expect(selectedCardIds).toContain(firstCard.id);
    expect(selectedCardIds).not.toContain(secondCard.id);
  });

  it('should not mark submission complete after submitting 1-of-2 rounds', async () => {
    // Arrange
    const mulliganDataHook = createFakeMulliganDataHook({
      currentRound: 1,
      maxRounds: 2,
      cardsInHand: [
        { id: 'A-instance', imageUrl: '/a.jpg' },
      ],
      hasCompletedAllRounds: false,
    });

    const submitMulliganHook = createFakeSubmitMulliganHook();

    const {result} = renderHook(() => useMulliganScreen({matchId: 'm1'}, mulliganDataHook, submitMulliganHook));

    // Act
    await act(async () => {
      result.current.onToggleCard(result.current.handCards[0].id);
      await result.current.onSubmit();
    });

    // Assert
    expect(result.current.isSubmitted).toBe(false);
  });

  it('should mark submission complete when submitting last round (2-of-2)', async () => {
    // Arrange
    const mulliganDataHook = createFakeMulliganDataHook({
      currentRound: 2,
      maxRounds: 2,
      cardsInHand: [
        { id: 'A-instance', imageUrl: '/a.jpg' },
      ],
      hasCompletedAllRounds: false,
    });

    const submitMulliganHook = createFakeSubmitMulliganHook();

    const {result} = renderHook(() => useMulliganScreen({matchId: 'm1'}, mulliganDataHook, submitMulliganHook));

    // Act
    await act(async () => {
      result.current.onToggleCard(result.current.handCards[0].id);
      await result.current.onSubmit();
    });

    // Assert
    expect(result.current.isSubmitted).toBe(true);
  });

});
