import {renderHook, waitFor} from '@testing-library/react';
import {
  useWaitingForOpponentPage
} from '@/src/client/Gaming/infrastructure/pages/WaitingForOpponentPage/useWaitingForOpponentPage';

type QueueStatus = {
  isInQueue: boolean;
  queueItem: unknown;
  match: string | null;
};

const createFakeQueueStatusHook = (queueStatus?: QueueStatus, isLoading = false, error?: string) => 
  () => ({ queueStatus, isLoading, error });

const createFakeCancelMatchRegistrationHook = (cancelMatchRegistrationFn = vi.fn()) => 
  () => ({ cancelMatchRegistration: cancelMatchRegistrationFn });

const createFakeRouterHook = (pushFn = vi.fn()) => 
  () => ({ push: pushFn });

describe('useWaitingForOpponentPage', () => {
  describe('When cancelling the search', () => {
    it('should navigate back to the play page', async () => {
      // Arrange
      const mockCancelMutation = vi.fn().mockResolvedValue({
        success: true,
        redirectUrl: '/en/games/g1/play'
      });
      const mockPush = vi.fn();
      
      const queueStatusHook = createFakeQueueStatusHook({ isInQueue: true, queueItem: null, match: null });
      const cancelMatchRegistrationHook = createFakeCancelMatchRegistrationHook(mockCancelMutation);
      const routerHook = createFakeRouterHook(mockPush);
      
      const {result} = renderHook(
        () => useWaitingForOpponentPage({gameId: 'g1', locale: 'en'}, queueStatusHook, cancelMatchRegistrationHook, routerHook)
      );

      await waitFor(() => {
        expect(result.current).toBeTruthy();
      });

      // Act
      await result.current.handleCancel();

      // Assert
      expect(mockCancelMutation).toHaveBeenCalledWith({
        gameId: 'g1', 
        locale: 'en'
      });
      expect(mockPush).toHaveBeenCalledWith('/en/games/g1/play');
    });
  });
});
