import {renderHook, act} from '@testing-library/react';
import {useCardZoom} from '@/src/client/Gaming/infrastructure/hooks/useCardZoom/useCardZoom';

describe('When using useCardZoom', () => {
  it('should initialize with no zoomed card', () => {
    // Act
    const {result} = renderHook(() => useCardZoom());

    // Assert
    expect(result.current.zoomedCard).toBe(null);
    expect(result.current.isVisible).toBe(false);
  });

  it('should show zoomed card when showZoomedCard is called', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoom());

    // Act
    act(() => {
      result.current.showZoomedCard('card123');
    });

    // Assert
    expect(result.current.zoomedCard).toEqual({
      cardId: 'card123',
      isVisible: true,
    });
    expect(result.current.isVisible).toBe(true);
  });

  it('should hide zoomed card when hideZoomedCard is called', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoom());

    act(() => {
      result.current.showZoomedCard('card123');
    });

    // Act
    act(() => {
      result.current.hideZoomedCard();
    });

    // Assert
    expect(result.current.zoomedCard).toBe(null);
    expect(result.current.isVisible).toBe(false);
  });

  it('should update card id when showing different card', () => {
    // Arrange
    const {result} = renderHook(() => useCardZoom());

    act(() => {
      result.current.showZoomedCard('card123');
    });

    // Act
    act(() => {
      result.current.showZoomedCard('card456');
    });

    // Assert
    expect(result.current.zoomedCard).toEqual({
      cardId: 'card456',
      isVisible: true,
    });
    expect(result.current.isVisible).toBe(true);
  });
});