import {renderHook} from '@testing-library/react';
import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using usePlayerHands with card image data', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return cards with image data when query resolves', () => {
    // Arrange
    const expectedCardData = {
      player1: [
        { instanceId: 'inst1', catalogCardId: 'card1', image: 'card1-image-url.jpg' },
        { instanceId: 'inst2', catalogCardId: 'card2', image: 'card2-image-url.jpg' }
      ],
      player2: [],
      player2Count: 5
    };
    mockUseQuery.mockReturnValue(expectedCardData);

    // Act
    const {result} = renderHook(() => usePlayerHands('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.player1).toEqual([
      { instanceId: 'inst1', catalogCardId: 'card1', image: 'card1-image-url.jpg' },
      { instanceId: 'inst2', catalogCardId: 'card2', image: 'card2-image-url.jpg' }
    ]);
    expect(result.current.player2).toEqual([]);
    expect(result.current.player2Count).toBe(5);
  });

  it('should handle cards with image field from card data', () => {
    // Arrange  
    const expectedCardData = {
      player1: [],
      player2: [
        { instanceId: 'inst3', catalogCardId: 'opponentCard1', image: '/game-assets/cards/special-card.png' }
      ],
      player2Count: 1
    };
    mockUseQuery.mockReturnValue(expectedCardData);

    // Act
    const {result} = renderHook(() => usePlayerHands('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.player1).toEqual([]);
    expect(result.current.player2).toEqual([
      { instanceId: 'inst3', catalogCardId: 'opponentCard1', image: '/game-assets/cards/special-card.png' }
    ]);
    expect(result.current.player2Count).toBe(1);
  });
});