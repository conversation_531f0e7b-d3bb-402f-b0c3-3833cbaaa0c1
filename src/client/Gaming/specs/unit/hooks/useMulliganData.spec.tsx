import {renderHook} from '@testing-library/react';
import {useMulliganData} from '@/src/client/Gaming/infrastructure/hooks/useMulliganData/useMulliganData';

const mockUseQuery = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: (...args: unknown[]) => mockUseQuery(...args),
}));

describe('When using useMulliganData', () => {
  beforeEach(() => {
    mockUseQuery.mockReset();
  });

  it('should return loading state while pending', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useMulliganData('m1'));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.mulliganData).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return idle state when matchId is undefined', () => {
    // Arrange
    mockUseQuery.mockReturnValue(undefined);

    // Act
    const {result} = renderHook(() => useMulliganData(undefined));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mulliganData).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

  it('should return data when query resolves successfully', () => {
    // Arrange
    const mockMulliganData = {
      currentRound: 1,
      maxRounds: 3,
      cardsInHand: [
        {catalogCardId: 'c1', name: 'Card One', imageUrl: '/card1.jpg'},
        {catalogCardId: 'c2', name: 'Card Two', imageUrl: '/card2.jpg'},
      ],
      selectedCardIds: ['c1'],
      canSkip: true,
      timeLimit: 30,
    };
    
    mockUseQuery.mockReturnValue({
      data: mockMulliganData,
      error: null,
    });

    // Act
    const {result} = renderHook(() => useMulliganData('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mulliganData).toEqual(mockMulliganData);
    expect(result.current.error).toBeUndefined();
  });

  it('should handle query with error', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      data: null,
      error: 'Match not found',
    });

    // Act
    const {result} = renderHook(() => useMulliganData('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mulliganData).toBeUndefined();
    expect(result.current.error).toBe('Match not found');
  });

  it('should handle query with no data', () => {
    // Arrange
    mockUseQuery.mockReturnValue({
      data: null,
      error: null,
    });

    // Act
    const {result} = renderHook(() => useMulliganData('m1'));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.mulliganData).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });

});