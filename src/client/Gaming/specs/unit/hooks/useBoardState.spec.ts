import {renderHook, act} from '@testing-library/react'

vi.mock('convex/react', async (orig) => {
  const actual = await (orig as unknown as () => object)()
  let value: unknown = undefined
  const mockUseQuery = (_q: unknown, _args: unknown) => value
  ;(mockUseQuery as unknown as { _set: (v: unknown) => void })._set = (v: unknown) => { value = v }
  return {
    ...(actual as Record<string, unknown>),
    useQuery: mockUseQuery,
  }
})

// eslint-disable-next-line import/first
import {useBoardState} from '@/src/client/Gaming/infrastructure/hooks/useBoardState/useBoardState'
// eslint-disable-next-line import/first
import {useQuery} from 'convex/react'

describe('useBoardState', () => {
  describe('When refreshing board state', () => {
    it('should load initial board state correctly', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        boardState: {
          player1Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) },
          player2Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) },
        },
        currentTurn: 'player1',
        gamePhase: 'playing',
      })

      // Act
      const {result} = renderHook(({matchId}) => useBoardState(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(result.current.boardState?.player1Board.firstRow.length).toBe(8)
    })

    it('should retain previous board state during refresh', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        boardState: {
          player1Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) },
          player2Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) },
        },
        currentTurn: 'player1',
        gamePhase: 'playing',
      })

      const {result, rerender} = renderHook(({matchId}) => useBoardState(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      // Act
      act(() => {
        uq._set(undefined)
        rerender({ matchId: 'm1' })
      })

      // Assert
      expect(result.current.isLoading).toBe(true)
      expect(result.current.boardState).toBeUndefined()
      expect(result.current.currentTurn).toBeUndefined()
    })
  })
})

