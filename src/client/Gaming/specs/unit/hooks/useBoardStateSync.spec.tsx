import {renderHook} from '@testing-library/react';
import {useBoardStateSync} from '@/src/client/Gaming/infrastructure/hooks/useBoardStateSync/useBoardStateSync';

const createFakeBoardStateHook = (isLoading: boolean, boardState?: unknown) => 
  () => ({ isLoading, boardState });

const createFakePlayerHandsHook = (isLoading: boolean, player1: unknown[] = [], player2: unknown[] = []) => 
  () => ({ isLoading, player1, player2 });

describe('When board state and player hands are loaded', () => {
  it('should return not loading', () => {
    // Arrange
    const fakeBoardStateHook = createFakeBoardStateHook(false, {player1Board: {firstRow: [], secondRow: []}});
    const fakePlayerHandsHook = createFakePlayerHandsHook(false, ['card-1'], ['card-2']);

    // Act
    const {result} = renderHook(() => useBoardStateSync('match-123', fakeBoardStateHook, fakePlayerHandsHook));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(false);
  });
});

describe('When match ID is undefined', () => {
  it('should return not loading', () => {
    // Arrange
    const fakeBoardStateHook = createFakeBoardStateHook(false, undefined);
    const fakePlayerHandsHook = createFakePlayerHandsHook(false, [], []);

    // Act
    const {result} = renderHook(() => useBoardStateSync(undefined, fakeBoardStateHook, fakePlayerHandsHook));

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(false);
  });
});

describe('When board state data is loading', () => {
  it('should return loading state', () => {
    // Arrange
    const fakeBoardStateHook = createFakeBoardStateHook(true, undefined);
    const fakePlayerHandsHook = createFakePlayerHandsHook(false, [], []);

    // Act
    const {result} = renderHook(() => useBoardStateSync('match-123', fakeBoardStateHook, fakePlayerHandsHook));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(false);
  });
});

describe('When player hands are loading', () => {
  it('should return loading state', () => {
    // Arrange
    const fakeBoardStateHook = createFakeBoardStateHook(false, {player1Board: {firstRow: [], secondRow: []}});
    const fakePlayerHandsHook = createFakePlayerHandsHook(true, [], []);

    // Act
    const {result} = renderHook(() => useBoardStateSync('match-123', fakeBoardStateHook, fakePlayerHandsHook));

    // Assert
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(false);
  });
});
