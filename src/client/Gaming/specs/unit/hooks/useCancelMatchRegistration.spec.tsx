import {renderHook} from '@testing-library/react';
import {useCancelMatchRegistration} from '@/src/client/Gaming/infrastructure/hooks/useCancelMatchRegistration/useCancelMatchRegistration';

const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useMutation: (...args: unknown[]) => mockUseMutation(...args),
}));

describe('When using useCancelMatchRegistration', () => {
  beforeEach(() => {
    mockUseMutation.mockReset();
  });

  it('should return cancelMatchRegistration function', () => {
    // Arrange
    const mockMutationFn = vi.fn();
    mockUseMutation.mockReturnValue(mockMutationFn);

    // Act
    const {result} = renderHook(() => useCancelMatchRegistration());

    // Assert
    expect(result.current.cancelMatchRegistration).toBeDefined();
    expect(typeof result.current.cancelMatchRegistration).toBe('function');
  });

  it('should call mutation with correct arguments when cancelMatchRegistration is called', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useCancelMatchRegistration());

    const args = {
      gameId: 'g1',
      locale: 'en',
    };

    // Act
    await result.current.cancelMatchRegistration(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      gameId: 'g1',
    });
  });

  it('should handle different game IDs', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useCancelMatchRegistration());

    const args = {
      gameId: 'game-xyz-789',
      locale: 'en',
    };

    // Act
    await result.current.cancelMatchRegistration(args);

    // Assert
    expect(mockMutationFn).toHaveBeenCalledWith({
      gameId: 'game-xyz-789',
    });
  });

  it('should return mutation result', async () => {
    // Arrange
    const mockMutationFn = vi.fn().mockResolvedValue({});
    mockUseMutation.mockReturnValue(mockMutationFn);
    const {result} = renderHook(() => useCancelMatchRegistration());

    const args = {
      gameId: 'g1',
      locale: 'en',
    };

    // Act
    const mutationResult = await result.current.cancelMatchRegistration(args);

    // Assert
    expect(mutationResult).toEqual({
      success: true,
      redirectUrl: '/en/games/g1/play'
    });
  });
});