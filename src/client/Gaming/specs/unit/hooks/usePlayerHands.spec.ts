import {renderHook, act} from '@testing-library/react'

vi.mock('convex/react', async (orig) => {
  const actual = await (orig as unknown as () => object)()
  let value: unknown = undefined
  const mockUseQuery = (_q: unknown, _args: unknown) => value
  ;(mockUseQuery as unknown as { _set: (v: unknown) => void })._set = (v: unknown) => { value = v }
  return {
    ...(actual as Record<string, unknown>),
    useQuery: mockUseQuery,
  }
})

// eslint-disable-next-line import/first
import {usePlayerHands} from '@/src/client/Gaming/infrastructure/hooks/usePlayerHands/usePlayerHands'
// eslint-disable-next-line import/first
import {useQuery} from 'convex/react'

describe('usePlayerHands', () => {
  describe('When refreshing player hands', () => {
    it('should load initial player hands correctly', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        player1: [{ instanceId: 'a', catalogCardId: 'c1', image: 'img1' }],
        player2: [],
        player1Count: 1,
        player2Count: 0,
      })

      // Act
      const {result} = renderHook(({matchId}) => usePlayerHands(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      // Assert
      expect(result.current.player1.length).toBe(1)
      expect(result.current.player2Count).toBe(0)
    })

    it('should retain previous hands during refresh', () => {
      // Arrange
      const uq = useQuery as unknown as { _set: (v: unknown) => void }
      uq._set({
        player1: [{ instanceId: 'a', catalogCardId: 'c1', image: 'img1' }],
        player2: [],
        player1Count: 1,
        player2Count: 0,
      })

      const {result, rerender} = renderHook(({matchId}) => usePlayerHands(matchId), {
        initialProps: { matchId: 'm1' as string | undefined },
      })

      // Act
      act(() => {
        uq._set(undefined)
        rerender({ matchId: 'm1' })
      })

      // Assert
      expect(result.current.player1.length).toBe(0)
      expect(result.current.player2Count).toBe(0)
      expect(result.current.isLoading).toBe(true)
    })
  })
})

