import {render, fireEvent, waitFor} from '@testing-library/react';
import {vi} from 'vitest';
import GameScene from '@/src/client/Gaming/infrastructure/components/GameScene/GameScene';

vi.mock('@/src/client/Gaming/infrastructure/hooks/useBoardStateSync/useBoardStateSync', () => ({
  useBoardStateSync: vi.fn(),
}));

vi.mock('@/src/client/Gaming/infrastructure/hooks/usePlaceCard/usePlaceCard', () => ({
  usePlaceCard: () => ({
    placeCard: vi.fn(),
  }),
}));

vi.mock('@/src/client/Gaming/infrastructure/components/TextureCache/TextureCache', () => ({
  TextureCacheProvider: ({children}: {children: React.ReactNode}) => <div>{children}</div>,
}));

vi.mock('@react-three/fiber', () => ({
  Canvas: ({children}: {children: React.ReactNode}) => <div data-testid="canvas">{children}</div>,
}));

describe('When preventing rapid clicks', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should throttle card placement clicks within 100ms', async () => {
    // Arrange
    const mockPlaceCard = vi.fn();
    const {usePlaceCard} = await import('@/src/client/Gaming/infrastructure/hooks/usePlaceCard/usePlaceCard');
    vi.mocked(usePlaceCard).mockReturnValue({placeCard: mockPlaceCard});

    render(<GameScene matchId="match-1" />);
    
    const slotElement = document.querySelector('[data-testid="game-slot"]');
    
    // Act
    if (slotElement) {
      fireEvent.click(slotElement);
      vi.advanceTimersByTime(50);
      fireEvent.click(slotElement);
      vi.advanceTimersByTime(50);  
      fireEvent.click(slotElement);
    }

    // Assert
    await waitFor(() => {
      expect(mockPlaceCard).toHaveBeenCalledTimes(1);
    });
  });
});