export type CancelMatchRegistrationResult = {
  error?: string;
};

export type JoinMatchMakingQueueResult = {
  error?: string;
};

export type MatchCreatedData = {
  payload: {
    matchId: string;
  };
};

export type PlayerQueueStatusResult = {
  isInQueue: boolean;
  queueItem: {
    id: string;
    gameId: string;
    deckId: string;
    queuedAt: number;
  } | null;
  match: string | null;
};

export interface MatchMakingService {
  cancelMatchRegistration(gameId: string): Promise<CancelMatchRegistrationResult>;
  joinMatchMakingQueue(gameId: string, deckId: string): Promise<JoinMatchMakingQueueResult>;
  watchForMatchCreated(gameId: string): Promise<MatchCreatedData | null>;
  getPlayerQueueStatus(gameId: string): Promise<PlayerQueueStatusResult>;
};
