export type MatchData = {
  player1Cards: string[];
  player2CardCount: number;
  currentPlayerPosition?: 'player1' | 'player2';
  roleBased?: {
    currentPlayer: { cardImageUrls: string[] };
    opponent: { handCount: number };
  };
};

export type GameData = {
  id: string;
  name: string;
  description?: string;
  publisher?: string;
  playerCount?: string;
  imageUrl?: string;
  bannerUrl?: string;
  playersOnline?: number;
  totalMatches?: number;
  screenshots?: string[];
  videos?: string[];
  reviews?: {
    id: string;
    playerName: string;
    rating: number;
    comment: string;
    createdAt: string;
  }[];
};

export type LoadGameByIdResult = {
  data?: GameData | null;
  error?: string;
};

export interface GameService {
  loadMatchData(matchId: string): Promise<MatchData>;
  loadGameById(gameId: string, locale?: string): Promise<LoadGameByIdResult>;
};
