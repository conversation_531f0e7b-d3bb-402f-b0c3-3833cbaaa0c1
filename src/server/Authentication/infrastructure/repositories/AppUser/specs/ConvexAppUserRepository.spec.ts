import {ConvexAppUserRepository} from '../ConvexAppUserRepository';
import {AppUser} from '@/src/server/Authentication/domain/AppUser/AppUser';
import schema from '@/convex/schema';
import {convexTest, TestConvexForDataModel} from 'convex-test';
import {DataModel, Id} from '@/convex/_generated/dataModel';

describe('ConvexAppUserRepository', () => {
  let testConvex: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    testConvex = convexTest(schema);
  });

  describe('When finding a user by id', () => {
    it('should return the user when it exists', async () => {
      // Arrange
      const appUserId = '00000000-0000-0000-0000-000000000123';
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId,
        convexUserId: 'auth456',
        name: '<PERSON>',
        avatar: 'https://example.com/avatar.jpg',
        email: '<EMAIL>',
        createdAt: Date.now(),
      }));

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        const user = await repository.findById(appUserId);
        return user ? user.toSnapshot() : null;
      });

      // Assert
      expect(result).not.toBeNull();
      expect(result).toEqual({
        id: '00000000-0000-0000-0000-000000000123',
        authId: 'auth456',
        name: 'John Doe',
        image: 'https://example.com/avatar.jpg',
        email: '<EMAIL>',
        status: undefined,
      });
    });

    it('should return null when user does not exist', async () => {
      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.findById('00000000-0000-0000-0000-000000000999');
      });

      // Assert
      expect(result).toBeNull();
    });

    it('should handle users with undefined email', async () => {
      // Arrange
      const appUserId = '00000000-0000-0000-0000-000000000789';
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId,
        convexUserId: 'auth789',
        name: 'Jane Smith',
        avatar: 'https://example.com/jane.jpg',
        createdAt: Date.now(),
      }));

      // Act
      const result = testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.findById(appUserId);
      });

      // Assert
      await expect(result).rejects.toThrow();
    });

    it('should properly map all user fields', async () => {
      // Arrange
      const appUserId = '00000000-0000-0000-0000-000000001111';
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId,
        convexUserId: 'authComplete',
        name: 'Complete User',
        avatar: 'https://example.com/complete.jpg',
        email: '<EMAIL>',
        createdAt: Date.now(),
      }));

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        const user = await repository.findById(appUserId);
        return user ? user.toSnapshot() : null;
      });

      // Assert
      expect(result).not.toBeNull();
      expect(result!.id).toBe('00000000-0000-0000-0000-000000001111');
      expect(result!.authId).toBe('authComplete');
      expect(result!.name).toBe('Complete User');
      expect(result!.image).toBe('https://example.com/complete.jpg');
      expect(result!.email).toBe('<EMAIL>');
    });
  });

  describe('When saving a user', () => {
    it('should update existing user status', async () => {
      // Arrange
      const appUserId = '00000000-0000-0000-0000-000000002222';
      const userId = await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId,
        convexUserId: 'auth123',
        name: 'Existing User',
        avatar: 'https://example.com/existing.jpg',
        email: '<EMAIL>',
        status: 'active',
        createdAt: Date.now(),
      })) as Id<'appUsers'>;

      const user = AppUser.fromSnapshot({
        id: appUserId,
        authId: 'auth123',
        name: 'Existing User',
        image: 'https://example.com/existing.jpg',
        email: '<EMAIL>',
      });
      user.setStatus('inactive');

      // Act
      await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.save(user);
      });

      // Assert
      const updatedUser = await testConvex.run(ctx => ctx.db.get(userId));
      expect(updatedUser!.status).toBe('inactive');
    });

    it('should not save when user does not exist in database', async () => {
      // Arrange
      const user = AppUser.fromSnapshot({
        id: '00000000-0000-0000-0000-000000003333',
        authId: 'auth999',
        name: 'Non Existent',
        image: 'https://example.com/none.jpg',
        email: '<EMAIL>',
      });
      user.setStatus('active');

      // Act
      await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.save(user);
      });

      // Assert
      const allUsers = await testConvex.run(ctx => ctx.db.query('appUsers').collect());
      expect(allUsers).toHaveLength(0);
    });

    it('should throw error when used with query context', async () => {
      // Arrange
      const user = AppUser.fromSnapshot({
        id: '00000000-0000-0000-0000-000000004444',
        authId: 'auth123',
        name: 'Test User',
        image: 'https://example.com/test.jpg',
        email: '<EMAIL>',
      });

      // Act
      const result = testConvex.run(async (ctx) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        delete (ctx as any).runMutation;
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.save(user);
      });

      // Assert
      await expect(result).rejects.toThrow('Save operation not supported in query context');
    });

    it('should handle user with undefined status', async () => {
      // Arrange
      const appUserId = '00000000-0000-0000-0000-000000005555';
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId,
        convexUserId: 'auth456',
        name: 'User Without Status',
        avatar: 'https://example.com/nostatus.jpg',
        email: '<EMAIL>',
        createdAt: Date.now(),
      }));

      const user = AppUser.fromSnapshot({
        id: appUserId,
        authId: 'auth456',
        name: 'User Without Status',
        image: 'https://example.com/nostatus.jpg',
        email: '<EMAIL>',
      });

      // Act
      await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.save(user);
      });

      // Assert
      const savedUser = await testConvex.run(ctx => 
        ctx.db.query('appUsers')
          .withIndex('by_appUserId', q => q.eq('appUserId', appUserId))
          .unique()
      );
      expect(savedUser!.status).toBeUndefined();
    });

    it('should update status to defined value from undefined', async () => {
      // Arrange
      const appUserId = '00000000-0000-0000-0000-000000006666';
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId,
        convexUserId: 'auth789',
        name: 'User To Activate',
        avatar: 'https://example.com/activate.jpg',
        email: '<EMAIL>',
        createdAt: Date.now(),
      }));

      const user = AppUser.fromSnapshot({
        id: appUserId,
        authId: 'auth789',
        name: 'User To Activate',
        image: 'https://example.com/activate.jpg',
        email: '<EMAIL>',
      });
      user.setStatus('premium');

      // Act
      await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.save(user);
      });

      // Assert
      const updatedUser = await testConvex.run(ctx => 
        ctx.db.query('appUsers')
          .withIndex('by_appUserId', q => q.eq('appUserId', appUserId))
          .unique()
      );
      expect(updatedUser!.status).toBe('premium');
    });
  });

  describe('When working with multiple users', () => {
    it('should find correct user by appUserId among multiple users', async () => {
      // Arrange
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId: '00000000-0000-0000-0000-000000007777',
        convexUserId: 'auth1',
        name: 'User One',
        avatar: 'https://example.com/1.jpg',
        email: '<EMAIL>',
        createdAt: Date.now(),
      }));
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId: '00000000-0000-0000-0000-000000008888',
        convexUserId: 'auth2',
        name: 'User Two',
        avatar: 'https://example.com/2.jpg',
        email: '<EMAIL>',
        createdAt: Date.now(),
      }));

      // Act
      const result = await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        const user = await repository.findById('00000000-0000-0000-0000-000000008888');
        return user ? user.toSnapshot() : null;
      });

      // Assert
      expect(result).not.toBeNull();
      expect(result!.id).toBe('00000000-0000-0000-0000-000000008888');
      expect(result!.name).toBe('User Two');
      expect(result!.email).toBe('<EMAIL>');
    });

    it('should update correct user when multiple users exist', async () => {
      // Arrange
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId: '00000000-0000-0000-0000-000000009999',
        convexUserId: 'auth1',
        name: 'User One',
        avatar: 'https://example.com/1.jpg',
        email: '<EMAIL>',
        status: 'active',
        createdAt: Date.now(),
      }));
      await testConvex.run(ctx => ctx.db.insert('appUsers', {
        appUserId: '00000000-0000-0000-0000-000000001010',
        convexUserId: 'auth2',
        name: 'User Two',
        avatar: 'https://example.com/2.jpg',
        email: '<EMAIL>',
        status: 'active',
        createdAt: Date.now(),
      }));

      const user = AppUser.fromSnapshot({
        id: '00000000-0000-0000-0000-000000009999',
        authId: 'auth1',
        name: 'User One',
        image: 'https://example.com/1.jpg',
        email: '<EMAIL>',
      });
      user.setStatus('suspended');

      // Act
      await testConvex.run(async (ctx) => {
        const repository = new ConvexAppUserRepository(ctx);
        return await repository.save(user);
      });

      // Assert
      const user1 = await testConvex.run(ctx => 
        ctx.db.query('appUsers')
          .withIndex('by_appUserId', q => q.eq('appUserId', '00000000-0000-0000-0000-000000009999'))
          .unique()
      );
      const user2 = await testConvex.run(ctx => 
        ctx.db.query('appUsers')
          .withIndex('by_appUserId', q => q.eq('appUserId', '00000000-0000-0000-0000-000000001010'))
          .unique()
      );
      
      expect(user1!.status).toBe('suspended');
      expect(user2!.status).toBe('active');
    });
  });
});