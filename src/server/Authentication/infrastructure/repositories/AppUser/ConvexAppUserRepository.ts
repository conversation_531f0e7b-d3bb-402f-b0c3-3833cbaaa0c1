import {GenericMutationCtx, GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {AppUserRepository} from "@/src/server/Authentication/application/ports/AppUserRepository";
import {AppUser} from "@/src/server/Authentication/domain/AppUser/AppUser";

export class ConvexAppUserRepository implements AppUserRepository {
  private readonly ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>;

  constructor(ctx: GenericMutationCtx<DataModel> | GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async findById(appUserId: string): Promise<AppUser | null> {
    const doc = await this.ctx.db
      .query('appUsers')
      .withIndex('by_appUserId', q => q.eq('appUserId', appUserId))
      .unique();
    return doc
      ? AppUser.fromSnapshot({
          id: doc.appUserId,
          authId: doc.convexUserId,
          name: doc.name,
          image: doc.avatar,
          email: doc.email!,
        })
      : null;
  }

  async save(user: AppUser) {
    if (!('runMutation' in this.ctx)) {
      throw new Error('Save operation not supported in query context');
    }
    
    const mutationCtx = this.ctx as GenericMutationCtx<DataModel>;
    const data = user.toSnapshot();
    const doc = await mutationCtx.db
      .query('appUsers')
      .withIndex('by_appUserId', q => q.eq('appUserId', data.id))
      .unique();
    if (doc) {
      await mutationCtx.db.patch(doc._id as Id<'appUsers'>, {status: data.status});
    }
  }
}
