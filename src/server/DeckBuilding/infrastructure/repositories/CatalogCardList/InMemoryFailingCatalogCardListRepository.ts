import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";

export class InMemoryFailingCatalogCardListRepository implements CatalogCardListRepository {
  async getByGameId(gameId: string): Promise<CatalogCardList> {
    throw new Error(gameId);
  }

  async getByIds(gameId: string, _catalogCardIds: string[]): Promise<CatalogCardList> {
    throw new Error(gameId);
  }
}
