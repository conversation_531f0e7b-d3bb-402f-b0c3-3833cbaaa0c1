import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";
import {CatalogCardList} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCardList";
import {CatalogCard} from "@/src/server/DeckBuilding/domain/Catalog/CatalogCard";

export class ConvexCatalogCardListRepository implements CatalogCardListRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getByGameId(gameId: string): Promise<CatalogCardList> {
    const cards = await this.ctx.db
      .query("catalogCards")
      .withIndex("by_gameId", q => q.eq("gameId", gameId as Id<"games">))
      .collect();

    const catalogCards: CatalogCard[] = cards.map(card => ({
      id: card._id,
      name: card.name,
      image: card.image,
      minDeckQuantity: card.minDeckQuantity,
      maxDeckQuantity: card.maxDeckQuantity,
      data: card.data,
    }));

    return CatalogCardList.createFrom(catalogCards);
  }

  async getByIds(gameId: string, catalogCardIds: string[]): Promise<CatalogCardList> {
    void gameId;
    const unique = Array.from(new Set(catalogCardIds));
    const docs = await Promise.all(unique.map(id => this.ctx.db.get(id as Id<'catalogCards'>)));
    const present = docs.filter((d): d is NonNullable<typeof d> => Boolean(d));
    const catalogCards: CatalogCard[] = present.map(card => ({
      id: card._id,
      name: card.name,
      image: card.image,
      minDeckQuantity: card.minDeckQuantity,
      maxDeckQuantity: card.maxDeckQuantity,
      data: card.data,
    }));
    return CatalogCardList.createFrom(catalogCards);
  }
}
