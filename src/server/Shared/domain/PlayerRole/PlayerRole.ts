export type PlayerRole = 'currentPlayer' | 'opponent';
export type LegacyPlayerId = 'player1' | 'player2';

export const convertRoleToLegacy = (role: PlayerRole, currentPlayerPosition: LegacyPlayerId): LegacyPlayerId => {
  if (role === 'currentPlayer') {
    return currentPlayerPosition;
  }
  return currentPlayerPosition === 'player1' ? 'player2' : 'player1';
};

export const convertLegacyToRole = (playerId: LegacyPlayerId, currentPlayerPosition: LegacyPlayerId): PlayerRole => {
  return playerId === currentPlayerPosition ? 'currentPlayer' : 'opponent';
};

export const getPlayerIdByRole = (role: PlayerRole, currentPlayerPosition: LegacyPlayerId): LegacyPlayerId => {
  return convertRoleToLegacy(role, currentPlayerPosition);
};