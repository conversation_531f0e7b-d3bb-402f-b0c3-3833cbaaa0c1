interface EnhancedPlacedCard {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: 'first' | 'second';
  image: string;
}

interface EnhancedPlayerBoard {
  firstRow: (EnhancedPlacedCard | null)[];
  secondRow: (EnhancedPlacedCard | null)[];
}

interface EnhancedBoardState {
  player1Board: EnhancedPlayerBoard;
  player2Board: EnhancedPlayerBoard;
}

export type LoadBoardStateViewModel = {
  error: string | null;
  boardState?: EnhancedBoardState;
  currentTurn?: string;
  gamePhase?: string;
  lastUpdated: number;
} | null;