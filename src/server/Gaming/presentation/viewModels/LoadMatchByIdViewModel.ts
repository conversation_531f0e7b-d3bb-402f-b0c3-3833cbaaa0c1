import {Id} from "@/convex/_generated/dataModel";

export type LoadMatchByIdViewModel = {
  error: string | null;
  data: {
    id: string;
    status: string;
    pageType: 'finished' | 'mulligan' | 'game';
    players: string[];
    isWinner: boolean;
    gameId: Id<'games'>;
    currentPlayerPosition: 'player1' | 'player2';
    roleBased?: {
      currentPlayer: { userId: string };
      opponent: { userId: string };
    };
  } | null;
};
