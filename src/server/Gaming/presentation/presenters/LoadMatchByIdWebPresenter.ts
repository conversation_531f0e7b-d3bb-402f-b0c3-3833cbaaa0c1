import {Match} from "@/src/server/Gaming/domain/Match/Match";
import {LoadMatchByIdPresenter} from "@/src/server/Gaming/application/ports/LoadMatchByIdPresenter";
import {LoadMatchByIdViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadMatchByIdViewModel";
import {Id} from "@/convex/_generated/dataModel";

export class LoadMatchByIdWebPresenter implements LoadMatchByIdPresenter {
  private viewModel: LoadMatchByIdViewModel = {error: null, data: null};

  display(match: Match, userId: string, hasCompletedAllMulliganRounds: boolean): void {
    const players = match.getPlayers();
    const currentPlayerPosition = players[0] === userId ? 'player1' : 'player2';
    const roleBased = {
      currentPlayer: { userId: currentPlayerPosition === 'player1' ? players[0] : players[1] },
      opponent: { userId: currentPlayerPosition === 'player1' ? players[1] : players[0] },
    };
    
    const status = match.getStatus();
    let pageType: 'finished' | 'mulligan' | 'game' = 'game';
    
    if (status === 'finished') {
      pageType = 'finished';
    } else if (status === 'waiting_for_mulligan' && !hasCompletedAllMulliganRounds) {
      pageType = 'mulligan';
    } else {
      pageType = 'game';
    }
    
    this.viewModel = {
      error: null,
      data: {
        id: match.getId(),
        status: match.getStatus(),
        pageType,
        players: players,
        isWinner: match.getWinner() === userId,
        gameId: match.getGameId() as Id<'games'>,
        currentPlayerPosition,
        roleBased,
      },
    };
  }

  displayError(error: Error): void {
    this.viewModel = {error: error.message, data: null};
  }

  getViewModel(): LoadMatchByIdViewModel {
    return this.viewModel;
  }
}
