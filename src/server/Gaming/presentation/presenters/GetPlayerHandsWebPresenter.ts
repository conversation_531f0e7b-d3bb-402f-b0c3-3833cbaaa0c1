import {GetPlayerHandsPresenter, GetPlayerHandsResult} from "@/src/server/Gaming/application/ports/GetPlayerHandsPresenter";
import {GetPlayerHandsViewModel} from "@/src/server/Gaming/presentation/viewModels/GetPlayerHandsViewModel";

export class GetPlayerHandsWebPresenter implements GetPlayerHandsPresenter {
  private viewModel: GetPlayerHandsViewModel = { error: null, data: null };

  display(result: GetPlayerHandsResult): void {
    this.viewModel = {
      error: null,
      data: result,
    };
  }

  displayError(error: Error): void {
    this.viewModel = { error: error.message, data: null };
  }

  getViewModel(): GetPlayerHandsViewModel {
    return this.viewModel;
  }
}