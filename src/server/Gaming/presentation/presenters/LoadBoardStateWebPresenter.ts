import {LoadBoardStatePresenter} from "@/src/server/Gaming/application/ports/LoadBoardStatePresenter";
import {LoadBoardStateViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadBoardStateViewModel";
import {EnhancedBoard} from "@/src/server/Gaming/application/ports/BoardEnhancementService";

export class LoadBoardStateWebPresenter implements LoadBoardStatePresenter {
  private viewModel: LoadBoardStateViewModel = null;

  display(board: EnhancedBoard, currentTurn?: string, gamePhase?: string): void {
    this.viewModel = {
      error: null,
      boardState: board,
      currentTurn,
      gamePhase,
      lastUpdated: Date.now(),
    };
  }

  displayError(error: Error): void {
    this.viewModel = {
      error: error.message,
      lastUpdated: Date.now(),
    };
  }

  getViewModel(): LoadBoardStateViewModel {
    return this.viewModel;
  }
}