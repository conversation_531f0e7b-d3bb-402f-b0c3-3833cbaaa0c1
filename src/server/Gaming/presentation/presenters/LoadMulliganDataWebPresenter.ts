import {LoadMulliganDataPresenter, LoadMulliganDataResult} from "@/src/server/Gaming/application/ports/LoadMulliganDataPresenter";
import {LoadMulliganDataViewModel} from "@/src/server/Gaming/presentation/viewModels/LoadMulliganDataViewModel";

export class LoadMulliganDataWebPresenter implements LoadMulliganDataPresenter {
  private viewModel: LoadMulliganDataViewModel = { error: null, data: null };

  display(result: LoadMulliganDataResult): void {
    this.viewModel = {
      error: null,
      data: result,
    };
  }

  displayError(error: Error): void {
    this.viewModel = { error: error.message, data: null };
  }

  getViewModel(): LoadMulliganDataViewModel {
    return this.viewModel;
  }
}
