import { GetMatchPlayersPresenter } from '../../application/ports/GetMatchPlayersPresenter';
import { GetMatchPlayersViewModel } from '../viewModels/GetMatchPlayersViewModel';

export class GetMatchPlayersWebPresenter implements GetMatchPlayersPresenter {
  private viewModel: GetMatchPlayersViewModel = {
    error: null,
    data: null,
  };

  display(
    currentPlayer: { id: string; name: string; avatar: string },
    opponent: { id: string; name: string; avatar: string }
  ): void {
    this.viewModel = {
      error: null,
      data: { currentPlayer, opponent },
    };
  }

  displayError(error: Error): void {
    this.viewModel = {
      error: error.message,
      data: null,
    };
  }

  getViewModel(): GetMatchPlayersViewModel {
    return this.viewModel;
  }
}