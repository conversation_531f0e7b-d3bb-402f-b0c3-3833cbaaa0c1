import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

export class InMemoryGameSettingsRepository implements GameSettingsRepository {
  private settings: Map<string, GameSettings> = new Map();

  async getByGameId(gameId: string): Promise<GameSettings | null> {
    return this.settings.get(gameId) || null;
  }

  addSettingsForGame(gameId: string, snapshot: {maxCardsInDeck: number; mulliganCount?: number; startingHandSize?: number}): void {
    this.settings.set(gameId, GameSettings.fromSnapshot({
      gameId,
      maxCardsInDeck: snapshot.maxCardsInDeck,
      mulliganCount: snapshot.mulliganCount ?? 1,
      startingHandSize: snapshot.startingHandSize ?? 7
    }));
  }

  clear(): void {
    this.settings.clear();
  }
}
