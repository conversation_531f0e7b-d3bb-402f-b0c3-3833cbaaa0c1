import {GenericQueryCtx} from 'convex/server';
import {DataModel, Id} from '@/convex/_generated/dataModel';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

export class ConvexGameSettingsRepository implements GameSettingsRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async getByGameId(gameId: string): Promise<GameSettings | null> {
    const doc = await this.ctx.db
      .query('gameSettings')
      .withIndex('by_gameId', q => q.eq('gameId', gameId as Id<'games'>))
      .first();
    if (!doc) {
      return null;
    }
    return GameSettings.fromSnapshot({
      gameId: doc.gameId,
      maxCardsInDeck: doc.maxCardsInDeck,
      mulliganCount: doc.mulliganCount,
      startingHandSize: doc.startingHandSize
    });
  }
}
