import {GenericQueryCtx} from "convex/server";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {BoardReadRepository} from "@/src/server/Gaming/application/ports/BoardReadRepository";
import {Board} from "@/src/server/Gaming/domain/Board/Board";
import {PlayerBoard} from "@/src/server/Gaming/domain/Board/PlayerBoard";
import {PlacedCard} from "@/src/server/Gaming/domain/Board/PlacedCard";

export class ConvexBoardReadRepository implements BoardReadRepository {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async findByMatchId(matchId: string): Promise<Board | null> {
    const matchDoc = await this.ctx.db.get(matchId as Id<'matches'>);
    if (!matchDoc) {
      return null;
    }

    const rawBoardState = matchDoc.boardState || {
      player1Board: {
        firstRow: Array(8).fill(null),
        secondRow: Array(8).fill(null),
      },
      player2Board: {
        firstRow: Array(8).fill(null),
        secondRow: Array(8).fill(null),
      }
    };

    const player1Board = this.createPlayerBoardFromRaw(rawBoardState.player1Board);
    const player2Board = this.createPlayerBoardFromRaw(rawBoardState.player2Board);

    return Board.create({
      player1Board,
      player2Board,
    });
  }

  private createPlayerBoardFromRaw(rawBoard: {firstRow: unknown[]; secondRow: unknown[]}): PlayerBoard {
    const firstRow = rawBoard.firstRow.map((card: unknown) => 
      card ? PlacedCard.create({
        cardId: (card as {cardId: string}).cardId,
        placedAt: (card as {placedAt: string}).placedAt,
        slotIndex: (card as {slotIndex: number}).slotIndex,
        rowType: (card as {rowType: 'first' | 'second'}).rowType,
      }) : null
    );

    const secondRow = rawBoard.secondRow.map((card: unknown) => 
      card ? PlacedCard.create({
        cardId: (card as {cardId: string}).cardId,
        placedAt: (card as {placedAt: string}).placedAt,
        slotIndex: (card as {slotIndex: number}).slotIndex,
        rowType: (card as {rowType: 'first' | 'second'}).rowType,
      }) : null
    );

    return PlayerBoard.create({
      firstRow,
      secondRow,
    });
  }
}