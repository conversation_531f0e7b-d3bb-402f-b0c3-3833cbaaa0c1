import {BoardReadRepository} from '@/src/server/Gaming/application/ports/BoardReadRepository';
import {Board} from '@/src/server/Gaming/domain/Board/Board';

export class InMemoryBoardReadRepository implements BoardReadRepository {
  private boards = new Map<string, Board>();

  async findByMatchId(matchId: string): Promise<Board | null> {
    return this.boards.get(matchId) || null;
  }

  addBoard(matchId: string, board: Board): void {
    this.boards.set(matchId, board);
  }

  clear(): void {
    this.boards.clear();
  }
}