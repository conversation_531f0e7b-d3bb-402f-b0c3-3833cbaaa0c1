import {GenericQueryCtx} from "convex/server";
import {DataModel, Doc, Id} from "@/convex/_generated/dataModel";
import {Board} from "@/src/server/Gaming/domain/Board/Board";
import {BoardEnhancementService, EnhancedBoard, EnhancedPlacedCard} from "@/src/server/Gaming/application/ports/BoardEnhancementService";

export class ConvexBoardEnhancementService implements BoardEnhancementService {
  private readonly ctx: GenericQueryCtx<DataModel>;

  constructor(ctx: GenericQueryCtx<DataModel>) {
    this.ctx = ctx;
  }

  async enhanceBoardWithImages(board: Board, matchId: string, playerIds: string[]): Promise<EnhancedBoard> {
    const cardIds = board.getAllCardIds();
    
    if (cardIds.size === 0) {
      return this.createEmptyEnhancedBoard(board);
    }

    const matchDoc = await this.ctx.db.get(matchId as Id<'matches'>);
    if (!matchDoc) {
      return this.createEmptyEnhancedBoard(board);
    }

    const gameDecks = await this.resolveGameDecks(matchDoc, playerIds);
    const cardToCatalogMapByPlayer = this.buildCardToCatalogMapByPlayer(gameDecks, cardIds);
    const catalogCardMap = await this.loadCatalogCards(cardToCatalogMapByPlayer);

    const player1Id = playerIds[0];
    const player2Id = playerIds[1];

    return {
      player1Board: this.enhancePlayerBoard(board.getPlayer1Board(), player1Id, cardToCatalogMapByPlayer, catalogCardMap),
      player2Board: this.enhancePlayerBoard(board.getPlayer2Board(), player2Id, cardToCatalogMapByPlayer, catalogCardMap),
    };
  }

  private createEmptyEnhancedBoard(board: Board): EnhancedBoard {
    const enhanceEmptyCard = (card: unknown) => {
      if (!card) return null;
      const snapshot = (card as {toSnapshot(): {cardId: string; placedAt: string; slotIndex: number; rowType: 'first' | 'second'}}).toSnapshot();
      return {
        cardId: snapshot.cardId,
        placedAt: snapshot.placedAt,
        slotIndex: snapshot.slotIndex,
        rowType: snapshot.rowType,
        image: '',
      };
    };

    return {
      player1Board: {
        firstRow: board.getPlayer1Board().getFirstRow().map(enhanceEmptyCard),
        secondRow: board.getPlayer1Board().getSecondRow().map(enhanceEmptyCard),
      },
      player2Board: {
        firstRow: board.getPlayer2Board().getFirstRow().map(enhanceEmptyCard),
        secondRow: board.getPlayer2Board().getSecondRow().map(enhanceEmptyCard),
      }
    };
  }

  private async resolveGameDecks(matchDoc: Doc<'matches'>, playerIds: string[]): Promise<Array<Doc<'gameDecks'>>> {
    type PlayerGameDeckRef = {playerId: string; gameDeckId: Id<'gameDecks'>};
    const referenced = matchDoc.playerGameDecks as PlayerGameDeckRef[] | undefined;
    
    if (referenced && referenced.length > 0) {
      const fetched = await Promise.all(referenced.map(ref => this.ctx.db.get(ref.gameDeckId)));
      return fetched.filter((d): d is NonNullable<typeof d> => Boolean(d));
    }

    const allForGame = await this.ctx.db
      .query('gameDecks')
      .withIndex('by_gameId', q => q.eq('gameId', matchDoc.gameId))
      .collect();
    
    const latestPerPlayer = playerIds.map(p => {
      const options = allForGame.filter(d => d.playerId === p);
      if (options.length === 0) return null;
      return options.sort((a, b) => b._creationTime - a._creationTime)[0];
    }).filter((d): d is NonNullable<typeof d> => Boolean(d));
    
    return latestPerPlayer;
  }

  private buildCardToCatalogMapByPlayer(
    gameDecks: Array<Doc<'gameDecks'>>, 
    cardIds: Set<string>
  ): Map<string, Map<string, {catalogCardId: string; customImage?: string}>> {
    const cardToCatalogMapByPlayer = new Map<string, Map<string, {catalogCardId: string; customImage?: string}>>();
    
    gameDecks.forEach(deck => {
      const byCard = cardToCatalogMapByPlayer.get(deck.playerId) ?? new Map<string, {catalogCardId: string; customImage?: string}>();
      deck.cards.forEach(card => {
        if (cardIds.has(card.id)) {
          const customImage = card && typeof card === 'object' && card.data && typeof card.data === 'object'
            ? (card.data as {image?: string}).image
            : undefined;
          byCard.set(card.id, {
            catalogCardId: card.catalogCardId,
            customImage,
          });
        }
      });
      cardToCatalogMapByPlayer.set(deck.playerId, byCard);
    });

    return cardToCatalogMapByPlayer;
  }

  private async loadCatalogCards(
    cardToCatalogMapByPlayer: Map<string, Map<string, {catalogCardId: string; customImage?: string}>>
  ): Promise<Map<string, Doc<'catalogCards'>>> {
    const catalogCardIds = Array.from(
      new Set(
        Array.from(cardToCatalogMapByPlayer.values())
          .flatMap(map => Array.from(map.values()).map(info => info.catalogCardId))
      )
    );

    const catalogCards = await Promise.all(
      catalogCardIds.map(id => this.ctx.db.get(id as Id<'catalogCards'>))
    );

    const present = catalogCards.filter(
      (c): c is NonNullable<typeof c> => Boolean(c)
    );

    return new Map(present.map(card => [card._id, card]));
  }

  private enhancePlayerBoard(
    playerBoard: {getFirstRow(): unknown[]; getSecondRow(): unknown[]}, 
    playerId: string, 
    cardToCatalogMapByPlayer: Map<string, Map<string, {catalogCardId: string; customImage?: string}>>,
    catalogCardMap: Map<string, Doc<'catalogCards'>>
  ): {firstRow: (EnhancedPlacedCard | null)[]; secondRow: (EnhancedPlacedCard | null)[]} {
    const enhanceCard = (cardSnapshot: unknown): EnhancedPlacedCard | null => {
      if (!cardSnapshot) return null;
      const card = cardSnapshot as {cardId: string; placedAt: string; slotIndex: number; rowType: 'first' | 'second'};

      const byCard = cardToCatalogMapByPlayer.get(playerId);
      const cardInfo = byCard?.get(card.cardId);
      if (!cardInfo) return null;

      const catalogCard = catalogCardMap.get(cardInfo.catalogCardId as Id<'catalogCards'>);
      if (!catalogCard) return null;

      const resolvedImage = cardInfo.customImage || catalogCard.image;

      return {
        cardId: card.cardId,
        placedAt: card.placedAt,
        slotIndex: card.slotIndex,
        rowType: card.rowType,
        image: resolvedImage,
      };
    };

    return {
      firstRow: playerBoard.getFirstRow().map((card: unknown) => enhanceCard(card ? (card as {toSnapshot(): unknown}).toSnapshot() : null)),
      secondRow: playerBoard.getSecondRow().map((card: unknown) => enhanceCard(card ? (card as {toSnapshot(): unknown}).toSnapshot() : null)),
    };
  }
}
