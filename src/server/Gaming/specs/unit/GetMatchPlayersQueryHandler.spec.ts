import { GetMatchPlayersQueryHandler } from '../../application/queries/GetMatchPlayers/GetMatchPlayersQueryHandler';
import { AppUser } from '../../../Authentication/domain/AppUser/AppUser';
import { InMemoryMatchReadRepository } from '../../infrastructure/repositories/Match/InMemoryMatchReadRepository';
import { InMemoryAppUserRepository } from '../../../Authentication/infrastructure/repositories/AppUser/InMemoryAppUserRepository';
import { TestGetMatchPlayersPresenter } from '../helpers/presenters/TestGetMatchPlayersPresenter';

describe("When getting match players", () => {
  let handler: GetMatchPlayersQueryHandler;
  let matchRepository: InMemoryMatchReadRepository;
  let userRepository: InMemoryAppUserRepository;
  let presenter: TestGetMatchPlayersPresenter;

  beforeEach(() => {
    matchRepository = new InMemoryMatchReadRepository();
    userRepository = new InMemoryAppUserRepository();
    presenter = new TestGetMatchPlayersPresenter();
    handler = new GetMatchPlayersQueryHandler(matchRepository, userRepository);
  });

  it("should return current player and opponent info for authenticated user", async () => {
    // Arrange
    const userId = '00000000-0000-0000-0000-000000000001';
    const matchId = 'match1';
    const users = [
      AppUser.fromSnapshot({ id: userId, authId: 'conv1', name: 'Alice', image: 'avatar1.jpg', email: '<EMAIL>' }),
      AppUser.fromSnapshot({ id: '00000000-0000-0000-0000-000000000002', authId: 'conv2', name: 'Bob', image: 'avatar2.jpg', email: '<EMAIL>' })
    ];
    
    matchRepository.addMatch({ 
      id: 'match1', 
      gameId: 'game1', 
      players: [userId, '00000000-0000-0000-0000-000000000002'], 
      status: 'active' 
    });
    users.forEach(user => userRepository.add(user));

    // Act
    await handler.handle({ matchId, userId }, presenter);

    // Assert
    const result = presenter.getResult();
    expect(result).toEqual({
      currentPlayer: { id: userId, name: 'Alice', avatar: 'avatar1.jpg' },
      opponent: { id: '00000000-0000-0000-0000-000000000002', name: 'Bob', avatar: 'avatar2.jpg' }
    });
  });

  it("should return error when match not found", async () => {
    // Arrange
    const userId = 'user1';
    const matchId = 'nonexistent';

    // Act
    await handler.handle({ matchId, userId }, presenter);

    // Assert
    expect(presenter.getError()?.message).toBe('Match not found');
  });

  it("should return error when user not in match", async () => {
    // Arrange
    const userId = '00000000-0000-0000-0000-000000000003';
    const matchId = 'match1';
    
    matchRepository.addMatch({ 
      id: 'match1', 
      gameId: 'game1', 
      players: ['00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002'], 
      status: 'active' 
    });

    // Act
    await handler.handle({ matchId, userId }, presenter);

    // Assert
    expect(presenter.getError()?.message).toBe('User not in match');
  });
});