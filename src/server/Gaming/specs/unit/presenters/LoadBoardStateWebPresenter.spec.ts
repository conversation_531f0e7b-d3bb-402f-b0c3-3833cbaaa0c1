import {LoadBoardStateWebPresenter} from '@/src/server/Gaming/presentation/presenters/LoadBoardStateWebPresenter';

describe("When presenting LoadBoardState results", () => {
  let presenter: LoadBoardStateWebPresenter;

  beforeEach(() => {
    presenter = new LoadBoardStateWebPresenter();
  });

  describe("When displaying successful board data", () => {
    it("should create a valid view model with board state", () => {
      // Arrange
      const enhancedBoard = {
        player1Board: {
          firstRow: [null, {cardId: 'card1', placedAt: '2023-01-01', slotIndex: 1, rowType: 'first' as const, image: 'image1.jpg'}],
          secondRow: Array(8).fill(null)
        },
        player2Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null)
        }
      };

      // Act
      presenter.display(enhancedBoard, 'player1', 'main');

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel).toMatchObject({
        error: null,
        boardState: enhancedBoard,
        currentTurn: 'player1',
        gamePhase: 'main',
        lastUpdated: expect.any(Number)
      });
    });
  });

  describe("When displaying board data without optional parameters", () => {
    it("should create a view model with undefined values for optional fields", () => {
      // Arrange
      const enhancedBoard = {
        player1Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) },
        player2Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) }
      };

      // Act
      presenter.display(enhancedBoard);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel?.currentTurn).toBeUndefined();
      expect(viewModel?.gamePhase).toBeUndefined();
    });
  });

  describe("When displaying an error", () => {
    it("should create a view model with error message", () => {
      // Arrange
      const error = new Error('Board not found');

      // Act
      presenter.displayError(error);

      // Assert
      const viewModel = presenter.getViewModel();
      expect(viewModel?.error).toBe('Board not found');
      expect(viewModel?.boardState).toBeUndefined();
    });
  });
});