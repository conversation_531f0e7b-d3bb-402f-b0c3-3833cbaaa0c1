import {PlacedCard} from '@/src/server/Gaming/domain/Board/PlacedCard';

describe("When working with PlacedCard", () => {
  describe("When creating a placed card", () => {
    it("should store all properties correctly", () => {
      // Arrange
      const props = {
        cardId: "card1",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 2,
        rowType: "first" as const
      };

      // Act
      const placedCard = PlacedCard.create(props);

      // Assert
      expect(placedCard.getCardId()).toBe("card1");
      expect(placedCard.getPlacedAt()).toBe("2023-01-01T00:00:00Z");
      expect(placedCard.getSlotIndex()).toBe(2);
      expect(placedCard.getRowType()).toBe("first");
    });
  });

  describe("When converting to snapshot", () => {
    it("should return all properties", () => {
      // Arrange
      const props = {
        cardId: "card1",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 2,
        rowType: "first" as const
      };
      const placedCard = PlacedCard.create(props);

      // Act
      const snapshot = placedCard.toSnapshot();

      // Assert
      expect(snapshot).toEqual(props);
    });
  });
});