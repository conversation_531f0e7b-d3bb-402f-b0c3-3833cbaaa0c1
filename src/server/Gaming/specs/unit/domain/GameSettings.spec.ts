import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

describe('GameSettings', () => {
  describe('When creating game settings with mulligan count', () => {
    it('should include mulligan count in snapshot', () => {
      // Arrange
      const settings = GameSettings.fromSnapshot({
        gameId: 'g1',
        maxCardsInDeck: 30,
        mulliganCount: 3,
        startingHandSize: 7
      });

      // Act
      const snapshot = settings.toSnapshot();

      // Assert
      expect(snapshot.maxCardsInDeck).toBe(30);
      expect(snapshot.mulliganCount).toBe(3);
    });

    it('should handle zero mulligan count', () => {
      // Arrange
      const settings = GameSettings.fromSnapshot({
        gameId: 'g1',
        maxCardsInDeck: 30,
        mulliganCount: 0,
        startingHandSize: 7
      });

      // Act
      const snapshot = settings.toSnapshot();

      // Assert
      expect(snapshot.mulliganCount).toBe(0);
    });

    it('should provide mulligan count getter', () => {
      // Arrange
      const settings = GameSettings.fromSnapshot({
        gameId: 'g1',
        maxCardsInDeck: 30,
        mulliganCount: 2,
        startingHandSize: 7
      });

      // Act
      const mulliganCount = settings.getMulliganCount();

      // Assert
      expect(mulliganCount).toBe(2);
    });

    it('should determine if mulligan is enabled', () => {
      // Arrange
      const enabledSettings = GameSettings.fromSnapshot({
        gameId: 'g1',
        maxCardsInDeck: 30,
        mulliganCount: 1,
        startingHandSize: 7
      });
      const disabledSettings = GameSettings.fromSnapshot({
        gameId: 'g2',
        maxCardsInDeck: 30,
        mulliganCount: 0,
        startingHandSize: 7
      });

      // Act
      const enabled = enabledSettings.isMulliganEnabled();
      const disabled = disabledSettings.isMulliganEnabled();

      // Assert
      expect(enabled).toBe(true);
      expect(disabled).toBe(false);
    });
  });
});