import {PlayerBoard} from '@/src/server/Gaming/domain/Board/PlayerBoard';
import {PlacedCard} from '@/src/server/Gaming/domain/Board/PlacedCard';

describe("When working with PlayerBoard", () => {
  describe("When creating an empty player board", () => {
    it("should create a board with 8 empty slots per row", () => {
      // Arrange
      
      // Act
      const playerBoard = PlayerBoard.createEmpty();

      // Assert
      expect(playerBoard.getFirstRow()).toHaveLength(8);
      expect(playerBoard.getSecondRow()).toHaveLength(8);
      expect(playerBoard.getFirstRow().every(slot => slot === null)).toBe(true);
      expect(playerBoard.getSecondRow().every(slot => slot === null)).toBe(true);
    });
  });

  describe("When placing a card in a valid slot", () => {
    it("should place the card successfully", () => {
      // Arrange
      const playerBoard = PlayerBoard.createEmpty();
      const card = PlacedCard.create({
        cardId: "card1",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 0,
        rowType: "first"
      });

      // Act
      playerBoard.placeCardInSlot(card, "first", 0);

      // Assert
      expect(playerBoard.getFirstRow()[0]).toBe(card);
      expect(playerBoard.hasPlacedCards()).toBe(true);
    });
  });

  describe("When placing a card in an occupied slot", () => {
    it("should throw an error", () => {
      // Arrange
      const playerBoard = PlayerBoard.createEmpty();
      const card1 = PlacedCard.create({
        cardId: "card1",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 0,
        rowType: "first"
      });
      const card2 = PlacedCard.create({
        cardId: "card2",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 0,
        rowType: "first"
      });
      playerBoard.placeCardInSlot(card1, "first", 0);

      // Act
      const action = () => playerBoard.placeCardInSlot(card2, "first", 0);

      // Assert
      expect(action).toThrow("Slot 0 is already occupied");
    });
  });

  describe("When placing a card in an invalid slot", () => {
    it("should throw an error", () => {
      // Arrange
      const playerBoard = PlayerBoard.createEmpty();
      const card = PlacedCard.create({
        cardId: "card1",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 9,
        rowType: "first"
      });

      // Act
      const action = () => playerBoard.placeCardInSlot(card, "first", 9);

      // Assert
      expect(action).toThrow("Invalid slot index: 9");
    });
  });

  describe("When getting all card IDs", () => {
    it("should return all unique card IDs", () => {
      // Arrange
      const playerBoard = PlayerBoard.createEmpty();
      const card1 = PlacedCard.create({
        cardId: "card1",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 0,
        rowType: "first"
      });
      const card2 = PlacedCard.create({
        cardId: "card2",
        placedAt: "2023-01-01T00:00:00Z",
        slotIndex: 1,
        rowType: "second"
      });
      playerBoard.placeCardInSlot(card1, "first", 0);
      playerBoard.placeCardInSlot(card2, "second", 1);

      // Act
      const cardIds = playerBoard.getAllCardIds();

      // Assert
      expect(cardIds).toEqual(new Set(["card1", "card2"]));
    });
  });
});