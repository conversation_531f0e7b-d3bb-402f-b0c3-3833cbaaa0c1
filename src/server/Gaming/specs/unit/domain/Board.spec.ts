import {Board} from '@/src/server/Gaming/domain/Board/Board';

describe("When working with Board", () => {
  describe("When creating an empty board", () => {
    it("should create a board with empty player boards", () => {
      // Arrange
      
      // Act
      const board = Board.createEmpty();

      // Assert
      expect(board.getPlayer1Board().hasPlacedCards()).toBe(false);
      expect(board.getPlayer2Board().hasPlacedCards()).toBe(false);
      expect(board.hasPlacedCards()).toBe(false);
    });
  });

  describe("When placing a card", () => {
    it("should place the card in the correct player board", () => {
      // Arrange
      const board = Board.createEmpty();

      // Act
      board.placeCard(0, "card1", "first", 2);

      // Assert
      expect(board.hasPlacedCards()).toBe(true);
      expect(board.getPlayer1Board().hasPlacedCards()).toBe(true);
      expect(board.getPlayer2Board().hasPlacedCards()).toBe(false);
    });
  });

  describe("When getting all card IDs", () => {
    it("should return all unique card IDs from both players", () => {
      // Arrange
      const board = Board.createEmpty();
      board.placeCard(0, "card1", "first", 0);
      board.placeCard(1, "card2", "second", 1);

      // Act
      const cardIds = board.getAllCardIds();

      // Assert
      expect(cardIds).toEqual(new Set(["card1", "card2"]));
    });
  });

  describe("When converting to snapshot", () => {
    it("should return the correct snapshot structure", () => {
      // Arrange
      const board = Board.createEmpty();
      board.placeCard(0, "card1", "first", 0);

      // Act
      const snapshot = board.toSnapshot();

      // Assert
      expect(snapshot.player1Board.firstRow[0]).toEqual({
        cardId: "card1",
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: "first"
      });
      expect(snapshot.player1Board.firstRow[1]).toBeNull();
    });
  });
});