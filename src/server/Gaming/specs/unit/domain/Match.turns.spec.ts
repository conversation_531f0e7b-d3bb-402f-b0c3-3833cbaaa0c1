import {Match} from '@/src/server/Gaming/domain/Match/Match';

describe('Match - Turn Management', () => {
  describe('When passing turn', () => {
    it('should pass turn from current player to opponent', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });

      // Act
      match.passTurn('player1');

      // Assert
      expect(match.getCurrentTurn()).toBe('player2');
    });

    it('should pass turn from opponent back to current player', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player2'
      });

      // Act
      match.passTurn('player2');

      // Assert
      expect(match.getCurrentTurn()).toBe('player1');
    });

    it('should prevent non-current player from passing turn', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });

      // Act
      const act = () => {
        match.passTurn('player2');
      };

      // Assert
      expect(act).toThrow('Only the current turn player can pass the turn');
    });

    it('should prevent passing turn when no current turn is set', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });

      // Act
      const act = () => {
        match.passTurn('player1');
      };

      // Assert
      expect(act).toThrow('No current turn is set');
    });

    it('should prevent passing turn when match is not active', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'finished',
        currentTurn: 'player1'
      });

      // Act
      const act = () => {
        match.passTurn('player1');
      };

      // Assert
      expect(act).toThrow('Cannot pass turn when match is not active');
    });

    it('should prevent unknown player from passing turn', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });

      // Act
      const act = () => {
        match.passTurn('unknownPlayer');
      };

      // Assert
      expect(act).toThrow('Player unknownPlayer is not part of this match');
    });
  });

  describe('When initializing match turns', () => {
    it('should set first player as current turn when completing mulligan', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan'
      });

      // Act
      match.completeMulliganPhase();

      // Assert
      expect(match.getCurrentTurn()).toBe('player1');
      expect(match.getStatus()).toBe('active');
    });

    it('should set first player as current turn when making ready for play', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'setup'
      });

      // Act
      match.makeReadyForPlay();

      // Assert
      expect(match.getCurrentTurn()).toBe('player1');
      expect(match.getStatus()).toBe('active');
    });

    it('should set first player as current turn when starting directly', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'setup'
      });

      // Act
      match.startDirectly();

      // Assert
      expect(match.getCurrentTurn()).toBe('player1');
      expect(match.getStatus()).toBe('active');
    });

    it('should not override existing turn when completing mulligan', () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentTurn: 'player2'
      });

      // Act
      match.completeMulliganPhase();

      // Assert
      expect(match.getCurrentTurn()).toBe('player2');
    });
  });
});