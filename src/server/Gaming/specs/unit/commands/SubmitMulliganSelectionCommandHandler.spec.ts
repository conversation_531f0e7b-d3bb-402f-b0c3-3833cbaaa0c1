import {SubmitMulliganSelectionCommandHandler} from '@/src/server/Gaming/application/commands/Mulligan/SubmitMulliganSelection/SubmitMulliganSelectionCommandHandler';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {InMemoryGameDeckRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckRepository';
import {InMemoryGameDeckReadRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckReadRepository';
import {InMemoryMulliganSelectionRepository} from '@/src/server/Gaming/infrastructure/repositories/MulliganSelection/InMemoryMulliganSelectionRepository';
import {InMemoryGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryGameSettingsRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';

describe('SubmitMulliganSelectionCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let deckRepository: InMemoryGameDeckRepository;
  let deckReadRepository: InMemoryGameDeckReadRepository;
  let mulliganRepository: InMemoryMulliganSelectionRepository;
  let gameSettingsRepository: InMemoryGameSettingsRepository;
  let handler: SubmitMulliganSelectionCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    deckRepository = new InMemoryGameDeckRepository();
    deckReadRepository = new InMemoryGameDeckReadRepository();
    mulliganRepository = new InMemoryMulliganSelectionRepository();
    gameSettingsRepository = new InMemoryGameSettingsRepository();
    handler = new SubmitMulliganSelectionCommandHandler(matchRepository, deckRepository, deckReadRepository, mulliganRepository, gameSettingsRepository);
  });

  describe('When submitting mulligan selection', () => {
    it('should save selection and perform mulligan when not skipped', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);
      
      gameSettingsRepository.addSettingsForGame('g1', {maxCardsInDeck: 30, mulliganCount: 3, startingHandSize: 7});

      const cards = [
        GameCard.fromSnapshot({id: 'c1', catalogCardId: 'card1', data: {}}),
        GameCard.fromSnapshot({id: 'c2', catalogCardId: 'card2', data: {}}),
        GameCard.fromSnapshot({id: 'c3', catalogCardId: 'card3', data: {}})
      ];
      const deck = GameDeck.create({
        id: 'd1',
        gameId: 'g1',
        playerId: 'player1',
        cards
      });
      await deckRepository.save(deck);
      deckReadRepository.addDeck(deck.toSnapshot());

      // Act
      await handler.handle({
        matchId: 'm1',
        playerId: 'player1',
        selectedCardIds: ['c1', 'c2'],
        skipped: false,
        round: 1
      });

      // Assert
      const selection = await mulliganRepository.findByMatchIdAndPlayerId('m1', 'player1');
      expect(selection).toBeTruthy();
      expect(selection!.isSkipped()).toBe(false);
      expect(selection!.getSelectedCardIds()).toEqual(['c1', 'c2']);
    });

    it('should save skipped selection when player skips', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);
      
      gameSettingsRepository.addSettingsForGame('g1', {maxCardsInDeck: 30, mulliganCount: 3, startingHandSize: 7});

      // Act
      await handler.handle({
        matchId: 'm1',
        playerId: 'player1',
        selectedCardIds: [],
        skipped: true,
        round: 1
      });

      // Assert
      const selection = await mulliganRepository.findByMatchIdAndPlayerId('m1', 'player1');
      expect(selection).toBeTruthy();
      expect(selection!.isSkipped()).toBe(true);
      expect(selection!.getSelectedCardIds()).toEqual([]);
    });

    it('should complete mulligan phase when both players submitted', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);
      
      gameSettingsRepository.addSettingsForGame('g1', {maxCardsInDeck: 30, mulliganCount: 1, startingHandSize: 7});

      const cards1 = [GameCard.fromSnapshot({id: 'c1', catalogCardId: 'card1', data: {}})];
      const deck1 = GameDeck.create({id: 'd1', gameId: 'g1', playerId: 'player1', cards: cards1});
      await deckRepository.save(deck1);
      deckReadRepository.addDeck(deck1.toSnapshot());

      const cards2 = [GameCard.fromSnapshot({id: 'c2', catalogCardId: 'card2', data: {}})];
      const deck2 = GameDeck.create({id: 'd2', gameId: 'g1', playerId: 'player2', cards: cards2});
      await deckRepository.save(deck2);
      deckReadRepository.addDeck(deck2.toSnapshot());

      await handler.handle({
        matchId: 'm1',
        playerId: 'player1',
        selectedCardIds: ['c1'],
        skipped: false,
        round: 1
      });

      // Act
      await handler.handle({
        matchId: 'm1',
        playerId: 'player2',
        selectedCardIds: [],
        skipped: true,
        round: 1
      });

      // Assert
      const updatedMatch = await matchRepository.findById('m1');
      expect(updatedMatch!.getStatus()).toBe('active');
    });

    it('should throw error when match not found', async () => {
      // Arrange

      // Act
      const promise = handler.handle({
        matchId: 'nonexistent',
        playerId: 'player1',
        selectedCardIds: [],
        skipped: true,
        round: 1
      });

      // Assert
      await expect(promise).rejects.toThrow('Match not found');
    });

    it('should throw error when match not in mulligan phase', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      await matchRepository.save(match);

      // Act
      const promise = handler.handle({
        matchId: 'm1',
        playerId: 'player1',
        selectedCardIds: [],
        skipped: true,
        round: 1
      });

      // Assert
      await expect(promise).rejects.toThrow('Match is not in mulligan phase');
    });
  });

  describe('When submitting using role-based identification', () => {
    it('should resolve player from currentPlayerPosition', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g2',
        players: ['u1', 'u2'],
        status: 'waiting_for_mulligan',
        currentMulliganRound: 1
      });
      await matchRepository.save(match);

      gameSettingsRepository.addSettingsForGame('g2', {maxCardsInDeck: 30, mulliganCount: 1, startingHandSize: 7});

      const cards = [GameCard.fromSnapshot({id: 'x1', catalogCardId: 'cx1', data: {}})];
      const deck = GameDeck.create({id: 'dx1', gameId: 'g2', playerId: 'u2', cards});
      await deckRepository.save(deck);
      deckReadRepository.addDeck(deck.toSnapshot());

      // Act
      await handler.handle({
        matchId: 'm1',
        currentPlayerPosition: 'player2',
        selectedCardIds: ['x1'],
        skipped: false,
        round: 1
      });

      // Assert
      const selection = await mulliganRepository.findByMatchIdAndPlayerId('m1', 'u2');
      expect(selection).toBeTruthy();
      expect(selection!.isSkipped()).toBe(false);
      expect(selection!.getSelectedCardIds()).toEqual(['x1']);
    });
  });
});
