import {PlayCard<PERSON>ommandHandler} from '@/src/server/Gaming/application/commands/Match/PlayCard/PlayCardCommandHandler';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';

describe('PlayCardCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let handler: PlayCardCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    handler = new PlayCardCommandHandler(matchRepository);
  });

  describe('When playing a card in active match', () => {
    it('should place card on board', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      const boardState = updatedMatch!.getBoardState();
      expect(boardState!.player1Board.firstRow[2]).toEqual({
        cardId: 'card-789',
        placedAt: expect.any(String),
        slotIndex: 2,
        rowType: 'first'
      });
    });
  });

  describe('When playing card in non-active match', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'finished'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        cardId: 'card-789',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Assert
      await expect(act).rejects.toThrow();
    });
  });

  describe('When playing card with turn validation', () => {
    it('should allow current turn player to play card', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player2'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        cardId: 'card-123',
        rowType: 'second',
        slotIndex: 0,
        userId: 'player2'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      const boardState = updatedMatch!.getBoardState();
      expect(boardState!.player2Board.secondRow[0]).toEqual({
        cardId: 'card-123',
        placedAt: expect.any(String),
        slotIndex: 0,
        rowType: 'second'
      });
    });

    it('should prevent non-current turn player from playing card', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        userId: 'player2'
      });

      // Assert
      await expect(act).rejects.toThrow('It is not your turn to play a card');
    });

    it('should prevent playing card when no current turn is set', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        userId: 'player1'
      });

      // Assert
      await expect(act).rejects.toThrow('No current turn is set in this match');
    });

    it('should prevent unknown player from playing card even if turn matches', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'unknownPlayer'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        cardId: 'card-123',
        rowType: 'first',
        slotIndex: 0,
        userId: 'unknownPlayer'
      });

      // Assert
      await expect(act).rejects.toThrow('Player is not part of this match');
    });
  });

  describe('When preventing card duplication', () => {
    it('should prevent playing same card twice in different positions', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      await handler.handle({
        matchId,
        cardId: 'card-duplicate',
        rowType: 'first',
        slotIndex: 0,
        userId: 'player1'
      });

      // Act
      const act = () => handler.handle({
        matchId,
        cardId: 'card-duplicate',
        rowType: 'first',
        slotIndex: 1,
        userId: 'player1'
      });

      // Assert
      await expect(act()).rejects.toThrow('Card card-duplicate has already been played');
    });

    it('should be idempotent when playing same card in same position', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // First play
      await handler.handle({
        matchId,
        cardId: 'card-idempotent',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Act
      await handler.handle({
        matchId,
        cardId: 'card-idempotent',
        rowType: 'first',
        slotIndex: 2,
        userId: 'player1'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      const boardState = updatedMatch!.getBoardState();
      expect(boardState!.player1Board.firstRow[2]).toEqual({
        cardId: 'card-idempotent',
        placedAt: expect.any(String),
        slotIndex: 2,
        rowType: 'first'
      });

      // Verify no other slots are occupied
      const firstRowOccupied = boardState!.player1Board.firstRow.filter(card => card !== null);
      expect(firstRowOccupied).toHaveLength(1);
    });

    it('should prevent playing same card in different row types', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      await handler.handle({
        matchId,
        cardId: 'card-different-row',
        rowType: 'first',
        slotIndex: 0,
        userId: 'player1'
      });

      // Act
      const act = handler.handle({
        matchId,
        cardId: 'card-different-row',
        rowType: 'second',
        slotIndex: 0,
        userId: 'player1'
      });

      // Assert
      await expect(act).rejects.toThrow('Card card-different-row has already been played');
    });
  });
});
