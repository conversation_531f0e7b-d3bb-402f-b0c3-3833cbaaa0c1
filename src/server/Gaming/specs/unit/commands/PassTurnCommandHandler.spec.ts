import {PassTurnCommandHandler} from '@/src/server/Gaming/application/commands/Match/PassTurn/PassTurnCommandHandler';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {createFakeEventBus} from '@/src/server/Shared/infrastructure/gateways/Context/FakeEventBus';

describe('PassTurnCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let eventBus: ReturnType<typeof createFakeEventBus>;
  let handler: PassTurnCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    eventBus = createFakeEventBus();
    handler = new PassTurnCommandHandler(matchRepository, eventBus);
  });

  describe('When passing turn in active match', () => {
    it('should pass turn to opponent', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        userId: 'player1'
      });

      // Assert
      const updatedMatch = await matchRepository.findById(matchId);
      expect(updatedMatch!.getCurrentTurn()).toBe('player2');
    });

    it('should dispatch TurnPassed event', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({
        matchId,
        userId: 'player1'
      });

      // Assert
      expect(eventBus.recorded[0]).toMatchObject({
        type: 'match',
        gameId: 'game-456',
        id: matchId,
        event: {
          type: 'TurnPassed',
          payload: {
            fromPlayer: 'player1',
            toPlayer: 'player2',
            matchId: matchId,
          },
        },
      });
    });
  });

  describe('When passing turn by non-current player', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        userId: 'player2'
      });

      // Assert
      await expect(act).rejects.toThrow('Only the current turn player can pass the turn');
    });
  });

  describe('When passing turn in non-active match', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'finished',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        userId: 'player1'
      });

      // Assert
      await expect(act).rejects.toThrow('Cannot pass turn when match is not active');
    });
  });

  describe('When passing turn with unknown player', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        userId: 'unknownPlayer'
      });

      // Assert
      await expect(act).rejects.toThrow('Player unknownPlayer is not part of this match');
    });
  });

  describe('When passing turn without current turn set', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        gameId: 'game-456',
        players: ['player1', 'player2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      const act = handler.handle({
        matchId,
        userId: 'player1'
      });

      // Assert
      await expect(act).rejects.toThrow('No current turn is set');
    });
  });

  describe('When match not found', () => {
    it('should throw error', async () => {
      // Act
      const act = handler.handle({
        matchId: 'non-existent-match',
        userId: 'player1'
      });

      // Assert
      await expect(act).rejects.toThrow('Match not found');
    });
  });
});