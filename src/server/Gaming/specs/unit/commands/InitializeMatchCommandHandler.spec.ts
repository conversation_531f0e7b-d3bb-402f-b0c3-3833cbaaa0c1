import {InitializeMatchCommandHandler} from '@/src/server/Gaming/application/commands/Match/InitializeMatch/InitializeMatchCommandHandler';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {InMemoryGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryGameSettingsRepository';
import {StartMulliganPhaseCommandHandler} from '@/src/server/Gaming/application/commands/Mulligan/StartMulliganPhase/StartMulliganPhaseCommandHandler';
import {Match} from '@/src/server/Gaming/domain/Match/Match';

describe('InitializeMatchCommandHandler', () => {
  let matchRepository: InMemoryMatchRepository;
  let gameSettingsRepository: InMemoryGameSettingsRepository;
  let startMulliganPhaseHandler: StartMulliganPhaseCommandHandler;
  let handler: InitializeMatchCommandHandler;

  beforeEach(() => {
    matchRepository = new InMemoryMatchRepository();
    gameSettingsRepository = new InMemoryGameSettingsRepository();
    startMulliganPhaseHandler = new StartMulliganPhaseCommandHandler(matchRepository);
    handler = new InitializeMatchCommandHandler(matchRepository, gameSettingsRepository, startMulliganPhaseHandler);
  });

  describe('When initializing match with mulligan enabled', () => {
    it('should start mulligan phase', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'setup'
      });
      await matchRepository.save(match);
      gameSettingsRepository.addSettingsForGame('g1', {maxCardsInDeck: 15, mulliganCount: 1, startingHandSize: 7});

      // Act
      await handler.handle({matchId: 'm1'});

      // Assert
      const updatedMatch = await matchRepository.findById('m1');
      expect(updatedMatch!.getStatus()).toBe('waiting_for_mulligan');
    });
  });

  describe('When initializing match with mulligan disabled', () => {
    it('should start directly to active', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'setup'
      });
      await matchRepository.save(match);
      gameSettingsRepository.addSettingsForGame('g1', {maxCardsInDeck: 15, mulliganCount: 0, startingHandSize: 7});

      // Act
      await handler.handle({matchId: 'm1'});

      // Assert
      const updatedMatch = await matchRepository.findById('m1');
      expect(updatedMatch!.getStatus()).toBe('active');
    });
  });

  describe('When match not found', () => {
    it('should throw error', async () => {
      // Arrange

      // Act
      const promise = handler.handle({matchId: 'nonexistent'});

      // Assert
      await expect(promise).rejects.toThrow('Match not found');
    });
  });

  describe('When match not in setup phase', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      await matchRepository.save(match);

      // Act
      const promise = handler.handle({matchId: 'm1'});

      // Assert
      await expect(promise).rejects.toThrow('Match is not in setup phase');
    });
  });

  describe('When game settings not found', () => {
    it('should throw error', async () => {
      // Arrange
      const match = Match.create({
        id: 'm1',
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'setup'
      });
      await matchRepository.save(match);

      // Act
      const promise = handler.handle({matchId: 'm1'});

      // Assert
      await expect(promise).rejects.toThrow('Game settings not found');
    });
  });
});