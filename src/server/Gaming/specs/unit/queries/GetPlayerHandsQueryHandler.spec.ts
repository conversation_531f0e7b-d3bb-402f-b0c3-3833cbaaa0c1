
import {GetPlayerHandsQueryHandler} from '@/src/server/Gaming/application/queries/GetPlayerHands/GetPlayerHandsQueryHandler';
import {GetPlayerHandsQuery} from '@/src/server/Gaming/application/queries/GetPlayerHands/GetPlayerHandsQuery';
import {GetPlayerHandsPresenter, GetPlayerHandsResult} from '@/src/server/Gaming/application/ports/GetPlayerHandsPresenter';
import {InMemoryMatchReadRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchReadRepository';
import {InMemoryGameDeckReadRepository} from '@/src/server/Gaming/infrastructure/repositories/GameDeck/InMemoryGameDeckReadRepository';
import {InMemoryCatalogCardListRepository} from '@/src/server/DeckBuilding/infrastructure/repositories/CatalogCardList/InMemoryCatalogCardListRepository';
import {InMemoryGameSettingsRepository} from '@/src/server/Gaming/infrastructure/repositories/GameSettings/InMemoryGameSettingsRepository';
import {InMemoryMulliganSelectionRepository} from '@/src/server/Gaming/infrastructure/repositories/MulliganSelection/InMemoryMulliganSelectionRepository';

describe('When getting player hands', () => {
  let handler: GetPlayerHandsQueryHandler;
  let matchRepository: InMemoryMatchReadRepository;
  let deckRepository: InMemoryGameDeckReadRepository;
  let catalogRepository: InMemoryCatalogCardListRepository;
  let gameSettingsRepository: InMemoryGameSettingsRepository;
  let mulliganRepository: InMemoryMulliganSelectionRepository;
  let presenter: TestGetPlayerHandsPresenter;

  beforeEach(() => {
    matchRepository = new InMemoryMatchReadRepository();
    deckRepository = new InMemoryGameDeckReadRepository();
    catalogRepository = new InMemoryCatalogCardListRepository();
    gameSettingsRepository = new InMemoryGameSettingsRepository();
    mulliganRepository = new InMemoryMulliganSelectionRepository();
    presenter = new TestGetPlayerHandsPresenter();

    handler = new GetPlayerHandsQueryHandler(
      matchRepository,
      deckRepository,
      catalogRepository,
      gameSettingsRepository,
      mulliganRepository
    );
  });

  it('should display error when match not found', async () => {
    // Arrange
    const query: GetPlayerHandsQuery = {
      matchId: 'non-existent-match',
      userId: 'user-123'
    };

    // Act
    await handler.handle(query, presenter);

    // Assert
    expect(presenter.error).toBe('Match not found');
    expect(presenter.result).toBeNull();
  });

  it('should return empty hands when deck not found', async () => {
    // Arrange
    const mockMatch = {
      getGameId: () => 'game-123',
      getOpponentOf: () => 'opponent-123',
      getPlayers: () => ['player-123', 'opponent-123'],
      getBoardState: () => null
    };
    matchRepository.findById = vi.fn().mockResolvedValue(mockMatch);

    const query: GetPlayerHandsQuery = {
      matchId: 'match-123',
      userId: 'player-123'
    };

    // Act
    await handler.handle(query, presenter);

    // Assert
    expect(presenter.error).toBeNull();
    expect(presenter.result).toEqual({
      player1: [],
      player2: [],
      player1Count: 0,
      player2Count: 0
    });
  });

  it('should return player1 hands when user is first player', async () => {
    // Arrange
    const mockMatch = {
      getGameId: () => 'game-123',
      getOpponentOf: () => 'opponent-123',
      getPlayers: () => ['player-123', 'opponent-123'],
      getBoardState: () => null
    };

    const mockPlayerDeck = {
      toSnapshot: () => ({
        cards: [
          { id: 'card-1', catalogCardId: 'catalog-1', data: { image: 'custom-1.jpg' } },
          { id: 'card-2', catalogCardId: 'catalog-2', data: { image: 'custom-2.jpg' } }
        ]
      })
    };

    const mockOpponentDeck = {
      toSnapshot: () => ({
        cards: Array.from({ length: 7 }, (_, i) => ({ 
          id: `opp-${i + 1}`, 
          catalogCardId: 'catalog-1', 
          data: {} 
        }))
      })
    };

    const mockGameSettings = {
      getMulliganCount: () => 1,
      getStartingHandSize: () => 7
    };

    const mockCatalogSubset = {
      toSnapshot: () => ({
        cards: [
          { id: 'catalog-1', name: 'Test Card 1', image: 'default-1.jpg' },
          { id: 'catalog-2', name: 'Test Card 2', image: 'default-2.jpg' }
        ]
      })
    };

    matchRepository.findById = vi.fn().mockResolvedValue(mockMatch);
    deckRepository.findLatestByGameIdAndPlayerId = vi.fn()
      .mockResolvedValueOnce(mockPlayerDeck)
      .mockResolvedValueOnce(mockOpponentDeck);
    gameSettingsRepository.getByGameId = vi.fn().mockResolvedValue(mockGameSettings);
    catalogRepository.getByIds = vi.fn().mockResolvedValue(mockCatalogSubset);
    mulliganRepository.findAllByMatchIdAndPlayerId = vi.fn().mockResolvedValue([]);

    const query: GetPlayerHandsQuery = {
      matchId: 'match-123',
      userId: 'player-123'
    };

    // Act
    await handler.handle(query, presenter);

    // Assert
    expect(presenter.error).toBeNull();
    expect(presenter.result).toEqual({
      player1: [
        { instanceId: 'card-1', catalogCardId: 'catalog-1', image: 'custom-1.jpg' },
        { instanceId: 'card-2', catalogCardId: 'catalog-2', image: 'custom-2.jpg' }
      ],
      player2: [],
      player1Count: 2,
      player2Count: 7
    });
  });

  it('should return player2 hands when user is second player', async () => {
    // Arrange
    const mockMatch = {
      getGameId: () => 'game-123',
      getOpponentOf: () => 'opponent-123',
      getPlayers: () => ['opponent-123', 'player-123'],
      getBoardState: () => null
    };

    const mockPlayerDeck = {
      toSnapshot: () => ({
        cards: [
          { id: 'card-1', catalogCardId: 'catalog-1', data: { image: 'custom-1.jpg' } },
          { id: 'card-2', catalogCardId: 'catalog-2', data: { image: 'custom-2.jpg' } }
        ]
      })
    };

    const mockOpponentDeck = {
      toSnapshot: () => ({
        cards: Array.from({ length: 7 }, (_, i) => ({ 
          id: `opp-${i + 1}`, 
          catalogCardId: 'catalog-1', 
          data: {} 
        }))
      })
    };

    const mockGameSettings = {
      getMulliganCount: () => 1,
      getStartingHandSize: () => 7
    };

    const mockCatalogSubset = {
      toSnapshot: () => ({
        cards: [
          { id: 'catalog-1', name: 'Test Card 1', image: 'default-1.jpg' },
          { id: 'catalog-2', name: 'Test Card 2', image: 'default-2.jpg' }
        ]
      })
    };

    matchRepository.findById = vi.fn().mockResolvedValue(mockMatch);
    deckRepository.findLatestByGameIdAndPlayerId = vi.fn()
      .mockResolvedValueOnce(mockPlayerDeck)
      .mockResolvedValueOnce(mockOpponentDeck);
    gameSettingsRepository.getByGameId = vi.fn().mockResolvedValue(mockGameSettings);
    catalogRepository.getByIds = vi.fn().mockResolvedValue(mockCatalogSubset);
    mulliganRepository.findAllByMatchIdAndPlayerId = vi.fn().mockResolvedValue([]);

    const query: GetPlayerHandsQuery = {
      matchId: 'match-123',
      userId: 'player-123'
    };

    // Act
    await handler.handle(query, presenter);

    // Assert
    expect(presenter.error).toBeNull();
    expect(presenter.result).toEqual({
      player1: [],
      player2: [
        { instanceId: 'card-1', catalogCardId: 'catalog-1', image: 'custom-1.jpg' },
        { instanceId: 'card-2', catalogCardId: 'catalog-2', image: 'custom-2.jpg' }
      ],
      player1Count: 7,
      player2Count: 2
    });
  });

  it('should filter out cards selected in completed mulligan rounds', async () => {
    // Arrange
    const mockMatch = {
      getGameId: () => 'game-123',
      getOpponentOf: () => 'opponent-123',
      getPlayers: () => ['player-123', 'opponent-123'],
      getBoardState: () => null
    };

    const mockPlayerDeck = {
      toSnapshot: () => ({
        cards: [
          { id: 'card-1', catalogCardId: 'catalog-1', data: { image: 'custom-1.jpg' } },
          { id: 'card-2', catalogCardId: 'catalog-2', data: { image: 'custom-2.jpg' } },
          { id: 'card-3', catalogCardId: 'catalog-1', data: { image: 'custom-3.jpg' } }
        ]
      })
    };

    const mockGameSettings = {
      getMulliganCount: () => 1,
      getStartingHandSize: () => 7
    };

    const mockCatalogSubset = {
      toSnapshot: () => ({
        cards: [
          { id: 'catalog-1', name: 'Test Card 1', image: 'default-1.jpg' },
          { id: 'catalog-2', name: 'Test Card 2', image: 'default-2.jpg' }
        ]
      })
    };

    const mockMulliganSelections = [
      {
        getRound: () => 1,
        isSkipped: () => false,
        getSelectedCardIds: () => ['card-1']
      }
    ];

    matchRepository.findById = vi.fn().mockResolvedValue(mockMatch);
    deckRepository.findLatestByGameIdAndPlayerId = vi.fn()
      .mockResolvedValueOnce(mockPlayerDeck)
      .mockResolvedValueOnce(null);
    gameSettingsRepository.getByGameId = vi.fn().mockResolvedValue(mockGameSettings);
    catalogRepository.getByIds = vi.fn().mockResolvedValue(mockCatalogSubset);
    mulliganRepository.findAllByMatchIdAndPlayerId = vi.fn().mockResolvedValue(mockMulliganSelections);

    const query: GetPlayerHandsQuery = {
      matchId: 'match-123',
      userId: 'player-123'
    };

    // Act
    await handler.handle(query, presenter);

    // Assert
    expect(presenter.error).toBeNull();
    expect(presenter.result?.player1).toHaveLength(2);
    expect(presenter.result?.player1).not.toContainEqual(
      expect.objectContaining({ instanceId: 'card-1' })
    );
  });
});

class TestGetPlayerHandsPresenter implements GetPlayerHandsPresenter {
  result: GetPlayerHandsResult | null = null;
  error: string | null = null;

  display(result: GetPlayerHandsResult): void {
    this.result = result;
    this.error = null;
  }

  displayError(error: Error): void {
    this.error = error.message;
    this.result = null;
  }
}