import {LoadBoardStateQueryHandler} from '@/src/server/Gaming/application/queries/LoadBoardState/LoadBoardStateQueryHandler';
import {InMemoryBoardReadRepository} from '@/src/server/Gaming/infrastructure/repositories/Board/InMemoryBoardReadRepository';
import {InMemoryMatchReadRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchReadRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadBoardStatePresenter} from '@/src/server/Gaming/application/ports/LoadBoardStatePresenter';
import {BoardEnhancementService} from '@/src/server/Gaming/application/ports/BoardEnhancementService';
import {Board} from '@/src/server/Gaming/domain/Board/Board';
import {Match} from '@/src/server/Gaming/domain/Match/Match';

describe("When handling LoadBoardState query", () => {
  let boardRepository: InMemoryBoardReadRepository;
  let matchRepository: InMemoryMatchReadRepository;
  let enhancementService: MockProxy<BoardEnhancementService>;
  let presenter: MockProxy<LoadBoardStatePresenter>;
  let handler: LoadBoardStateQueryHandler;

  beforeEach(() => {
    boardRepository = new InMemoryBoardReadRepository();
    matchRepository = new InMemoryMatchReadRepository();
    enhancementService = mock<BoardEnhancementService>();
    presenter = mock<LoadBoardStatePresenter>();
    handler = new LoadBoardStateQueryHandler(
      boardRepository,
      matchRepository,
      enhancementService
    );
  });

  describe("When match and board exist", () => {
    it("should display enhanced board with match data", async () => {
      // Arrange
      const match = Match.create({
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active',
        currentTurn: 'player1',
        gamePhase: 'main'
      });
      const matchId = await matchRepository.save(match);
      const board = Board.createEmpty();
      boardRepository.addBoard(matchId, board);
      
      const enhancedBoard = {
        player1Board: { firstRow: [], secondRow: [] },
        player2Board: { firstRow: [], secondRow: [] }
      };
      enhancementService.enhanceBoardWithImages.mockResolvedValue(enhancedBoard);

      // Act
      await handler.handle({matchId}, presenter);

      // Assert
      expect(enhancementService.enhanceBoardWithImages).toHaveBeenCalledWith(
        board,
        matchId,
        ['player1', 'player2']
      );
      expect(presenter.display).toHaveBeenCalledWith(
        enhancedBoard,
        'player1',
        'main'
      );
    });
  });

  describe("When match does not exist", () => {
    it("should display match not found error", async () => {
      // Act
      await handler.handle({matchId: 'unknown'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('Match not found'));
    });
  });

  describe("When board does not exist", () => {
    it("should display board not found error", async () => {
      // Arrange
      const match = Match.create({
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);

      // Act
      await handler.handle({matchId}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('Board not found'));
    });
  });

  describe("When enhancement service throws an error", () => {
    it("should display the error", async () => {
      // Arrange
      const match = Match.create({
        gameId: 'g1',
        players: ['player1', 'player2'],
        status: 'active'
      });
      const matchId = await matchRepository.save(match);
      const board = Board.createEmpty();
      boardRepository.addBoard(matchId, board);
      
      const error = new Error('Enhancement failed');
      enhancementService.enhanceBoardWithImages.mockRejectedValue(error);

      // Act
      await handler.handle({matchId}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(error);
    });
  });
});