import {
  LoadMatchByIdQueryHandler
} from '@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQueryHandler';
import {InMemoryMatchRepository} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryMatchRepository';
import {mock, MockProxy} from 'vitest-mock-extended';
import {LoadMatchByIdPresenter} from '@/src/server/Gaming/application/ports/LoadMatchByIdPresenter';
import {
  InMemoryFailingMatchRepository
} from '@/src/server/Gaming/infrastructure/repositories/Match/InMemoryFailingMatchRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';

describe('LoadMatchByIdQueryHandler', () => {
  describe('When retrieving the match successfully', () => {
    let repository: InMemoryMatchRepository;
    let gameSettingsRepository: MockProxy<GameSettingsRepository>;
    let mulliganRepository: MockProxy<MulliganSelectionRepository>;
    let presenter: MockProxy<LoadMatchByIdPresenter>;
    let handler: LoadMatchByIdQueryHandler;

    beforeEach(() => {
      repository = new InMemoryMatchRepository();
      gameSettingsRepository = mock<GameSettingsRepository>();
      mulliganRepository = mock<MulliganSelectionRepository>();
      presenter = mock<LoadMatchByIdPresenter>();
      handler = new LoadMatchByIdQueryHandler(repository, gameSettingsRepository, mulliganRepository);
    });

    describe('When the match exists', () => {
      it('should display it', async () => {
        // Arrange
        const match = Match.create({gameId: 'g1', players: ['u1', 'u2'], status: 'setup'});
        const matchId = await repository.save(match);
        const gameSettings = GameSettings.fromSnapshot({
          gameId: 'g1',
          maxCardsInDeck: 60,
          mulliganCount: 2,
          startingHandSize: 7
        });
        const mulliganSelections: MulliganSelection[] = [];
        
        gameSettingsRepository.getByGameId.mockResolvedValue(gameSettings);
        mulliganRepository.findAllByMatchIdAndPlayerId.mockResolvedValue(mulliganSelections);

        // Act
        await handler.handle({matchId, userId: 'u1'}, presenter);

        // Assert
        expect(presenter.display).toHaveBeenCalledWith(match, 'u1', false);
      });
    });

    describe('When the match does not exist', () => {
      it('should display a not found error', async () => {
        // Act
        await handler.handle({matchId: 'unknown', userId: 'u1'}, presenter);

        // Assert
        expect(presenter.displayError).toHaveBeenCalledWith(new Error('Match not found'));
      });
    });
  });

  describe('When an error occurs', () => {
    it('should display the error', async () => {
      // Arrange
      const repository = new InMemoryFailingMatchRepository();
      const gameSettingsRepository = mock<GameSettingsRepository>();
      const mulliganRepository = mock<MulliganSelectionRepository>();
      const presenter = mock<LoadMatchByIdPresenter>();
      const handler = new LoadMatchByIdQueryHandler(repository, gameSettingsRepository, mulliganRepository);

      // Act
      await handler.handle({matchId: 'm1', userId: 'u1'}, presenter);

      // Assert
      expect(presenter.displayError).toHaveBeenCalledWith(new Error('m1'));
    });
  });
});
