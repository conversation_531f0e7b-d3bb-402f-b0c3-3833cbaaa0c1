import { GetMatchPlayersPresenter } from '@/src/server/Gaming/application/ports/GetMatchPlayersPresenter';

export class TestGetMatchPlayersPresenter implements GetMatchPlayersPresenter {
  private result: { currentPlayer: { id: string; name: string; avatar: string }; opponent: { id: string; name: string; avatar: string } } | null = null;
  private error: Error | null = null;

  display(
    currentPlayer: { id: string; name: string; avatar: string },
    opponent: { id: string; name: string; avatar: string }
  ): void {
    this.result = { currentPlayer, opponent };
    this.error = null;
  }

  displayError(error: Error): void {
    this.error = error;
    this.result = null;
  }

  getResult() {
    return this.result;
  }

  getError() {
    return this.error;
  }
}