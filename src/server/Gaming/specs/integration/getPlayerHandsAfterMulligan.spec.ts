import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe('When getting player hands after mulligan', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it('should return only 7 cards after mulligan is completed', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const c1 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 1', image: '1.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    const c2 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 2', image: '2.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    const c3 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 3', image: '3.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'playing',
      gamePhase: 'playing',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'gc1', catalogCardId: c1 as string, data: {image: 'card1.jpg'}},
        {id: 'gc2', catalogCardId: c2 as string, data: {image: 'card2.jpg'}},
        {id: 'gc3', catalogCardId: c3 as string, data: {image: 'card3.jpg'}},
        {id: 'gc4', catalogCardId: c1 as string, data: {image: 'card4.jpg'}},
        {id: 'gc5', catalogCardId: c2 as string, data: {image: 'card5.jpg'}},
        {id: 'gc6', catalogCardId: c3 as string, data: {image: 'card6.jpg'}},
        {id: 'gc7', catalogCardId: c1 as string, data: {image: 'card7.jpg'}},
        {id: 'gc8', catalogCardId: c2 as string, data: {image: 'card8.jpg'}},
        {id: 'gc9', catalogCardId: c3 as string, data: {image: 'card9.jpg'}},
        {id: 'gc10', catalogCardId: c1 as string, data: {image: 'card10.jpg'}},
      ],
    }));

    await sophie.run(ctx => ctx.db.insert('mulliganSelections', {
      matchId,
      playerId: SOPHIE_IDENTITY.subject,
      round: 1,
      selectedCardIds: ['gc8', 'gc9', 'gc10'],
      skipped: false
    }));

    await john.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: 'jgc1', catalogCardId: c1 as string, data: {image: 'john-card1.jpg'}},
        {id: 'jgc2', catalogCardId: c2 as string, data: {image: 'john-card2.jpg'}},
        {id: 'jgc3', catalogCardId: c3 as string, data: {image: 'john-card3.jpg'}},
        {id: 'jgc4', catalogCardId: c1 as string, data: {image: 'john-card4.jpg'}},
        {id: 'jgc5', catalogCardId: c2 as string, data: {image: 'john-card5.jpg'}},
        {id: 'jgc6', catalogCardId: c3 as string, data: {image: 'john-card6.jpg'}},
        {id: 'jgc7', catalogCardId: c1 as string, data: {image: 'john-card7.jpg'}},
      ],
    }));

    // Act
    const result = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(result).toBeTruthy();
    expect(result!.player1).toHaveLength(7);
    expect(result!.player1).toEqual([
      { instanceId: 'gc1', catalogCardId: c1, image: 'card1.jpg' },
      { instanceId: 'gc2', catalogCardId: c2, image: 'card2.jpg' },
      { instanceId: 'gc3', catalogCardId: c3, image: 'card3.jpg' },
      { instanceId: 'gc4', catalogCardId: c1, image: 'card4.jpg' },
      { instanceId: 'gc5', catalogCardId: c2, image: 'card5.jpg' },
      { instanceId: 'gc6', catalogCardId: c3, image: 'card6.jpg' },
      { instanceId: 'gc7', catalogCardId: c1, image: 'card7.jpg' }
    ]);
    expect(result!.player2).toEqual([]);
    expect(result!.player2Count).toBe(7);
  });

  it('should return all 7 cards when no mulligan selections exist', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const c1 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 1', image: '1.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'playing',
      gamePhase: 'playing',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'gc1', catalogCardId: c1 as string, data: {image: 'card1.jpg'}},
        {id: 'gc2', catalogCardId: c1 as string, data: {image: 'card2.jpg'}},
        {id: 'gc3', catalogCardId: c1 as string, data: {image: 'card3.jpg'}},
        {id: 'gc4', catalogCardId: c1 as string, data: {image: 'card4.jpg'}},
        {id: 'gc5', catalogCardId: c1 as string, data: {image: 'card5.jpg'}},
        {id: 'gc6', catalogCardId: c1 as string, data: {image: 'card6.jpg'}},
        {id: 'gc7', catalogCardId: c1 as string, data: {image: 'card7.jpg'}},
        {id: 'gc8', catalogCardId: c1 as string, data: {image: 'card8.jpg'}},
        {id: 'gc9', catalogCardId: c1 as string, data: {image: 'card9.jpg'}},
        {id: 'gc10', catalogCardId: c1 as string, data: {image: 'card10.jpg'}},
      ],
    }));

    // Act
    const result = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(result).toBeTruthy();
    expect(result!.player1).toHaveLength(7);
    expect(result!.player1).toEqual([
      { instanceId: 'gc1', catalogCardId: c1, image: 'card1.jpg' },
      { instanceId: 'gc2', catalogCardId: c1, image: 'card2.jpg' },
      { instanceId: 'gc3', catalogCardId: c1, image: 'card3.jpg' },
      { instanceId: 'gc4', catalogCardId: c1, image: 'card4.jpg' },
      { instanceId: 'gc5', catalogCardId: c1, image: 'card5.jpg' },
      { instanceId: 'gc6', catalogCardId: c1, image: 'card6.jpg' },
      { instanceId: 'gc7', catalogCardId: c1, image: 'card7.jpg' }
    ]);
  });
});