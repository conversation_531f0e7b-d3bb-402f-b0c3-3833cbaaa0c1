import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe("When playing the same card twice quickly", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it("should not throw and board/hand stay consistent", async () => {
    // Arrange
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    const c1 = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Card 1",
      image: "1.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      currentTurn: SOPHIE_IDENTITY.subject,
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    const sophieDeckId = (await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: c1 as string, data: {image: "s1.jpg"}},
        {id: "gc2", catalogCardId: c1 as string, data: {image: "s2.jpg"}},
        {id: "gc3", catalogCardId: c1 as string, data: {image: "s3.jpg"}},
        {id: "gc4", catalogCardId: c1 as string, data: {image: "s4.jpg"}},
        {id: "gc5", catalogCardId: c1 as string, data: {image: "s5.jpg"}},
        {id: "gc6", catalogCardId: c1 as string, data: {image: "s6.jpg"}},
        {id: "gc7", catalogCardId: c1 as string, data: {image: "s7.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    const johnDeckId = (await john.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: "jgc1", catalogCardId: c1 as string, data: {image: "j1.jpg"}},
        {id: "jgc2", catalogCardId: c1 as string, data: {image: "j2.jpg"}},
        {id: "jgc3", catalogCardId: c1 as string, data: {image: "j3.jpg"}},
        {id: "jgc4", catalogCardId: c1 as string, data: {image: "j4.jpg"}},
        {id: "jgc5", catalogCardId: c1 as string, data: {image: "j5.jpg"}},
        {id: "jgc6", catalogCardId: c1 as string, data: {image: "j6.jpg"}},
        {id: "jgc7", catalogCardId: c1 as string, data: {image: "j7.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    await sophie.run(ctx => ctx.db.patch(matchId, {
      playerGameDecks: [
        { playerId: SOPHIE_IDENTITY.subject, gameDeckId: sophieDeckId },
        { playerId: JOHN_IDENTITY.subject, gameDeckId: johnDeckId },
      ]
    }));

    // Act
    await sophie.mutation(api.mutations.playCard.endpoint, {
      matchId,
      cardId: "gc1",
      rowType: "first",
      slotIndex: 0,
    });

    // Second call with the same params should be idempotent
    const secondCall = sophie.mutation(api.mutations.playCard.endpoint, {
      matchId,
      cardId: "gc1",
      rowType: "first",
      slotIndex: 0,
    });

    // Assert
    await secondCall;

    const board = await sophie.query(api.queries.board.getBoardState, {matchId});
    const p1FirstRow = board?.boardState?.player1Board.firstRow ?? [];
    const placed = p1FirstRow.filter(c => c !== null);
    expect(placed).toHaveLength(1);
    expect(p1FirstRow[0]?.cardId).toBe('gc1');

    const hands = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});
    expect(hands).toBeTruthy();
    expect(hands!.player1).toHaveLength(6);
    expect(hands!.player1.map(c => c.instanceId)).toEqual([
      'gc2','gc3','gc4','gc5','gc6','gc7'
    ]);
  });
});
