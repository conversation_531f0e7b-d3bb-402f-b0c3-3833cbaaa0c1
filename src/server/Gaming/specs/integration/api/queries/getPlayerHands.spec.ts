import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe('When getting player hands', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it('should return player hands for the current user', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const c1 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 1', image: '1.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    const c2 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 2', image: '2.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'setup',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'gc1', catalogCardId: c1 as string, data: {image: 'sophie-card1.jpg'}},
        {id: 'gc2', catalogCardId: c2 as string, data: {image: 'sophie-card2.jpg'}},
      ],
    }));

    await john.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: 'jgc1', catalogCardId: c1 as string, data: {image: 'john-card1.jpg'}},
        {id: 'jgc2', catalogCardId: c1 as string, data: {image: 'john-card2.jpg'}},
        {id: 'jgc3', catalogCardId: c1 as string, data: {image: 'john-card3.jpg'}},
        {id: 'jgc4', catalogCardId: c1 as string, data: {image: 'john-card4.jpg'}},
        {id: 'jgc5', catalogCardId: c1 as string, data: {image: 'john-card5.jpg'}},
        {id: 'jgc6', catalogCardId: c1 as string, data: {image: 'john-card6.jpg'}},
        {id: 'jgc7', catalogCardId: c1 as string, data: {image: 'john-card7.jpg'}},
        {id: 'jgc8', catalogCardId: c1 as string, data: {image: 'john-card8.jpg'}},
      ],
    }));

    // Act
    const result = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(result!.player1).toEqual([
      { instanceId: 'gc1', catalogCardId: c1, image: 'sophie-card1.jpg' },
      { instanceId: 'gc2', catalogCardId: c2, image: 'sophie-card2.jpg' }
    ]);
    expect(result!.player2).toEqual([]);
    expect(result!.player2Count).toBe(7);
  });

  it('should return opponent hand count but not cards when user is player2', async () => {
    // Arrange
    const gameId = await john.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const c1 = await john.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 1', image: '1.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await john.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await john.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'setup',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'gc1', catalogCardId: c1 as string, data: {image: 'test-card1.jpg'}},
        {id: 'gc2', catalogCardId: c1 as string, data: {image: 'test-card2.jpg'}},
        {id: 'gc3', catalogCardId: c1 as string, data: {image: 'test-card3.jpg'}},
        {id: 'gc4', catalogCardId: c1 as string, data: {image: 'test-card4.jpg'}},
        {id: 'gc5', catalogCardId: c1 as string, data: {image: 'test-card5.jpg'}},
        {id: 'gc6', catalogCardId: c1 as string, data: {image: 'test-card6.jpg'}},
        {id: 'gc7', catalogCardId: c1 as string, data: {image: 'test-card7.jpg'}},
        {id: 'gc8', catalogCardId: c1 as string, data: {image: 'test-card8.jpg'}},
      ],
    }));

    await john.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: 'jgc1', catalogCardId: c1 as string, data: {image: 'john-card1.jpg'}},
        {id: 'jgc2', catalogCardId: c1 as string, data: {image: 'john-card2.jpg'}},
        {id: 'jgc3', catalogCardId: c1 as string, data: {image: 'john-card3.jpg'}},
        {id: 'jgc4', catalogCardId: c1 as string, data: {image: 'john-card4.jpg'}},
        {id: 'jgc5', catalogCardId: c1 as string, data: {image: 'john-card5.jpg'}},
        {id: 'jgc6', catalogCardId: c1 as string, data: {image: 'john-card6.jpg'}},
        {id: 'jgc7', catalogCardId: c1 as string, data: {image: 'john-card7.jpg'}},
        {id: 'jgc8', catalogCardId: c1 as string, data: {image: 'john-card8.jpg'}},
      ],
    }));

    // Act
    const result = await john.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(result!.player1).toEqual([]);
    expect(result!.player2).toHaveLength(7);
    expect(result!.player2[0]).toEqual(
      { instanceId: 'jgc1', catalogCardId: c1, image: 'john-card1.jpg' }
    );
    expect(result!.player2Count).toBe(7);
  });

  it('should throw error when match does not exist', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const fakeMatchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'setup',
      createdAt: Date.now(),
    })) as Id<'matches'>;
    
    await sophie.run(ctx => ctx.db.delete(fakeMatchId));

    // Act
    const queryPromise = sophie.query(api.queries.gaming.getPlayerHands, {matchId: fakeMatchId});

    // Assert
    await expect(queryPromise).rejects.toThrow('Match not found');
  });
});