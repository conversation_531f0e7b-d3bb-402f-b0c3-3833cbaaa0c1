import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe('When ensuring symmetric hand counts', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it('should return player1Count and player2Count for both players', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const c1 = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Card 1', image: '1.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'setup',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 's1', catalogCardId: c1 as string, data: {image: 's1.jpg'}},
        {id: 's2', catalogCardId: c1 as string, data: {image: 's2.jpg'}},
        {id: 's3', catalogCardId: c1 as string, data: {image: 's3.jpg'}},
        {id: 's4', catalogCardId: c1 as string, data: {image: 's4.jpg'}},
        {id: 's5', catalogCardId: c1 as string, data: {image: 's5.jpg'}},
        {id: 's6', catalogCardId: c1 as string, data: {image: 's6.jpg'}},
        {id: 's7', catalogCardId: c1 as string, data: {image: 's7.jpg'}},
      ],
    }));

    await john.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: 'j1', catalogCardId: c1 as string, data: {image: 'j1.jpg'}},
        {id: 'j2', catalogCardId: c1 as string, data: {image: 'j2.jpg'}},
        {id: 'j3', catalogCardId: c1 as string, data: {image: 'j3.jpg'}},
        {id: 'j4', catalogCardId: c1 as string, data: {image: 'j4.jpg'}},
        {id: 'j5', catalogCardId: c1 as string, data: {image: 'j5.jpg'}},
        {id: 'j6', catalogCardId: c1 as string, data: {image: 'j6.jpg'}},
        {id: 'j7', catalogCardId: c1 as string, data: {image: 'j7.jpg'}},
      ],
    }));

    // Act
    const sophieResult = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});
    const johnResult = await john.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(sophieResult).toBeTruthy();
    expect(sophieResult!.player1Count).toBe(7);
    expect(sophieResult!.player2Count).toBe(7);
    expect(johnResult).toBeTruthy();
    expect(johnResult!.player1Count).toBe(7);
    expect(johnResult!.player2Count).toBe(7);
  });
});

