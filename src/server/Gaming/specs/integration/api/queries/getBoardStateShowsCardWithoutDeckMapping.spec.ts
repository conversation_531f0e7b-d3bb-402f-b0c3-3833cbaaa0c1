import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe("When board has placed cards but no deck mapping", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let _john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    _john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it("should include the placed card with empty image fallback", async () => {
    // Arrange
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    // No decks or playerGameDecks created on purpose

    await sophie.run(ctx => ctx.db.patch(matchId, {
      boardState: {
        player1Board: { firstRow: [ { cardId: 'gc1', placedAt: new Date().toISOString(), slotIndex: 0, rowType: 'first' }, null, null, null, null, null, null, null ], secondRow: Array(8).fill(null) },
        player2Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) }
      }
    }));

    // Act
    const result = await sophie.query(api.queries.board.getBoardState, {matchId});

    // Assert
    expect(result).toBeTruthy();
    const cell = result!.boardState!.player1Board.firstRow[0];
    expect(cell).toBeTruthy();
    expect(cell!.cardId).toBe('gc1');
    expect(cell!.slotIndex).toBe(0);
    expect(cell!.rowType).toBe('first');
    expect(cell!.image).toBe('');
  });
});
