import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe("When a card is placed on the board", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it("should remove the placed card from the current player's hand", async () => {
    // Arrange
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    const c1 = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Card 1",
      image: "1.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      currentTurn: SOPHIE_IDENTITY.subject,
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: c1 as string, data: {image: "s1.jpg"}},
        {id: "gc2", catalogCardId: c1 as string, data: {image: "s2.jpg"}},
        {id: "gc3", catalogCardId: c1 as string, data: {image: "s3.jpg"}},
        {id: "gc4", catalogCardId: c1 as string, data: {image: "s4.jpg"}},
        {id: "gc5", catalogCardId: c1 as string, data: {image: "s5.jpg"}},
        {id: "gc6", catalogCardId: c1 as string, data: {image: "s6.jpg"}},
        {id: "gc7", catalogCardId: c1 as string, data: {image: "s7.jpg"}},
        {id: "gc8", catalogCardId: c1 as string, data: {image: "s8.jpg"}},
      ],
    }));

    await john.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: "jgc1", catalogCardId: c1 as string, data: {image: "j1.jpg"}},
        {id: "jgc2", catalogCardId: c1 as string, data: {image: "j2.jpg"}},
        {id: "jgc3", catalogCardId: c1 as string, data: {image: "j3.jpg"}},
        {id: "jgc4", catalogCardId: c1 as string, data: {image: "j4.jpg"}},
        {id: "jgc5", catalogCardId: c1 as string, data: {image: "j5.jpg"}},
        {id: "jgc6", catalogCardId: c1 as string, data: {image: "j6.jpg"}},
        {id: "jgc7", catalogCardId: c1 as string, data: {image: "j7.jpg"}},
        {id: "jgc8", catalogCardId: c1 as string, data: {image: "j8.jpg"}},
      ],
    }));

    // Act
    await sophie.mutation(api.mutations.playCard.endpoint, {
      matchId,
      cardId: "gc1",
      rowType: "first",
      slotIndex: 0,
    });

    // Assert
    const afterSophiePlays = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});
    expect(afterSophiePlays).toBeTruthy();
    expect(afterSophiePlays!.player1).toHaveLength(6);
    expect(afterSophiePlays!.player1.map(c => c.instanceId)).toEqual([
      'gc2','gc3','gc4','gc5','gc6','gc7'
    ]);
    expect(afterSophiePlays!.player2).toEqual([]);
    expect(afterSophiePlays!.player2Count).toBe(7);
  });

  it("should update the opponent visible card count after placement", async () => {
    // Arrange
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    const c1 = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Card 1",
      image: "1.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      currentTurn: SOPHIE_IDENTITY.subject,
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: c1 as string, data: {image: "s1.jpg"}},
        {id: "gc2", catalogCardId: c1 as string, data: {image: "s2.jpg"}},
        {id: "gc3", catalogCardId: c1 as string, data: {image: "s3.jpg"}},
        {id: "gc4", catalogCardId: c1 as string, data: {image: "s4.jpg"}},
        {id: "gc5", catalogCardId: c1 as string, data: {image: "s5.jpg"}},
        {id: "gc6", catalogCardId: c1 as string, data: {image: "s6.jpg"}},
        {id: "gc7", catalogCardId: c1 as string, data: {image: "s7.jpg"}},
      ],
    }));

    await john.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: "jgc1", catalogCardId: c1 as string, data: {image: "j1.jpg"}},
        {id: "jgc2", catalogCardId: c1 as string, data: {image: "j2.jpg"}},
        {id: "jgc3", catalogCardId: c1 as string, data: {image: "j3.jpg"}},
        {id: "jgc4", catalogCardId: c1 as string, data: {image: "j4.jpg"}},
        {id: "jgc5", catalogCardId: c1 as string, data: {image: "j5.jpg"}},
        {id: "jgc6", catalogCardId: c1 as string, data: {image: "j6.jpg"}},
        {id: "jgc7", catalogCardId: c1 as string, data: {image: "j7.jpg"}},
      ],
    }));

    // Act
    await sophie.mutation(api.mutations.playCard.endpoint, {
      matchId,
      cardId: "gc1",
      rowType: "first",
      slotIndex: 0,
    });

    // Assert
    const johnView = await john.query(api.queries.gaming.getPlayerHands, {matchId});
    expect(johnView!.player1Count).toBe(6);
    expect(johnView!.player2).toHaveLength(7);
  });
});
