import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe("When computing player hands with match-referenced decks", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it("should use the deck referenced by the match over the latest deck", async () => {
    // Arrange
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    const cA = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Alpha",
      image: "alpha.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    const cB = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Beta",
      image: "beta.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 1
    }));

    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      currentTurn: SOPHIE_IDENTITY.subject,
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    // Older deck (should be used by match): gc1 -> cA with image 'oldA.jpg'
    const oldDeckId = (await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: cA as string, data: {image: "oldA.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    // Newer deck (should NOT be used): gc1 -> cB with image 'newB.jpg'
    await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: cB as string, data: {image: "newB.jpg"}},
      ],
    }));

    // John deck (irrelevant)
    const johnDeckId = (await john.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: "j1", catalogCardId: cB as string, data: {image: "j-beta.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    // Reference the older deck on the match
    await sophie.run(ctx => ctx.db.patch(matchId, {
      playerGameDecks: [
        { playerId: SOPHIE_IDENTITY.subject, gameDeckId: oldDeckId },
        { playerId: JOHN_IDENTITY.subject, gameDeckId: johnDeckId },
      ]
    }));

    // Act
    const hands = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(hands).toBeTruthy();
    expect(hands!.player1).toHaveLength(1);
    expect(hands!.player1[0]!.instanceId).toBe('gc1');
    expect(hands!.player1[0]!.catalogCardId).toBe(cA as string);
    expect(hands!.player1[0]!.image).toBe('oldA.jpg');
  });
});

