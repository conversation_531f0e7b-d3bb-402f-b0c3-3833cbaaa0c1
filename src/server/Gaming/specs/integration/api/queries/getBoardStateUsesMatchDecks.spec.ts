import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe("When resolving board state images", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it("should use decks referenced by the match to resolve placed card images", async () => {
    // Arrange
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    const cA = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Alpha",
      image: "alpha.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    const cB = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Beta",
      image: "beta.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    // Deck used by match for Sophie: gc1 -> cA with image 's-alpha.jpg'
    const sophieDeckId = (await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: cA as string, data: {image: "s-alpha.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    // Conflicting deck (not referenced by match): also contains gc1 -> cB with image 'conflict.jpg'
    await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: cB as string, data: {image: "conflict.jpg"}},
      ],
    }));

    const johnDeckId = (await john.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: "j1", catalogCardId: cB as string, data: {image: "j-beta.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    // Patch match to set the playerGameDecks reference
    await sophie.run(ctx => ctx.db.patch(matchId, {
      playerGameDecks: [
        { playerId: SOPHIE_IDENTITY.subject, gameDeckId: sophieDeckId },
        { playerId: JOHN_IDENTITY.subject, gameDeckId: johnDeckId },
      ]
    }));

    // Put gc1 on player1 first row
    await sophie.run(ctx => ctx.db.patch(matchId, {
      boardState: {
        player1Board: { firstRow: [ { cardId: 'gc1', placedAt: new Date().toISOString(), slotIndex: 0, rowType: 'first' }, null, null, null, null, null, null, null ], secondRow: Array(8).fill(null) },
        player2Board: { firstRow: Array(8).fill(null), secondRow: Array(8).fill(null) }
      }
    }));

    // Act
    const result = await sophie.query(api.queries.board.getBoardState, {matchId});

    // Assert
    const image = result?.boardState?.player1Board.firstRow[0]?.image;
    expect(image).toBe("s-alpha.jpg");
  });
});

