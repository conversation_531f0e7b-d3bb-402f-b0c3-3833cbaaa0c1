import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe("When resolving board state with identical instance ids per player", () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;
  let john: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);
    john = testConvex.withIdentity(JOHN_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it("should resolve images per player, even if card instance ids collide", async () => {
    // Arrange
    // Create game
    const gameId = (await sophie.run(ctx => ctx.db.insert("games", {
      name: "Test Game",
      ownerId: "owner-123",
    }))) as Id<'games'>;

    // Two catalog cards
    const cA = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Alpha",
      image: "alpha.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    const cB = (await sophie.run(ctx => ctx.db.insert("catalogCards", {
      gameId,
      name: "Beta",
      image: "beta.jpg",
      language: "en",
      minDeckQuantity: 0,
      maxDeckQuantity: 4,
      data: {}
    }))) as Id<'catalogCards'>;

    // Create active match
    const matchId = (await sophie.run(ctx => ctx.db.insert("matches", {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: "active",
      createdAt: Date.now(),
    }))) as Id<'matches'>;

    // Create per-player game decks, both using the same instance id "gc1" but different catalog and image
    const sophieDeckId = (await sophie.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: cA as string, data: {image: "p1-alpha.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    const johnDeckId = (await john.run(ctx => ctx.db.insert("gameDecks", {
      gameId,
      playerId: JOHN_IDENTITY.subject,
      cards: [
        {id: "gc1", catalogCardId: cB as string, data: {image: "p2-beta.jpg"}},
      ],
    }))) as Id<'gameDecks'>;

    // Reference the decks on the match
    await sophie.run(ctx => ctx.db.patch(matchId, {
      playerGameDecks: [
        { playerId: SOPHIE_IDENTITY.subject, gameDeckId: sophieDeckId },
        { playerId: JOHN_IDENTITY.subject, gameDeckId: johnDeckId },
      ]
    }));

    // Place the same instance id on both player rows
    await sophie.run(ctx => ctx.db.patch(matchId, {
      boardState: {
        player1Board: {
          firstRow: [ { cardId: 'gc1', placedAt: new Date().toISOString(), slotIndex: 0, rowType: 'first' }, null, null, null, null, null, null, null ],
          secondRow: Array(8).fill(null)
        },
        player2Board: {
          firstRow: [ { cardId: 'gc1', placedAt: new Date().toISOString(), slotIndex: 0, rowType: 'first' }, null, null, null, null, null, null, null ],
          secondRow: Array(8).fill(null)
        }
      }
    }));

    // Act
    const result = await sophie.query(api.queries.board.getBoardState, {matchId});

    // Assert
    const p1Image = result?.boardState?.player1Board.firstRow[0]?.image;
    const p2Image = result?.boardState?.player2Board.firstRow[0]?.image;

    expect(p1Image).toBe("p1-alpha.jpg");
    expect(p2Image).toBe("p2-beta.jpg");
  });
});

