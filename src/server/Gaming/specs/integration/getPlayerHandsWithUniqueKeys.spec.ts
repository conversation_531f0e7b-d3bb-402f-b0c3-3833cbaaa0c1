import schema from "@/convex/schema";
import {convexTest, TestConvexForDataModel} from "convex-test";
import {DataModel, Id} from "@/convex/_generated/dataModel";
import {api} from "@/convex/_generated/api";
import {
  ADMIN_IDENTITY,
  JOHN_APP_USER,
  JOHN_IDENTITY,
  SOPHIE_APP_USER,
  SOPHIE_IDENTITY
} from "@/src/server/Authentication/specs/helpers/fakes/fakeUsers";
import {createAppUser} from "@/src/server/Authentication/specs/helpers/createAppUsers";

describe('When getting player hands with duplicate card types', () => {
  let asAdmin: TestConvexForDataModel<DataModel>;
  let sophie: TestConvexForDataModel<DataModel>;

  beforeEach(() => {
    const testConvex = convexTest(schema);
    asAdmin = testConvex.withIdentity(ADMIN_IDENTITY);
    sophie = testConvex.withIdentity(SOPHIE_IDENTITY);

    createAppUser(asAdmin, SOPHIE_APP_USER);
    createAppUser(asAdmin, JOHN_APP_USER);
  });

  it('should return unique instance IDs even for duplicate card types', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const fireball = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Fireball', image: 'fireball.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'playing',
      gamePhase: 'playing',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    // Create deck with 3 instances of the same card type (Fireball)
    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'instance-1', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'instance-2', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}}, 
        {id: 'instance-3', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'instance-4', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'instance-5', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'instance-6', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'instance-7', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
      ],
    }));

    // Act
    const result = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(result).toBeTruthy();
    expect(result!.player1).toHaveLength(7);
    
    // Extract all instanceIds
    const instanceIds = result!.player1.map((card: {instanceId: string}) => card.instanceId);
    const uniqueInstanceIds = new Set(instanceIds);
    
    // All instanceIds should be unique
    expect(uniqueInstanceIds.size).toBe(7);
    expect(instanceIds).toEqual([
      'instance-1',
      'instance-2', 
      'instance-3',
      'instance-4',
      'instance-5',
      'instance-6',
      'instance-7'
    ]);

    // All cards should have the same catalogCardId (same card type)
    const catalogCardIds = result!.player1.map((card: {catalogCardId: string}) => card.catalogCardId);
    expect(catalogCardIds.every((id: string) => id === fireball)).toBe(true);
    
    // All cards should have the same image
    const images = result!.player1.map((card: {image: string}) => card.image);
    expect(images.every((img: string) => img === 'fireball.jpg')).toBe(true);
  });

  it('should maintain unique keys after mulligan with duplicate card types', async () => {
    // Arrange
    const gameId = await sophie.run(ctx => ctx.db.insert('games', {
      name: 'Test Game',
      ownerId: 'owner-123'
    })) as Id<'games'>;

    const fireball = await sophie.run(ctx => ctx.db.insert('catalogCards', {
      gameId,
      name: 'Fireball', image: 'fireball.jpg', language: 'en', minDeckQuantity: 0, maxDeckQuantity: 4, data: {}
    })) as Id<'catalogCards'>;

    await sophie.run(ctx => ctx.db.insert('gameSettings', {
      gameId,
      maxCardsInDeck: 40,
      mulliganCount: 1,
      startingHandSize: 7
    }));

    const matchId = await sophie.run(ctx => ctx.db.insert('matches', {
      gameId,
      players: [SOPHIE_IDENTITY.subject, JOHN_IDENTITY.subject],
      status: 'playing',
      gamePhase: 'playing',
      createdAt: Date.now(),
    })) as Id<'matches'>;

    await sophie.run(ctx => ctx.db.insert('gameDecks', {
      gameId,
      playerId: SOPHIE_IDENTITY.subject,
      cards: [
        {id: 'fb1', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb2', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb3', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb4', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb5', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb6', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb7', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb8', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb9', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
        {id: 'fb10', catalogCardId: fireball as string, data: {image: 'fireball.jpg'}},
      ],
    }));

    // Mulligan: remove 3 cards from the initial hand (fb5, fb6, fb7)  
    await sophie.run(ctx => ctx.db.insert('mulliganSelections', {
      matchId,
      playerId: SOPHIE_IDENTITY.subject,
      round: 1,
      selectedCardIds: ['fb5', 'fb6', 'fb7'],
      skipped: false
    }));

    // Act
    const result = await sophie.query(api.queries.gaming.getPlayerHands, {matchId});

    // Assert
    expect(result).toBeTruthy();
    expect(result!.player1).toHaveLength(4);
    
    const instanceIds = result!.player1.map((card: {instanceId: string}) => card.instanceId);
    const uniqueInstanceIds = new Set(instanceIds);
    
    expect(uniqueInstanceIds.size).toBe(4);
    expect(instanceIds).toEqual(['fb1', 'fb2', 'fb3', 'fb4']);
  });
});