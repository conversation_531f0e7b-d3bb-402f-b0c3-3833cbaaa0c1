interface PlacedCardProps {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: 'first' | 'second';
}

export class PlacedCard {
  private props: PlacedCardProps;

  constructor(props: PlacedCardProps) {
    this.props = props;
  }

  static create(props: PlacedCardProps): PlacedCard {
    return new PlacedCard(props);
  }

  getCardId(): string {
    return this.props.cardId;
  }

  getPlacedAt(): string {
    return this.props.placedAt;
  }

  getSlotIndex(): number {
    return this.props.slotIndex;
  }

  getRowType(): 'first' | 'second' {
    return this.props.rowType;
  }

  toSnapshot(): PlacedCardProps {
    return {
      cardId: this.props.cardId,
      placedAt: this.props.placedAt,
      slotIndex: this.props.slotIndex,
      rowType: this.props.rowType,
    };
  }
}