import {PlayerBoard} from './PlayerBoard';
import {PlacedCard} from './PlacedCard';

interface BoardProps {
  player1Board: PlayerBoard;
  player2Board: PlayerBoard;
}

interface PlacedCardSnapshot {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: 'first' | 'second';
}

interface BoardSnapshot {
  player1Board: {
    firstRow: (PlacedCardSnapshot | null)[];
    secondRow: (PlacedCardSnapshot | null)[];
  };
  player2Board: {
    firstRow: (PlacedCardSnapshot | null)[];
    secondRow: (PlacedCardSnapshot | null)[];
  };
}

export class Board {
  private props: BoardProps;

  constructor(props: BoardProps) {
    this.props = props;
  }

  static createEmpty(): Board {
    return new Board({
      player1Board: PlayerBoard.createEmpty(),
      player2Board: PlayerBoard.createEmpty(),
    });
  }

  static create(props: BoardProps): Board {
    return new Board(props);
  }

  getPlayer1Board(): PlayerBoard {
    return this.props.player1Board;
  }

  getPlayer2Board(): PlayerBoard {
    return this.props.player2Board;
  }

  getPlayerBoard(playerIndex: number): PlayerBoard {
    return playerIndex === 0 ? this.props.player1Board : this.props.player2Board;
  }

  hasPlacedCards(): boolean {
    return this.props.player1Board.hasPlacedCards() || 
           this.props.player2Board.hasPlacedCards();
  }

  getAllCardIds(): Set<string> {
    const cardIds = new Set<string>();
    this.props.player1Board.getAllCardIds().forEach(id => cardIds.add(id));
    this.props.player2Board.getAllCardIds().forEach(id => cardIds.add(id));
    return cardIds;
  }

  placeCard(playerIndex: number, cardId: string, rowType: 'first' | 'second', slotIndex: number): void {
    const placedCard = PlacedCard.create({
      cardId,
      placedAt: new Date().toISOString(),
      slotIndex,
      rowType
    });

    const playerBoard = this.getPlayerBoard(playerIndex);
    playerBoard.placeCardInSlot(placedCard, rowType, slotIndex);
  }

  toSnapshot(): BoardSnapshot {
    return {
      player1Board: this.props.player1Board.toSnapshot(),
      player2Board: this.props.player2Board.toSnapshot(),
    };
  }
}