import {PlacedCard} from './PlacedCard';

interface PlayerBoardProps {
  firstRow: (PlacedCard | null)[];
  secondRow: (PlacedCard | null)[];
}

export class PlayerBoard {
  private props: PlayerBoardProps;

  constructor(props: PlayerBoardProps) {
    this.props = props;
  }

  static createEmpty(): PlayerBoard {
    return new PlayerBoard({
      firstRow: Array(8).fill(null),
      secondRow: Array(8).fill(null),
    });
  }

  static create(props: PlayerBoardProps): PlayerBoard {
    return new PlayerBoard(props);
  }

  getFirstRow(): (PlacedCard | null)[] {
    return [...this.props.firstRow];
  }

  getSecondRow(): (PlacedCard | null)[] {
    return [...this.props.secondRow];
  }

  placeCardInSlot(card: PlacedCard, rowType: 'first' | 'second', slotIndex: number): void {
    const row = rowType === 'first' ? this.props.firstRow : this.props.secondRow;
    
    if (slotIndex < 0 || slotIndex >= row.length) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }
    
    if (row[slotIndex] !== null) {
      throw new Error(`Slot ${slotIndex} is already occupied`);
    }
    
    row[slotIndex] = card;
  }

  hasPlacedCards(): boolean {
    return [
      ...this.props.firstRow,
      ...this.props.secondRow,
    ].some(card => card !== null);
  }

  getAllCardIds(): Set<string> {
    const cardIds = new Set<string>();
    [...this.props.firstRow, ...this.props.secondRow].forEach(card => {
      if (card) {
        cardIds.add(card.getCardId());
      }
    });
    return cardIds;
  }

  toSnapshot() {
    return {
      firstRow: this.props.firstRow?.map(card => card?.toSnapshot() ?? null) ?? [],
      secondRow: this.props.secondRow?.map(card => card?.toSnapshot() ?? null) ?? [],
    };
  }
}