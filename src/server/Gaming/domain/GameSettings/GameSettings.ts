export type GameSettingsSnapshot = { 
  gameId: string;
  maxCardsInDeck: number;
  mulliganCount: number;
  startingHandSize: number;
};

export class GameSettings {
  private readonly props: GameSettingsSnapshot;

  constructor(props: GameSettingsSnapshot) {
    this.props = props;
  }

  static fromSnapshot(snapshot: GameSettingsSnapshot): GameSettings {
    return new GameSettings({...snapshot});
  }

  toSnapshot(): GameSettingsSnapshot {
    return {...this.props};
  }

  getMulliganCount(): number {
    return this.props.mulliganCount;
  }

  isMulliganEnabled(): boolean {
    return this.props.mulliganCount > 0;
  }

  getStartingHandSize(): number {
    return this.props.startingHandSize;
  }
}
