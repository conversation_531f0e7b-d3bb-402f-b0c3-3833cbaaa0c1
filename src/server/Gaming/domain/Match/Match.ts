import {PlayerRole, LegacyPlayerId, convertRoleToLegacy} from '@/src/server/Shared/domain/PlayerRole/PlayerRole';

interface PlacedCard {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: string;
}

interface BoardState {
  player1Board: {
    firstRow: (PlacedCard | null)[];
    secondRow: (PlacedCard | null)[];
  };
  player2Board: {
    firstRow: (PlacedCard | null)[];
    secondRow: (PlacedCard | null)[];
  };
}

export interface MatchProps {
  id?: string;
  gameId: string;
  players: string[];
  status: string;
  winner?: string;
  currentMulliganRound?: number;
  boardState?: BoardState;
  currentTurn?: string;
  gamePhase?: string;
}

export type MatchSnapshot = Omit<MatchProps, 'id'> & { id: string };

export class Match {
  private props: MatchProps;
  constructor(props: MatchProps) {
    this.props = props;
  }

  static create(props: MatchProps) {
    return new Match(props);
  }

  static fromSnapshot(snapshot: MatchSnapshot): Match {
    return new Match({...snapshot});
  }

  toSnapshot(): MatchSnapshot {
    return {
      id: this.props.id!,
      gameId: this.props.gameId,
      players: this.props.players,
      status: this.props.status,
      winner: this.props.winner,
      currentMulliganRound: this.props.currentMulliganRound,
      boardState: this.props.boardState,
      currentTurn: this.props.currentTurn,
      gamePhase: this.props.gamePhase,
    };
  }

  getId() { return this.props.id!; }
  getGameId() { return this.props.gameId; }
  getPlayers() { return [...this.props.players]; }
  getStatus() { return this.props.status; }
  getWinner() { return this.props.winner; }
  getCurrentMulliganRound() { return this.props.currentMulliganRound || 1; }

  isFinished() { return this.props.status === 'finished'; }

  finishMatchWithWinner(winner: string) {
    this.props.winner = winner;
    this.props.status = 'finished';
  }

  getOpponentOf(playerId: string): string {
    return this.props.players.find(p => p !== playerId)!;
  }

  startMulliganPhase(): void {
    if (this.props.status !== 'setup') {
      throw new Error(`Cannot start mulligan from ${this.props.status} state`);
    }
    this.props.status = 'waiting_for_mulligan';
    this.props.currentMulliganRound = 1;
  }

  advanceToNextMulliganRound(): void {
    if (this.props.status !== 'waiting_for_mulligan') {
      throw new Error(`Cannot advance mulligan round from ${this.props.status} state`);
    }
    this.props.currentMulliganRound = (this.props.currentMulliganRound || 1) + 1;
  }

  completeMulliganPhase(): void {
    this.props.status = 'active';
    this.initializeFirstTurn();
  }

  makeReadyForPlay(): void {
    this.props.status = 'active';
    this.initializeFirstTurn();
  }

  isWaitingForMulligan(): boolean {
    return this.props.status === 'waiting_for_mulligan';
  }

  startDirectly(): void {
    if (this.props.status !== 'setup') {
      throw new Error(`Cannot start directly from ${this.props.status} state`);
    }
    this.props.status = 'active';
    this.initializeFirstTurn();
  }

  initializeBoardState(): void {
    if (!this.props.boardState) {
      this.props.boardState = {
        player1Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        },
        player2Board: {
          firstRow: Array(8).fill(null),
          secondRow: Array(8).fill(null),
        }
      };
    }
  }

  placeCard(playerId: string, cardId: string, rowType: 'first' | 'second', slotIndex: number): void {
    this.initializeBoardState();
    
    const playerBoard = playerId === this.props.players[0] ? 'player1Board' : 'player2Board';
    const row = this.props.boardState![playerBoard][rowType === 'first' ? 'firstRow' : 'secondRow'];
    
    if (slotIndex < 0 || slotIndex >= row.length) {
      throw new Error(`Invalid slot index: ${slotIndex}`);
    }
    
    if (row[slotIndex] !== null) {
      throw new Error(`Slot ${slotIndex} is already occupied`);
    }
    
    row[slotIndex] = {
      cardId,
      placedAt: new Date().toISOString(),
      slotIndex,
      rowType
    };
  }

  getBoardState(): BoardState | undefined {
    return this.props.boardState;
  }

  isCardAlreadyPlayed(cardId: string): boolean {
    if (!this.props.boardState) {
      return false;
    }

    const { player1Board, player2Board } = this.props.boardState;

    const allRows = [
      player1Board.firstRow,
      player1Board.secondRow,
      player2Board.firstRow,
      player2Board.secondRow
    ];

    return allRows.some(row =>
      row.some(placedCard => placedCard?.cardId === cardId)
    );
  }

  isCardPlayedInPosition(cardId: string, playerId: string, rowType: 'first' | 'second', slotIndex: number): boolean {
    if (!this.props.boardState) {
      return false;
    }

    const playerBoard = playerId === this.props.players[0] ? 'player1Board' : 'player2Board';
    const row = this.props.boardState[playerBoard][rowType === 'first' ? 'firstRow' : 'secondRow'];

    if (slotIndex < 0 || slotIndex >= row.length) {
      return false;
    }

    const placedCard = row[slotIndex];
    return placedCard?.cardId === cardId;
  }

  getCurrentTurn(): string | undefined {
    return this.props.currentTurn;
  }

  setCurrentTurn(playerId: string): void {
    this.props.currentTurn = playerId;
  }

  getGamePhase(): string | undefined {
    return this.props.gamePhase;
  }

  setGamePhase(phase: string): void {
    this.props.gamePhase = phase;
  }

  placeCardByRole(
    role: PlayerRole, 
    currentPlayerPosition: LegacyPlayerId, 
    cardId: string, 
    rowType: 'first' | 'second', 
    slotIndex: number
  ): void {
    const targetPlayerId = this.getPlayerIdByRole(role, currentPlayerPosition);
    this.placeCard(targetPlayerId, cardId, rowType, slotIndex);
  }

  getCurrentTurnByRole(currentPlayerPosition: LegacyPlayerId): PlayerRole | undefined {
    const currentTurn = this.getCurrentTurn();
    if (!currentTurn) return undefined;
    
    return currentTurn === currentPlayerPosition ? 'currentPlayer' : 'opponent';
  }

  setCurrentTurnByRole(role: PlayerRole, currentPlayerPosition: LegacyPlayerId): void {
    const targetPlayerId = this.getPlayerIdByRole(role, currentPlayerPosition);
    this.setCurrentTurn(targetPlayerId);
  }

  getPlayerIdByRole(role: PlayerRole, currentPlayerPosition: LegacyPlayerId): string {
    const legacyPlayerId = convertRoleToLegacy(role, currentPlayerPosition);
    const playerIndex = legacyPlayerId === 'player1' ? 0 : 1;
    return this.props.players[playerIndex];
  }

  getOpponentByRole(currentPlayerPosition: LegacyPlayerId): string {
    const opponentLegacyId = currentPlayerPosition === 'player1' ? 'player2' : 'player1';
    const playerIndex = opponentLegacyId === 'player1' ? 0 : 1;
    return this.props.players[playerIndex];
  }

  passTurn(playerId: string): void {
    if (this.props.status !== 'active') {
      throw new Error('Cannot pass turn when match is not active');
    }

    if (!this.props.currentTurn) {
      throw new Error('No current turn is set');
    }

    if (!this.props.players.includes(playerId)) {
      throw new Error(`Player ${playerId} is not part of this match`);
    }

    if (this.props.currentTurn !== playerId) {
      throw new Error('Only the current turn player can pass the turn');
    }

    const opponentId = this.getOpponentOf(playerId);
    this.props.currentTurn = opponentId;
  }

  private initializeFirstTurn(): void {
    if (!this.props.currentTurn && this.props.players.length >= 2) {
      this.props.currentTurn = this.props.players[0];
    }
  }
}
