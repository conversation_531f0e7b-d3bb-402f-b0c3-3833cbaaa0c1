import { GetMatchPlayersQuery } from './GetMatchPlayersQuery';
import { GetMatchPlayersPresenter } from '../../ports/GetMatchPlayersPresenter';
import { MatchReadRepository } from '../../ports/MatchReadRepository';
import { AppUserRepository } from '@/src/server/Authentication/application/ports/AppUserRepository';

export class GetMatchPlayersQueryHandler {
  private readonly matchRepository: MatchReadRepository;
  private readonly userRepository: AppUserRepository;

  constructor(matchRepository: MatchReadRepository, userRepository: AppUserRepository) {
    this.matchRepository = matchRepository;
    this.userRepository = userRepository;
  }

  async handle({ matchId, userId }: GetMatchPlayersQuery, presenter: GetMatchPlayersPresenter): Promise<void> {
    try {
      const match = await this.matchRepository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      const players = match.getPlayers();
      if (!players.includes(userId)) {
        presenter.displayError(new Error('User not in match'));
        return;
      }

      const opponentId = players.find(playerId => playerId !== userId);
      if (!opponentId) {
        presenter.displayError(new Error('Opponent not found'));
        return;
      }

      const currentUser = await this.userRepository.findById(userId);
      const opponentUser = await this.userRepository.findById(opponentId);

      if (!currentUser || !opponentUser) {
        presenter.displayError(new Error('Player data not found'));
        return;
      }

      const currentSnapshot = currentUser.toSnapshot();
      const opponentSnapshot = opponentUser.toSnapshot();

      presenter.display(
        { 
          id: currentSnapshot.id, 
          name: currentSnapshot.name, 
          avatar: currentSnapshot.image 
        },
        { 
          id: opponentSnapshot.id, 
          name: opponentSnapshot.name, 
          avatar: opponentSnapshot.image 
        }
      );
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}