import {LoadMulliganDataQuery} from './LoadMulliganDataQuery';
import {MatchReadRepository} from '@/src/server/Gaming/application/ports/MatchReadRepository';
import {GameDeckReadRepository} from '@/src/server/Gaming/application/ports/GameDeckReadRepository';
import {LoadMulliganDataPresenter} from '@/src/server/Gaming/application/ports/LoadMulliganDataPresenter';
import {CatalogCardListRepository} from '@/src/server/DeckBuilding/application/ports/CatalogCardListRepository';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';

export class LoadMulliganDataQueryHandler {
  private readonly matchRepository: MatchReadRepository;
  private readonly deckRepository: GameDeckReadRepository;
  private readonly catalogRepository: CatalogCardListRepository;
  private readonly gameSettingsRepository: GameSettingsRepository;
  private readonly mulliganRepository: MulliganSelectionRepository;

  constructor(
    matchRepository: MatchReadRepository,
    deckRepository: GameDeckReadRepository,
    catalogRepository: CatalogCardListRepository,
    gameSettingsRepository: GameSettingsRepository,
    mulliganRepository: MulliganSelectionRepository
  ) {
    this.matchRepository = matchRepository;
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
    this.gameSettingsRepository = gameSettingsRepository;
    this.mulliganRepository = mulliganRepository;
  }

  async handle(query: LoadMulliganDataQuery, presenter: LoadMulliganDataPresenter): Promise<void> {
    try {
      const match = await this.matchRepository.findById(query.matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      if (!match.isWaitingForMulligan()) {
        const matchStatus = match.getStatus();
        if (matchStatus === 'active' || matchStatus === 'playing') {
          const gameSettings = await this.gameSettingsRepository.getByGameId(match.getGameId());
          if (!gameSettings) {
            presenter.displayError(new Error('Game settings not found'));
            return;
          }

          const maxRounds = gameSettings.getMulliganCount();
          presenter.display({
            currentRound: maxRounds,
            maxRounds: maxRounds,
            cardsInHand: [],
            selectedCardIds: [],
            canSkip: false,
            timeLimit: undefined,
            hasCompletedAllRounds: true,
          });
          return;
        }
        presenter.displayError(new Error('Match is not in mulligan phase'));
        return;
      }

      const gameId = match.getGameId();
      const playerDeck = await this.deckRepository.findLatestByGameIdAndPlayerId(gameId, query.userId);

      if (!playerDeck) {
        presenter.displayError(new Error('Player deck not found'));
        return;
      }

      const deckSnapshot = playerDeck.toSnapshot();
      const handCards = deckSnapshot.cards.slice(0, 7);
      let cardUrls: string[] = [];
      let catalogLookup = null as null | { findById: (id: string) => {name: string; image: string} | undefined };
      try {
        const catalogSubset = await this.catalogRepository.getByIds(gameId, handCards.map(c => c.catalogCardId));
        const snap = catalogSubset.toSnapshot().cards;
        const map = new Map(snap.map(c => [c.id, {name: c.name, image: c.image}] as const));
        catalogLookup = { findById: (id: string) => map.get(id) };
        cardUrls = handCards.map(card => {
          const cc = map.get(card.catalogCardId);
          return cc ? `/game-assets/cards/en/thumbnail/${cc.image}` : '';
        });
      } catch {
        const catalog = await this.catalogRepository.getByGameId(gameId);
        catalogLookup = { findById: (id: string) => {
          const c = catalog.findCardById(id);
          return c ? {name: c.name, image: c.image} : undefined;
        }};
        cardUrls = handCards.map(card => {
          const c = catalog.findCardById(card.catalogCardId);
          return c ? `/game-assets/cards/en/thumbnail/${c.image}` : '';
        });
      }

      const gameSettings = await this.gameSettingsRepository.getByGameId(match.getGameId());
      if (!gameSettings) {
        presenter.displayError(new Error('Game settings not found'));
        return;
      }

      const previousSelections = await this.mulliganRepository.findAllByMatchIdAndPlayerId(query.matchId, query.userId);
      const playerCurrentRound = previousSelections.length + 1;
      const maxRounds = gameSettings.getMulliganCount();
      const hasCompletedAllRounds = playerCurrentRound > maxRounds;


      presenter.display({
        currentRound: hasCompletedAllRounds ? maxRounds : playerCurrentRound,
        maxRounds: maxRounds,
        cardsInHand: handCards.map((card, index) => {
          const catalogCard = catalogLookup?.findById(card.catalogCardId);
          return {
            id: card.id,
            catalogCardId: card.catalogCardId,
            name: catalogCard ? catalogCard.name : `Card ${index + 1}`,
            imageUrl: cardUrls[index] || '/placeholder-card.png'
          };
        }),
        selectedCardIds: [],
        canSkip: true,
        timeLimit: undefined,
        hasCompletedAllRounds,
      });
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
