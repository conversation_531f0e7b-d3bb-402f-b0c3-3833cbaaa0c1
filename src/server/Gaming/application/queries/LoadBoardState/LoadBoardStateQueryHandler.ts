import {LoadBoardStateQuery} from "./LoadBoardStateQuery";
import {BoardReadRepository} from "@/src/server/Gaming/application/ports/BoardReadRepository";
import {LoadBoardStatePresenter} from "@/src/server/Gaming/application/ports/LoadBoardStatePresenter";
import {MatchReadRepository} from "@/src/server/Gaming/application/ports/MatchReadRepository";
import {BoardEnhancementService} from "@/src/server/Gaming/application/ports/BoardEnhancementService";

export class LoadBoardStateQueryHandler {
  private readonly boardRepository: BoardReadRepository;
  private readonly matchRepository: MatchReadRepository;
  private readonly enhancementService: BoardEnhancementService;

  constructor(
    boardRepository: BoardReadRepository,
    matchRepository: MatchReadRepository,
    enhancementService: BoardEnhancementService
  ) {
    this.boardRepository = boardRepository;
    this.matchRepository = matchRepository;
    this.enhancementService = enhancementService;
  }

  async handle({matchId}: LoadBoardStateQuery, presenter: LoadBoardStatePresenter): Promise<void> {
    try {
      const match = await this.matchRepository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      const board = await this.boardRepository.findByMatchId(matchId);
      if (!board) {
        presenter.displayError(new Error('Board not found'));
        return;
      }

      const enhancedBoard = await this.enhancementService.enhanceBoardWithImages(
        board, 
        matchId, 
        match.getPlayers()
      );

      presenter.display(enhancedBoard, match.getCurrentTurn(), match.getGamePhase());
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}