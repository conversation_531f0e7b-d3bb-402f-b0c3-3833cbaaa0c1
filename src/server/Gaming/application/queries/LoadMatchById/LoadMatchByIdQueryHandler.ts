import {LoadMatchByIdQuery} from "@/src/server/Gaming/application/queries/LoadMatchById/LoadMatchByIdQuery";
import {MatchReadRepository} from "@/src/server/Gaming/application/ports/MatchReadRepository";
import {LoadMatchByIdPresenter} from "@/src/server/Gaming/application/ports/LoadMatchByIdPresenter";
import {GameSettingsRepository} from "@/src/server/Gaming/application/ports/GameSettingsRepository";
import {MulliganSelectionRepository} from "@/src/server/Gaming/application/ports/MulliganSelectionRepository";

export class LoadMatchByIdQueryHandler {
  private readonly repository: MatchReadRepository;
  private readonly gameSettingsRepository: GameSettingsRepository;
  private readonly mulliganRepository: MulliganSelectionRepository;

  constructor(
    repository: MatchReadRepository,
    gameSettingsRepository: GameSettingsRepository,
    mulliganRepository: MulliganSelectionRepository
  ) {
    this.repository = repository;
    this.gameSettingsRepository = gameSettingsRepository;
    this.mulliganRepository = mulliganRepository;
  }

  async handle({matchId, userId}: LoadMatchByIdQuery, presenter: LoadMatchByIdPresenter) {
    try {
      const match = await this.repository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      const gameSettings = await this.gameSettingsRepository.getByGameId(match.getGameId());
      if (!gameSettings) {
        presenter.displayError(new Error('Game settings not found'));
        return;
      }

      const previousSelections = await this.mulliganRepository.findAllByMatchIdAndPlayerId(matchId, userId);
      const maxRounds = gameSettings.getMulliganCount();
      const hasCompletedAllRounds = previousSelections.length >= maxRounds;

      presenter.display(match, userId, hasCompletedAllRounds);
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
