import {LoadMatchDataQuery} from "@/src/server/Gaming/application/queries/LoadMatchData/LoadMatchDataQuery";
import {MatchReadRepository} from "@/src/server/Gaming/application/ports/MatchReadRepository";
import {GameDeckReadRepository} from "@/src/server/Gaming/application/ports/GameDeckReadRepository";
import {LoadMatchDataPresenter} from "@/src/server/Gaming/application/ports/LoadMatchDataPresenter";
import {CatalogCardListRepository} from "@/src/server/DeckBuilding/application/ports/CatalogCardListRepository";

export class LoadMatchDataQueryHandler {
  private readonly matchRepository: MatchReadRepository;
  private readonly deckRepository: GameDeckReadRepository;
  private readonly catalogRepository: CatalogCardListRepository;

  constructor(matchRepository: MatchReadRepository, deckRepository: GameDeckReadRepository, catalogRepository: CatalogCardListRepository) {
    this.matchRepository = matchRepository;
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
  }

  async handle({matchId, userId}: LoadMatchDataQuery, presenter: LoadMatchDataPresenter) {
    try {
      const match = await this.matchRepository.findById(matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      const gameId = match.getGameId();
      const myId = userId;
      const opponentId = match.getOpponentOf(myId);

      const [myDeck, opponentDeck] = await Promise.all([
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, myId),
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, opponentId),
      ]);

      let myCardUrls: string[] = [];
      if (myDeck) {
        const myCatalogIds = myDeck.toSnapshot().cards.map(c => c.catalogCardId);
        try {
          const subset = await this.catalogRepository.getByIds(gameId, myCatalogIds);
          const map = new Map(
            subset.toSnapshot().cards.map(c => [c.id, c.image] as const)
          );
          myCardUrls = myCatalogIds
            .map(id => map.get(id))
            .filter((img): img is string => Boolean(img))
            .map(img => `/game-assets/cards/en/thumbnail/${img}`);
        } catch {
          const catalog = await this.catalogRepository.getByGameId(gameId);
          myCardUrls = myCatalogIds
            .map(id => catalog.findCardById(id))
            .filter((c): c is NonNullable<typeof c> => Boolean(c))
            .map(c => `/game-assets/cards/en/thumbnail/${c.image}`);
        }
      }
      const opponentCardCount = opponentDeck ? opponentDeck.toSnapshot().cards.length : 0;

      const currentPlayerPosition: 'player1' | 'player2' = match.getPlayers()[0] === myId ? 'player1' : 'player2';

      presenter.display(myCardUrls, opponentCardCount, {
        currentPlayerPosition,
        roleBased: {
          currentPlayer: { cardImageUrls: myCardUrls },
          opponent: { handCount: opponentCardCount },
        },
      });
    } catch (error) {
      presenter.displayError(error as Error);
    }
  }
}
