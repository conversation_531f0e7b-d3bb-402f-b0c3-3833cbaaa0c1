import {GetPlayerHandsQuery} from './GetPlayerHandsQuery';
import {MatchReadRepository} from '@/src/server/Gaming/application/ports/MatchReadRepository';
import {GameDeckReadRepository} from '@/src/server/Gaming/application/ports/GameDeckReadRepository';
import {GetPlayerHandsPresenter} from '@/src/server/Gaming/application/ports/GetPlayerHandsPresenter';
import {CatalogCardListRepository} from '@/src/server/DeckBuilding/application/ports/CatalogCardListRepository';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {Match} from '@/src/server/Gaming/domain/Match/Match';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';
import {GameSettings} from '@/src/server/Gaming/domain/GameSettings/GameSettings';
import {GameCardSnapshot} from '@/src/server/Gaming/domain/GameCard/GameCard';
import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';

interface PlacedCard {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: string;
}

export class GetPlayerHandsQueryHandler {
  private readonly matchRepository: MatchReadRepository;
  private readonly deckRepository: GameDeckReadRepository;
  private readonly catalogRepository: CatalogCardListRepository;
  private readonly gameSettingsRepository: GameSettingsRepository;
  private readonly mulliganRepository: MulliganSelectionRepository;

  constructor(
    matchRepository: MatchReadRepository,
    deckRepository: GameDeckReadRepository,
    catalogRepository: CatalogCardListRepository,
    gameSettingsRepository: GameSettingsRepository,
    mulliganRepository: MulliganSelectionRepository
  ) {
    this.matchRepository = matchRepository;
    this.deckRepository = deckRepository;
    this.catalogRepository = catalogRepository;
    this.gameSettingsRepository = gameSettingsRepository;
    this.mulliganRepository = mulliganRepository;
  }

  async handle(query: GetPlayerHandsQuery, presenter: GetPlayerHandsPresenter): Promise<void> {
    try {
      const match = await this.matchRepository.findById(query.matchId);
      if (!match) {
        presenter.displayError(new Error('Match not found'));
        return;
      }

      const gameId = match.getGameId();
      const opponentId = match.getOpponentOf(query.userId);

      const [myDeck, opponentDeck, gameSettings] = await Promise.all([
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, query.userId),
        this.deckRepository.findLatestByGameIdAndPlayerId(gameId, opponentId),
        this.gameSettingsRepository.getByGameId(gameId),
      ]);

      if (!myDeck || !gameSettings) {
        presenter.display({
          player1: [],
          player2: [],
          player1Count: 0,
          player2Count: 0,
        });
        return;
      }

      const finalHandCardIds = await this.calculateFinalHandCardIds(
        myDeck,
        query.matchId,
        query.userId,
        gameSettings,
        match
      );

      const myCards = await this.resolveCardDetails(finalHandCardIds, myDeck, gameId, gameSettings);
      const opponentCardCount = this.calculateOpponentCardCount(opponentDeck, match, query.userId, gameSettings);
      const currentPlayerPosition = this.determinePlayerPosition(match, query.userId);

      const result = this.formatResult(myCards, opponentCardCount, currentPlayerPosition);
      presenter.display(result);
    } catch (error) {
      presenter.displayError(error instanceof Error ? error : new Error('Unknown error'));
    }
  }

  private async calculateFinalHandCardIds(
    myDeck: GameDeck,
    matchId: string,
    userId: string,
    gameSettings: GameSettings,
    match: Match
  ): Promise<string[]> {
    const deckSnapshot = myDeck.toSnapshot();
    const startingHandSize = gameSettings.getStartingHandSize();
    const handCards = deckSnapshot.cards.slice(0, startingHandSize);
    let finalHandCardIds = handCards.map((c: GameCardSnapshot) => c.id);

    const mulliganSelections = await this.mulliganRepository.findAllByMatchIdAndPlayerId(matchId, userId);
    const maxRounds = gameSettings.getMulliganCount();
    const playerCurrentRound = mulliganSelections.length + 1;
    const hasCompletedAllRounds = playerCurrentRound > maxRounds;

    if (hasCompletedAllRounds) {
      finalHandCardIds = this.applyMulliganSelections(finalHandCardIds, mulliganSelections);
    }

    finalHandCardIds = this.removePlacedCards(finalHandCardIds, match, userId);

    return finalHandCardIds;
  }

  private applyMulliganSelections(handCardIds: string[], mulliganSelections: MulliganSelection[]): string[] {
    let finalHandCardIds = [...handCardIds];

    mulliganSelections
      .sort((a, b) => (a.getRound() || 0) - (b.getRound() || 0))
      .forEach(selection => {
        if (!selection.isSkipped()) {
          finalHandCardIds = finalHandCardIds.filter(cardId => 
            !selection.getSelectedCardIds().includes(cardId)
          );
        }
      });

    return finalHandCardIds;
  }

  private removePlacedCards(handCardIds: string[], match: Match, userId: string): string[] {
    const currentBoard = match.getBoardState();
    if (!currentBoard) {
      return handCardIds;
    }

    const currentPlayerPosition = this.determinePlayerPosition(match, userId);
    const myBoard = currentPlayerPosition === 'player1' 
      ? currentBoard.player1Board 
      : currentBoard.player2Board;

    const placedIds = new Set<string>();
    [...myBoard.firstRow, ...myBoard.secondRow].forEach((c: PlacedCard | null) => {
      if (c?.cardId) placedIds.add(c.cardId);
    });

    return handCardIds.filter(id => !placedIds.has(id));
  }

  private async resolveCardDetails(
    cardIds: string[], 
    myDeck: GameDeck, 
    gameId: string,
    gameSettings: GameSettings
  ): Promise<Array<{ instanceId: string; catalogCardId: string; image: string }>> {
    const deckSnapshot = myDeck.toSnapshot();
    const startingHandSize = gameSettings.getStartingHandSize();
    const handCards = deckSnapshot.cards.slice(0, startingHandSize);

    const catalogCardIds = Array.from(new Set(handCards.map((c: GameCardSnapshot) => c.catalogCardId)));
    
    try {
      const catalogSubset = await this.catalogRepository.getByIds(gameId, catalogCardIds);
      const catalogCards = catalogSubset.toSnapshot().cards;
      const catalogCardMap = new Map(
        catalogCards.map(card => [card.id, card])
      );

      return cardIds.map(cardId => {
        const card = handCards.find((c: GameCardSnapshot) => c.id === cardId);
        if (!card) return null;

        const catalogCard = catalogCardMap.get(card.catalogCardId);
        if (!catalogCard) return null;

        const customImageFromDeckData = typeof card.data?.image === 'string' ? card.data.image : undefined;
        const resolvedImage = customImageFromDeckData || catalogCard.image;

        return {
          instanceId: card.id,
          catalogCardId: card.catalogCardId,
          image: resolvedImage
        };
      }).filter((card): card is NonNullable<typeof card> => card !== null);
    } catch {
      const catalog = await this.catalogRepository.getByGameId(gameId);
      
      return cardIds.map(cardId => {
        const card = handCards.find((c: GameCardSnapshot) => c.id === cardId);
        if (!card) return null;

        const catalogCard = catalog.findCardById(card.catalogCardId);
        if (!catalogCard) return null;

        const customImageFromDeckData = typeof card.data?.image === 'string' ? card.data.image : undefined;
        const resolvedImage = customImageFromDeckData || catalogCard.image;

        return {
          instanceId: card.id,
          catalogCardId: card.catalogCardId,
          image: resolvedImage
        };
      }).filter((card): card is NonNullable<typeof card> => card !== null);
    }
  }

  private calculateOpponentCardCount(opponentDeck: GameDeck | null, match: Match, userId: string, gameSettings: GameSettings): number {
    if (!opponentDeck) {
      return 0;
    }

    const baseCount = gameSettings.getStartingHandSize();
    let placedByOpponent = 0;
    const board = match.getBoardState();
    
    if (board) {
      const currentPlayerPosition = this.determinePlayerPosition(match, userId);
      const opponentBoard = currentPlayerPosition === 'player1' 
        ? board.player2Board 
        : board.player1Board;
      
      placedByOpponent = [...opponentBoard.firstRow, ...opponentBoard.secondRow]
        .filter((c: PlacedCard | null) => c !== null).length;
    }

    return Math.max(0, baseCount - placedByOpponent);
  }

  private determinePlayerPosition(match: Match, userId: string): 'player1' | 'player2' {
    return match.getPlayers()[0] === userId ? 'player1' : 'player2';
  }

  private formatResult(
    myCards: Array<{ instanceId: string; catalogCardId: string; image: string }>,
    opponentCardCount: number,
    currentPlayerPosition: 'player1' | 'player2'
  ) {
    if (currentPlayerPosition === 'player1') {
      return {
        player1: myCards,
        player2: [],
        player1Count: myCards.length,
        player2Count: opponentCardCount,
      };
    }

    return {
      player1: [],
      player2: myCards,
      player1Count: opponentCardCount,
      player2Count: myCards.length,
    };
  }
}