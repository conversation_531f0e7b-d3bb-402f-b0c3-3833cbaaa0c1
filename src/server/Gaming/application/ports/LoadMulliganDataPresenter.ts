export type LoadMulliganDataResult = {
  currentRound: number;
  maxRounds: number;
  cardsInHand: Array<{
    id: string;
    catalogCardId: string;
    name: string;
    imageUrl: string;
  }>;
  selectedCardIds: string[];
  canSkip: boolean;
  timeLimit?: number;
  hasCompletedAllRounds: boolean;
};

export interface LoadMulliganDataPresenter {
  display(result: LoadMulliganDataResult): void;
  displayError(error: Error): void;
}
