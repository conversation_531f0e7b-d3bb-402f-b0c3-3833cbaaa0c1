import {Board} from "@/src/server/Gaming/domain/Board/Board";

export interface EnhancedPlacedCard {
  cardId: string;
  placedAt: string;
  slotIndex: number;
  rowType: 'first' | 'second';
  image: string;
}

export interface EnhancedPlayerBoard {
  firstRow: (EnhancedPlacedCard | null)[];
  secondRow: (EnhancedPlacedCard | null)[];
}

export interface EnhancedBoard {
  player1Board: EnhancedPlayerBoard;
  player2Board: EnhancedPlayerBoard;
}

export interface BoardEnhancementService {
  enhanceBoardWithImages(board: Board, matchId: string, playerIds: string[]): Promise<EnhancedBoard>;
}