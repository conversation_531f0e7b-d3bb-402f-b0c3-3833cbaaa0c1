export interface GetPlayerHandsResult {
  readonly player1: Array<{
    readonly instanceId: string;
    readonly catalogCardId: string;
    readonly image: string;
  }>;
  readonly player2: Array<{
    readonly instanceId: string;
    readonly catalogCardId: string;
    readonly image: string;
  }>;
  readonly player1Count: number;
  readonly player2Count: number;
}

export interface GetPlayerHandsPresenter {
  display(result: GetPlayerHandsResult): void;
  displayError(error: Error): void;
}