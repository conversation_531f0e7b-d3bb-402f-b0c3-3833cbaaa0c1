import {PassTurnCommand} from "./PassTurnCommand";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {MatchNotFoundError} from "@/src/server/Gaming/domain/Match/errors/MatchNotFoundError";
import {EventBus} from "@/src/server/Shared/application/ports/EventBus";

export class PassTurnCommandHandler {
  private readonly matchRepository: MatchRepository;
  private readonly eventBus: EventBus;

  constructor(matchRepository: MatchRepository, eventBus: EventBus) {
    this.matchRepository = matchRepository;
    this.eventBus = eventBus;
  }

  async handle({matchId, userId}: PassTurnCommand) {
    const match = await this.matchRepository.findById(matchId);
    if (!match) {
      throw new MatchNotFoundError();
    }

    const previousTurn = match.getCurrentTurn();
    match.passTurn(userId);
    const newTurn = match.getCurrentTurn();

    await this.matchRepository.save(match);

    await this.eventBus.dispatchMatchEvent(match.getGameId(), match.getId(), {
      type: "TurnPassed",
      payload: {
        fromPlayer: previousTurn!,
        toPlayer: newTurn!,
        matchId: match.getId(),
      },
    });
  }
}