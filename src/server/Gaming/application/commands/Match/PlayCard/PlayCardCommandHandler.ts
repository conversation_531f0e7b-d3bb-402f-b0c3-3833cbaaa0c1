import {PlayCardCommand} from "./PlayCardCommand";
import {MatchRepository} from "@/src/server/Gaming/application/ports/MatchRepository";
import {MatchNotFoundError} from "@/src/server/Gaming/domain/Match/errors/MatchNotFoundError";

export class PlayCardCommandHandler {
  private readonly matchRepository: MatchRepository;

  constructor(matchRepository: MatchRepository) {
    this.matchRepository = matchRepository;
  }

  async handle({matchId, cardId, rowType, slotIndex, userId}: PlayCardCommand) {
    const match = await this.matchRepository.findById(matchId);
    if (!match) {
      throw new MatchNotFoundError();
    }
    
    if (match.getStatus() !== 'active') {
      throw new Error('Cannot play card in non-active match');
    }

    if (!match.getPlayers().includes(userId)) {
      throw new Error('Player is not part of this match');
    }

    if (!match.getCurrentTurn()) {
      throw new Error('No current turn is set in this match');
    }

    if (match.getCurrentTurn() !== userId) {
      throw new Error('It is not your turn to play a card');
    }

    if (match.isCardAlreadyPlayed(cardId)) {
      throw new Error(`Card ${cardId} has already been played`);
    }

    match.placeCard(userId, cardId, rowType, slotIndex);

    await this.matchRepository.save(match);
  }
}
