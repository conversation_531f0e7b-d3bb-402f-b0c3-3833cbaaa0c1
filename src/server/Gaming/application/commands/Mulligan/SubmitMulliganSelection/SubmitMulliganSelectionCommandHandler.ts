import {SubmitMulliganSelectionCommand} from './SubmitMulliganSelectionCommand';
import {MatchRepository} from '@/src/server/Gaming/application/ports/MatchRepository';
import {GameDeckRepository} from '@/src/server/Gaming/application/ports/GameDeckRepository';
import {GameDeckReadRepository} from '@/src/server/Gaming/application/ports/GameDeckReadRepository';
import {MulliganSelectionRepository} from '@/src/server/Gaming/application/ports/MulliganSelectionRepository';
import {GameSettingsRepository} from '@/src/server/Gaming/application/ports/GameSettingsRepository';
import {MulliganSelection} from '@/src/server/Gaming/domain/Mulligan/MulliganSelection';
import {GameCard} from '@/src/server/Gaming/domain/GameCard/GameCard';
import {GameDeck} from '@/src/server/Gaming/domain/GameDeck/GameDeck';

export class SubmitMulliganSelectionCommandHandler {
  private readonly matchRepository: MatchRepository;
  private readonly deckRepository: GameDeckRepository;
  private readonly deckReadRepository: GameDeckReadRepository;
  private readonly mulliganRepository: MulliganSelectionRepository;
  private readonly gameSettingsRepository: GameSettingsRepository;

  constructor(
    matchRepository: MatchRepository,
    deckRepository: GameDeckRepository,
    deckReadRepository: GameDeckReadRepository,
    mulliganRepository: MulliganSelectionRepository,
    gameSettingsRepository: GameSettingsRepository
  ) {
    this.matchRepository = matchRepository;
    this.deckRepository = deckRepository;
    this.deckReadRepository = deckReadRepository;
    this.mulliganRepository = mulliganRepository;
    this.gameSettingsRepository = gameSettingsRepository;
  }

  async handle(command: SubmitMulliganSelectionCommand): Promise<void> {
    const match = await this.matchRepository.findById(command.matchId);
    if (!match) {
      throw new Error('Match not found');
    }

    if (!match.isWaitingForMulligan()) {
      throw new Error('Match is not in mulligan phase');
    }

    const gameSettings = await this.gameSettingsRepository.getByGameId(match.getGameId());
    if (!gameSettings) {
      throw new Error('Game settings not found');
    }

    const players = match.getPlayers();
    const effectivePlayerId =
      command.playerId ??
      (command.currentPlayerPosition
        ? (command.currentPlayerPosition === 'player1' ? players[0] : players[1])
        : undefined);

    if (!effectivePlayerId) {
      throw new Error('Player identification missing');
    }

    const playerPreviousSelections = await this.mulliganRepository.findAllByMatchIdAndPlayerId(command.matchId, effectivePlayerId);
    const expectedRound = playerPreviousSelections.length + 1;
    const maxRounds = gameSettings.getMulliganCount();

    if (command.round !== expectedRound) {
      throw new Error(`Invalid mulligan round. Expected ${expectedRound}, received ${command.round}`);
    }

    if (command.round > maxRounds) {
      throw new Error(`Player has already completed all ${maxRounds} mulligan rounds`);
    }

    const existingSelection = await this.mulliganRepository.findByMatchIdPlayerIdAndRound(
      command.matchId, 
      effectivePlayerId, 
      command.round
    );
    if (existingSelection) {
      throw new Error(`Player has already submitted mulligan selection for round ${command.round}`);
    }

    const selection = command.skipped 
      ? MulliganSelection.createSkipped(effectivePlayerId, command.matchId, command.round)
      : MulliganSelection.create({
          playerId: effectivePlayerId,
          matchId: command.matchId,
          selectedCardIds: command.selectedCardIds,
          skipped: false,
          round: command.round
        });

    await this.mulliganRepository.save(selection);

    if (!command.skipped) {
      await this.performMulligan(effectivePlayerId, match.getGameId(), command.selectedCardIds);
    }

    const allPlayersCompleted = await this.checkAllPlayersCompletedAllRounds(
      command.matchId, 
      match.getPlayers(), 
      maxRounds
    );

    if (allPlayersCompleted) {
      match.completeMulliganPhase();
      await this.matchRepository.save(match);
    }
  }

  private async performMulligan(playerId: string, gameId: string, selectedCardIds: string[]): Promise<void> {
    const deck = await this.deckReadRepository.findLatestByGameIdAndPlayerId(gameId, playerId);
    if (!deck) {
      throw new Error('Player deck not found');
    }

    const deckCards = deck.toSnapshot().cards;
    const remainingCards = deckCards.filter(card => !selectedCardIds.includes(card.id));
    const availableCards = deckCards.filter(card => selectedCardIds.includes(card.id));

    const shuffledAvailable = this.shuffleArray([...availableCards]);
    const newCards = shuffledAvailable.slice(0, selectedCardIds.length);

    const updatedCards = [...remainingCards, ...newCards];
    const updatedDeck = GameDeck.create({
      id: deck.toSnapshot().id,
      gameId,
      playerId,
      cards: updatedCards.map(c => GameCard.fromSnapshot(c))
    });

    await this.deckRepository.save(updatedDeck);
  }

  private shuffleArray<T>(array: T[]): T[] {
    const result = [...array];
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
  }

  private async checkAllPlayersCompletedAllRounds(matchId: string, players: string[], maxRounds: number): Promise<boolean> {
    for (const playerId of players) {
      const playerSelections = await this.mulliganRepository.findAllByMatchIdAndPlayerId(matchId, playerId);
      
      const hasReachedMaxRounds = playerSelections.length >= maxRounds;
      const hasSkippedEarly = playerSelections.some(s => s.isSkipped());
      
      if (!hasReachedMaxRounds && !hasSkippedEarly) {
        return false;
      }
    }
    return true;
  }

}
