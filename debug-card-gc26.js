/**
 * Debug script to investigate card gc26 desync issue
 * Run this in your browser console while on the gaming page
 */

console.log('🔍 Investigating card gc26 desync issue...');

// 1. Check client-side state
const checkClientState = () => {
  console.log('\n📱 CLIENT STATE:');
  
  // Check if Redux store is available
  if (window.__REDUX_DEVTOOLS_EXTENSION__) {
    console.log('Redux DevTools available - check Gaming state manually');
  }
  
  // Check local storage
  const localStorageKeys = Object.keys(localStorage);
  const relevantKeys = localStorageKeys.filter(key => 
    key.includes('match') || key.includes('gaming') || key.includes('card')
  );
  
  console.log('Relevant localStorage keys:', relevantKeys);
  relevantKeys.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      console.log(`${key}:`, JSON.parse(value));
    } catch (e) {
      console.log(`${key}:`, value);
    }
  });
};

// 2. Check current match ID from URL or context
const getCurrentMatchId = () => {
  const url = window.location.href;
  const matchIdMatch = url.match(/matches\/([^\/]+)/);
  if (matchIdMatch) {
    return matchIdMatch[1];
  }
  
  // Try to get from React context if available
  console.log('⚠️ Could not extract match ID from URL. Current URL:', url);
  return null;
};

// 3. Instructions for server-side debugging
const serverDebuggingInstructions = () => {
  console.log('\n🖥️ SERVER-SIDE DEBUGGING:');
  console.log('Run these queries in your Convex dashboard or dev environment:');
  
  const matchId = getCurrentMatchId();
  if (matchId) {
    console.log(`
// 1. Check match board state
ctx.db.get("${matchId}")

// 2. Check match events for card gc26
import { query } from "./_generated/server";
export default query(async (ctx) => {
  const matchId = "${matchId}";
  const events = await ctx.db.query('matchEvents')
    .withIndex('by_matchId', q => q.eq('matchId', matchId))
    .order('desc')
    .collect();
  
  const gc26Events = events.filter(event => 
    event.payload && JSON.stringify(event.payload).includes('gc26')
  );
  
  return { allEvents: events.slice(0, 10), gc26Events };
});

// 3. Check player hands
import { api } from "./_generated/api";
// Use existing getPlayerHands query with matchId: "${matchId}"
`);
  } else {
    console.log('❌ Match ID not found. Navigate to a match page first.');
  }
};

// 4. Check for common client-side issues
const checkClientIssues = () => {
  console.log('\n🐛 CHECKING CLIENT ISSUES:');
  
  // Check if multiple React roots exist
  const reactRoots = document.querySelectorAll('[data-reactroot]');
  if (reactRoots.length > 1) {
    console.warn('⚠️ Multiple React roots detected:', reactRoots.length);
  }
  
  // Check for JavaScript errors
  let errorCount = 0;
  const originalError = console.error;
  console.error = (...args) => {
    errorCount++;
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.log(`JavaScript errors in last 5 seconds: ${errorCount}`);
    console.error = originalError;
  }, 5000);
  
  // Check network requests
  const originalFetch = window.fetch;
  let pendingRequests = 0;
  
  window.fetch = async (...args) => {
    pendingRequests++;
    console.log(`🌐 Outgoing request: ${args[0]}`, { pending: pendingRequests });
    
    try {
      const response = await originalFetch(...args);
      pendingRequests--;
      
      if (args[0].includes('convex') && args[0].includes('gc26')) {
        console.log('🎯 Found gc26-related request:', args[0], response.status);
      }
      
      return response;
    } catch (error) {
      pendingRequests--;
      console.error('❌ Request failed:', args[0], error);
      throw error;
    }
  };
  
  // Restore after 30 seconds
  setTimeout(() => {
    window.fetch = originalFetch;
  }, 30000);
};

// Run all checks
checkClientState();
serverDebuggingInstructions();
checkClientIssues();

console.log('\n✅ Debug script complete. Check server-side using the queries above.');
console.log('💡 To check card gc26 specifically, look for:');
console.log('   - boardState containing gc26');
console.log('   - player hands containing gc26');
console.log('   - match events with gc26 in payload');